package services

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
)

// MigrateReportsSharedPresetIDs updates existing reports with shared preset IDs
func (dp *DataProcessor) MigrateReportsSharedPresetIDs() error {
	log.Println("Starting migration of reports to add shared preset IDs")

	// Get all presets
	presets, err := dp.GetPresets()
	if err != nil {
		return fmt.Errorf("error getting presets: %w", err)
	}

	// Create a map of preset IDs to shared IDs
	presetToSharedID := make(map[string]string)
	presetToVersion := make(map[string]int)

	// Also create a map of preset names to shared IDs for reports that only have preset name
	presetNameToSharedID := make(map[string]string)

	for _, preset := range presets {
		if preset.SharedID != "" {
			presetToSharedID[preset.ID] = preset.SharedID
			presetToVersion[preset.ID] = preset.Version

			// Map preset name to shared ID as well (for older reports)
			if preset.Name != "" {
				presetNameToSharedID[preset.Name] = preset.SharedID
			}
		}
	}

	// Get all reports
	reports, err := dp.GetReports()
	if err != nil {
		return fmt.Errorf("error getting reports: %w", err)
	}

	// Check if any reports need updating
	updatedCount := 0
	for i, report := range reports {
		// Case 1: Report has preset ID but no shared preset ID
		if report.PresetID != "" && report.SharedPresetID == "" {
			// Look up the shared ID for this preset
			if sharedID, ok := presetToSharedID[report.PresetID]; ok {
				reports[i].SharedPresetID = sharedID

				// Also set the preset version if not already set
				if reports[i].PresetVersion == 0 {
					if version, ok := presetToVersion[report.PresetID]; ok {
						reports[i].PresetVersion = version
					}
				}

				updatedCount++
				log.Printf("Updated report %s with shared preset ID %s from preset ID %s", report.ID, sharedID, report.PresetID)
			}
		}

		// Case 2: Report has preset name but no preset ID or shared preset ID
		if report.PresetID == "" && report.SharedPresetID == "" && report.PresetName != "" {
			// Look up the shared ID for this preset name
			if sharedID, ok := presetNameToSharedID[report.PresetName]; ok {
				reports[i].SharedPresetID = sharedID
				updatedCount++
				log.Printf("Updated report %s with shared preset ID %s from preset name %s", report.ID, sharedID, report.PresetName)
			}
		}
	}

	// If no reports were updated, we're done
	if updatedCount == 0 {
		log.Println("No reports needed migration for shared preset IDs")
		return nil
	}

	// Save the updated reports
	reportsFilePath := filepath.Join(dp.dataFolder, reportsFilename)
	data, err := json.MarshalIndent(reports, "", "    ")
	if err != nil {
		return fmt.Errorf("error marshaling reports: %w", err)
	}

	if err := os.WriteFile(reportsFilePath, data, 0644); err != nil {
		return fmt.Errorf("error writing reports file: %w", err)
	}

	// Invalidate cache - completely flush the cache to ensure all reports are reloaded
	dp.cache.Flush()
	log.Println("Cache completely flushed to ensure updated reports are loaded")

	log.Printf("Successfully migrated %d reports to add shared preset IDs", updatedCount)
	return nil
}
