import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { fileURLToPath } from 'url'

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      // Add explicit aliases for any paths that might be using @configs
      '@configs': path.resolve(__dirname, './src/configs'),
    },
  },
  server: {
    port: 5173,
    // Add proxy for backend API
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
      },
    },
    // Improve hot module replacement
    hmr: {
      overlay: true,
    },
  },
  // Ensure proper base path for assets
  base: '/',
  // Add build options to ensure proper path handling
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    // Ensure paths are normalized for cross-platform compatibility
    rollupOptions: {
      output: {
        manualChunks: undefined
      }
    }
  }
})
