import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle, CheckCircle, XCircle } from "lucide-react"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        // Determine icon based on variant
        const showIcon = true // Enable icons for all toasts
        let Icon = null
        
        if (props.variant === "destructive") {
          Icon = XCircle
        } else if (props.variant === "default" && 
                 (title?.includes("Sync") || 
                  title?.includes("Polling") || 
                  title?.includes("Repository") || 
                  title?.includes("GitLab"))) {
          Icon = CheckCircle
        } else if (title?.includes("Error")) {
          Icon = AlertCircle
        }
        
        return (
          <Toast key={id} {...props}>
            <div className="flex">
              {showIcon && Icon && (
                <div className="mr-2">
                  <Icon className={`h-5 w-5 ${props.variant === "destructive" ? "text-red-500" : "text-green-500"}`} />
                </div>
              )}
              <div className="flex-1">
                <ToastClose />
                {title && <ToastTitle>{title}</ToastTitle>}
                {description && (
                  <ToastDescription>{description}</ToastDescription>
                )}
              </div>
            </div>
            {action}
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
