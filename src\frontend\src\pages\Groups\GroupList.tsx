import { Group, PaginationInfo } from './GroupTypes';
import GroupListItem from './GroupListItem';

interface GroupListProps {
  loading: boolean;
  dataReady: boolean;
  filteredGroups: Group[];
  expandedRows: Record<string, boolean>;
  toggleRowExpansion: (groupName: string) => void;
  selectedRepoId: string | null;
  selectedLob: string;
  pagination: PaginationInfo;
  setInputValue: (value: string) => void;
  fetchGroupsData: (repoId: string, page: number, pageSize: number, lob?: string, search?: string) => void;
}

const GroupList = ({
  loading,
  dataReady,
  filteredGroups,
  expandedRows,
  toggleRowExpansion,
  selectedRepoId,
  selectedLob,
  pagination,
  setInputValue,
  fetchGroupsData
}: GroupListProps) => {
  if (loading && !dataReady) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 mx-auto border-4 border-primary border-t-transparent rounded-full"></div>
          <p className="mt-2 text-gray-500">Loading groups...</p>
        </div>
      </div>
    );
  }

  if (!dataReady || !filteredGroups || filteredGroups.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg w-full max-w-md">
          <p className="text-gray-500">No groups found</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {filteredGroups.map((group, index) => {
        const groupKey = `group-${index}-${group.Groupname || Math.random().toString(36).substring(2, 9)}`;
        
        return (
          <GroupListItem
            key={groupKey}
            group={group}
            groupKey={groupKey}
            expandedRows={expandedRows}
            toggleRowExpansion={toggleRowExpansion}
            selectedRepoId={selectedRepoId}
            selectedLob={selectedLob}
            pagination={pagination}
            setInputValue={setInputValue}
            fetchGroupsData={fetchGroupsData}
          />
        );
      })}
    </div>
  );
};

export default GroupList;
