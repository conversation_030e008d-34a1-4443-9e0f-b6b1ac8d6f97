import { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useRepository } from '@/context/RepositoryContext';
import { useFilters } from '@/components/layout/Layout';
import { apiClient, ApiError } from '@/api/client';

// Import components
import UserFilters from './UserFilters';
import UserList from './UserList';
import UserPagination from './UserPagination';

// Import types and utilities
import { User, PaginationInfo, UserRequestParams } from './UserTypes';
import { processUsers, extractLobsFromUsers } from './UserUtils';

// Global declaration for search timer
declare global {
  interface Window {
    searchTimer: ReturnType<typeof setTimeout> | null;
  }
}

const UsersPage = () => {
  // State for repository selection and data
  const { selectedRepoId, onRepositoryChange } = useRepository();
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [dataReady, setDataReady] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLob, setSelectedLob] = useState<string>('');
  const [lobs, setLobs] = useState<string[]>([]);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();

  // Track if component is mounted to prevent state updates after unmount
  const isMounted = useRef(true);

  // Single initialization ref to enforce only one fetch on page load
  const hasInitialized = useRef(false);

  // Page size options
  const pageSizeOptions = [10, 25, 50, 100];

  // Track the last successful API request to avoid redundant calls
  const lastRequest = useRef<UserRequestParams>({
    repoId: '',
    page: 0,
    pageSize: 0,
    lob: '',
    search: '',
  });

  // Track pagination state
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 50,
    totalItems: 0,
    totalPages: 0
  });

  // Reference to search input to maintain focus
  const searchInputRef = useRef<HTMLInputElement | null>(null);

  // Track if an API call is in progress to restore focus afterward
  const [isApiFetching, setIsApiFetching] = useState(false);

  // Toggle row expansion
  const toggleRowExpansion = (userName: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [userName]: !prev[userName]
    }));
  };

  // Handle page change - updates URL which triggers the main useEffect
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      // Update URL directly without using state that could trigger useEffect
      const params = new URLSearchParams(location.search);
      params.set('page', String(newPage));
      navigate(`${location.pathname}?${params.toString()}`, { replace: true });

      // Update internal pagination state directly
      setPagination(prev => ({...prev, page: newPage}));

      // Fetch data directly
      if (selectedRepoId) {
        fetchUsersData(selectedRepoId, newPage, pagination.pageSize, selectedLob, searchQuery);
      }
    }
  };

  // Handle page size change - updates URL which triggers the main useEffect
  const handlePageSizeChange = (newPageSize: number) => {
    // Update URL directly
    const params = new URLSearchParams(location.search);
    params.set('pageSize', String(newPageSize));
    params.set('page', '1'); // Reset to first page when changing page size
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });

    // Update internal pagination state directly
    setPagination(prev => ({...prev, pageSize: newPageSize, page: 1}));

    // Fetch data directly
    if (selectedRepoId) {
      fetchUsersData(selectedRepoId, 1, newPageSize, selectedLob, searchQuery);
    }
  };

  // Directly reload with fresh data
  const handleRefreshClick = () => {
    if (!selectedRepoId || loading) return;

    // Force refresh by resetting initialization flag
    hasInitialized.current = false;

    // Update URL to trigger a fetch with page 1
    const params = new URLSearchParams(location.search);
    // Keep the existing search query
    if (searchQuery) {
      params.set('query', searchQuery);
      params.set('search', searchQuery);
    } else {
      params.delete('query');
      params.delete('search');
    }
    if (selectedLob) {
      params.set('lob', selectedLob);
    } else {
      params.delete('lob');
    }
    params.set('page', '1');
    params.set('pageSize', pagination.pageSize.toString());

    // Use replace:true to avoid focus issues
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });

    // Fetch data directly
    fetchUsersData(selectedRepoId, 1, pagination.pageSize, selectedLob, searchQuery);
  };

  // Fetch users data
  const fetchUsersData = async (repoId: string, page: number, pageSize: number, lob?: string, search?: string) => {
    // Store the active element to check if search input has focus
    const activeElement = document.activeElement;
    const searchInputHasFocus = activeElement && activeElement.tagName === 'INPUT';

        try {

      // Show loading state if not already loading
      if (!loading) {
        setLoading(true);
        setDataReady(false);
      }

      // Only cancel previous requests if we're actually searching
      if (search) {
        // Cancel any previous requests for users search
        // This ensures only the latest search query is processed
        const endpoint = `/api/search/users`;
        const cancelCount = apiClient.utils.cancelRequestsByPattern(endpoint);
        if (cancelCount > 0) {
                  }
      }

      // Build the search query
      let query = search || "";

      // Add LOB filter if specified
      if (lob) {
        if (query) {
          query = `(${query}) AND lobs:${lob}`;
        } else {
          query = `lobs:${lob}`;
        }
      }


      // Get users data from the API using Bleve search
      const response = await apiClient.search.searchUsers(query, page, pageSize, repoId);

      // After API call, restore focus if needed
      if (searchInputHasFocus && searchInputRef.current) {
                searchInputRef.current.focus();
      }


      // Handle the Bleve search response format
      const userItems = response?.users || [];
      const paginationInfo = {
        page: response?.page || page,
        pageSize: response?.pageSize || pageSize,
        totalItems: response?.total || userItems.length,
        totalPages: response?.totalPages || Math.ceil(userItems.length / pageSize)
      };


      // Only update pagination if values are different to prevent infinite loops
      const shouldUpdatePagination =
        paginationInfo.page !== pagination.page ||
        paginationInfo.pageSize !== pagination.pageSize ||
        paginationInfo.totalItems !== pagination.totalItems ||
        paginationInfo.totalPages !== pagination.totalPages;

      if (shouldUpdatePagination) {
        setPagination(paginationInfo);
      }

      // For empty results, set empty state and return early
      if (userItems.length === 0) {
                setFilteredUsers([]);
        if (isMounted.current) {
          setDataReady(true);
          setLoading(false);
        }
        return;
      }

      // Process users
      const processedUsers = processUsers(userItems);

      // Extract LOBs for filter dropdown (only if not already populated)
      if (lobs.length === 0 && processedUsers.length > 0) {
        const extractedLobs = extractLobsFromUsers(processedUsers);
        setLobs(extractedLobs);
      }

      // Update users state
      setFilteredUsers(processedUsers);

      // Finish loading
      if (isMounted.current) {
        setDataReady(true);
        setLoading(false);
      }
    } catch (error) {
      // Check if this is a cancelled request
      if (error instanceof ApiError && error.status === -1) {
                // Don't update UI state for cancelled requests
        return;
      }

      console.error('Error fetching users data:', error);
      // Get the error message from the API response if available
      let errorMessage = "Failed to fetch users data";
      if (error instanceof ApiError && error.data && error.data.error) {
        errorMessage = error.data.error;
      }

      toast({
        title: "Search Error",
        description: errorMessage,
        variant: "destructive",
      });
      setFilteredUsers([]);
      setDataReady(true);
      setLoading(false);

      // After error, restore focus if needed
      if (searchInputHasFocus && searchInputRef.current) {
                searchInputRef.current.focus();
      }
    }
  };

  // Use a memoized UserFilters component to prevent unnecessary re-renders
  const MemoizedUserFilters = useCallback(() => {
    return (
      <UserFilters
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedLob={selectedLob}
        setSelectedLob={setSelectedLob}
        lobs={lobs}
        loading={loading}
        searchInputRef={searchInputRef}
        selectedRepoId={selectedRepoId}
        handleRefreshClick={handleRefreshClick}
        fetchUsersData={fetchUsersData}
        pagination={pagination}
        filteredUsers={filteredUsers}
      />
    );
  // Remove searchQuery from the dependency array to prevent re-renders when typing
  // Only re-render when these other dependencies change
  }, [selectedLob, lobs, loading, selectedRepoId, pagination, filteredUsers]);

  // Get access to the filters context
  const { setFilters } = useFilters();

  // Set the filters in the top header
  useEffect(() => {
    setFilters(<MemoizedUserFilters />);

    // Cleanup on unmount
    return () => setFilters(null);
  }, [setFilters, MemoizedUserFilters]);

  // Component cleanup
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Listen for repository changes and refresh data
  useEffect(() => {
    // Register a callback for repository changes
    const unsubscribe = onRepositoryChange((newRepoId, prevRepoId) => {
      if (newRepoId !== prevRepoId && newRepoId) {
        // Reset state for the new repository
        setLobs([]);
        setFilteredUsers([]);

        // Force a refresh by resetting the initialization flag
        hasInitialized.current = false;

        // Get current pagination and search parameters
        const params = new URLSearchParams(location.search);
        const page = parseInt(params.get('page') || '1', 10);
        const pageSize = parseInt(params.get('pageSize') || '50', 10);
        const searchValue = params.get('query') || params.get('search') || '';
        const lobValue = params.get('lob') || '';

        // Fetch data for the new repository
        fetchUsersData(newRepoId, page, pageSize, lobValue, searchValue);
      }
    });

    // Clean up the subscription when the component unmounts
    return unsubscribe;
  }, [onRepositoryChange, location.search]);

  // Single source of truth for data fetching - handles URL params, pagination, and data fetching
  useEffect(() => {
    // Only proceed if component is mounted
    if (!isMounted.current) return;

    // Parse URL parameters
    const params = new URLSearchParams(location.search);

    // Get search query - check both 'query' and 'search' parameters
    const queryParam = params.get('query');
    const searchParam = params.get('search');
    const effectiveSearchParam = queryParam || searchParam;

    if (effectiveSearchParam) {
            setSearchQuery(effectiveSearchParam);

      // Also update the search input value directly
      if (searchInputRef.current) {
        searchInputRef.current.value = effectiveSearchParam;
      }
    } else {
            setSearchQuery('');
    }

    // Get pagination parameters
    let page = 1;
    let pageSize = 50;

    const pageParam = params.get('page');
    if (pageParam) {
      const parsedPage = parseInt(pageParam, 10);
      if (!isNaN(parsedPage) && parsedPage > 0) {
        page = parsedPage;
      }
    }

    const pageSizeParam = params.get('pageSize');
    if (pageSizeParam) {
      const parsedPageSize = parseInt(pageSizeParam, 10);
      if (!isNaN(parsedPageSize) && pageSizeOptions.includes(parsedPageSize)) {
        pageSize = parsedPageSize;
      }
    }

    // Update pagination state without triggering a re-render if values are the same
    const paginationChanged =
      pagination.page !== page ||
      pagination.pageSize !== pageSize;

    if (paginationChanged) {
      setPagination(prev => ({
        ...prev,
        page,
        pageSize
      }));
    }

    // Skip if we don't have a repository ID
    if (!selectedRepoId) {
      if (loading) setLoading(false);
      return;
    }

    // Check if this is exactly the same request as last time
    const currentRequest = {
      repoId: selectedRepoId,
      page,
      pageSize,
      lob: selectedLob,
      search: effectiveSearchParam || ''
    };

    // Skip duplicate requests unless forced by hasInitialized being false
    if (hasInitialized.current &&
        currentRequest.repoId === lastRequest.current.repoId &&
        currentRequest.page === lastRequest.current.page &&
        currentRequest.pageSize === lastRequest.current.pageSize &&
        currentRequest.lob === lastRequest.current.lob &&
        currentRequest.search === lastRequest.current.search) {
            return;
    }

    // Now we're going to make a real API request - update our state
    lastRequest.current = { ...currentRequest };
    hasInitialized.current = true;

    // Show loading state
    setLoading(true);
    setDataReady(false);

    // Always fetch data when the page loads or URL changes
    const searchValue = effectiveSearchParam || '';
    const lobValue = params.get('lob') || '';

    // Update selectedLob if it's different from the URL parameter
    if (lobValue !== selectedLob) {
      setSelectedLob(lobValue);
    }

    fetchUsersData(selectedRepoId, page, pageSize, lobValue, searchValue);

  }, [location.search, selectedRepoId]);

  // Maintain focus after API calls
  useEffect(() => {
        // When loading changes from true to false, it means API call completed
    if (!loading && isApiFetching) {
            setIsApiFetching(false);
      // Return focus to input after a small delay to ensure DOM is ready
      setTimeout(() => {
        if (searchInputRef.current) {
                    searchInputRef.current.focus();
        } else {
                  }
      }, 50);
    }
  }, [loading, isApiFetching]);

  return (
    <div className="flex flex-col h-full">
      {/* Column Headers */}
      <div className="flex-none border-b bg-gray-50 dark:bg-gray-800 rounded-t-md">
        <div className="grid grid-cols-2 gap-4 py-3 px-4">
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">User</div>
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">LOB</div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-grow overflow-auto bg-white dark:bg-gray-900 rounded-b-md">
        <UserList
          loading={loading}
          dataReady={dataReady}
          filteredUsers={filteredUsers}
          expandedRows={expandedRows}
          toggleRowExpansion={toggleRowExpansion}
          setSearchQuery={setSearchQuery}
        />
      </div>

      {/* Fixed Table Controls */}
      <UserPagination
        pagination={pagination}
        loading={loading}
        pageSizeOptions={pageSizeOptions}
        handlePageChange={handlePageChange}
        handlePageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};

export default UsersPage;
