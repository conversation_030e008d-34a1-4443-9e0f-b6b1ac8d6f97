import { useState, useEffect } from "react";
import {
  Refresh<PERSON><PERSON>,
  GitBranch,
  CheckCircle,
  AlertCircle,
  Server,
  FileText,
  Clock,
  Play,
  Pause,
} from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { apiClient, RepositoryStatus, RepositoryType } from "@/api/client";
import { useRepository } from "@/context/RepositoryContext";
import { Badge } from "@/components/ui/badge";
import SyncDetailsModal from "./SyncDetailsModal";

interface RepositoryStatusCardProps {
  repoId?: string; // Optional: If provided, will override the context repository
}

const RepositoryStatusCard = ({ repoId }: RepositoryStatusCardProps = {}) => {
  const [status, setStatus] = useState<RepositoryStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [repoName, setRepoName] = useState<string>("");
  const [repoType, setRepoType] = useState<RepositoryType>("gitlab");
  const [showSyncModal, setShowSyncModal] = useState(false);
  const [isPolling, setIsPolling] = useState(false);
  const [pollFrequency, setPollFrequency] = useState(300); // Default 5 minutes
  const [togglePollingLoading, setTogglePollingLoading] = useState(false);
  const {
    selectedRepoId,
    isLoading: contextLoading,
    repositories,
  } = useRepository();
  const { toast } = useToast();

  // Use provided repoId or fall back to selected repo from context
  const currentRepoId = repoId || selectedRepoId;

  // Function to fetch repository information
  const fetchRepositoryInfo = async () => {
    if (!currentRepoId) {
      return;
    }

    // First check if the repository is already in our context
    const repoFromContext = repositories.find(
      (repo) => repo.id === currentRepoId
    );
    if (repoFromContext) {
      setRepoName(repoFromContext.name);

      // Get the full repository configuration to get polling frequency
      try {
        const { config } = await apiClient.repositories.getConfiguration(
          currentRepoId
        );
        if (config && config.pollFrequency) {
          setPollFrequency(config.pollFrequency);
        }

        // Set polling status based on isActive flag
        setIsPolling(config.isActive || false);
      } catch (error) {
        console.error("Error fetching repository configuration:", error);
        // Fallback to context data
        setIsPolling(repoFromContext.isActive || false);
      }

      // FORCE the repository type to match what's in the context
      if (repoFromContext.type === "bitbucket") {
        setRepoType("bitbucket");
      } else {
        setRepoType(repoFromContext.type || "gitlab");
      }
      return;
    }

    // If not found in context, fetch from API
    try {
      // Get the full repository configuration
      try {
        const { config } = await apiClient.repositories.getConfiguration(
          currentRepoId
        );
        if (config) {
          setRepoName(config.name || "");

          // Set polling frequency if available
          if (config.pollFrequency) {
            setPollFrequency(config.pollFrequency);
          }

          // Set polling status based on isActive flag
          setIsPolling(config.isActive || false);

          // FORCE the repository type based on configuration
          if (
            config.type === "bitbucket" ||
            config.bitbucketWorkspace ||
            config.bitbucketRepoSlug
          ) {
            setRepoType("bitbucket");
          } else {
            setRepoType("gitlab");
          }
        }
      } catch (configError) {
        console.error(
          "Error fetching specific repository configuration:",
          configError
        );

        // Fallback to getting all configurations
        const { configs } = await apiClient.repositories.getConfigurations();

        const repo = configs.find((config) => config.id === currentRepoId);
        if (repo) {
          setRepoName(repo.name);

          // Set polling status based on isActive flag
          setIsPolling(repo.isActive || false);

          // FORCE the repository type based on configuration
          if (
            repo.type === "bitbucket" ||
            repo.bitbucketWorkspace ||
            repo.bitbucketRepoSlug
          ) {
            setRepoType("bitbucket");
          } else {
            setRepoType("gitlab");
          }
        } else {
        }
      }
    } catch (error) {
      // Just log the error, non-critical operation
      console.error(
        "fetchRepositoryInfo: Error fetching repository name:",
        error
      );
    }
  };

  // Function to fetch repository status
  const fetchStatus = async () => {
    if (!currentRepoId) {
      setStatus(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      // Use the type-agnostic API endpoint
      const response = await apiClient.repositories.getRepositoryStatus(
        currentRepoId
      );

      // The API response can be in different formats depending on the endpoint
      if (response) {
        // Check if the response has the expected properties
        if (response.status) {
          // Format: { status: {...} }
          setStatus(response.status);
        } else if ("lastSync" in response || "LastSync" in response) {
          // Format: directly the status object

          // Convert property names to the expected format
          // Use type assertion to avoid TypeScript errors
          const responseAny = response as any;
          const formattedStatus: RepositoryStatus = {
            lastSync: responseAny.lastSync || responseAny.LastSync || "",
            lastCommit: responseAny.lastCommit || responseAny.LastCommit || "",
            hasChanges:
              responseAny.hasChanges || responseAny.HasChanges || false,
            syncInProgress:
              responseAny.syncInProgress || responseAny.SyncInProgress || false,
          };

          setStatus(formattedStatus);
        } else {
          console.error("Invalid repository status response format:", response);
          setStatus(null);
        }
      } else {
        console.error("Empty repository status response");
        setStatus(null);
      }
    } catch (error) {
      toast({
        title: "Failed to load repository status",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
      console.error("Error fetching repository status:", error);

      // Try to read the LAST_SYNC and COMMIT_HASH files directly as a fallback
      try {
        const fallbackStatus: RepositoryStatus = {
          lastSync: new Date().toISOString(),
          lastCommit: "Unknown",
          hasChanges: false,
          syncInProgress: false,
        };

        setStatus(fallbackStatus);
      } catch (fallbackError) {
        console.error("Failed to create fallback status:", fallbackError);
        setStatus(null);
      }
    } finally {
      setLoading(false);
    }
  };

  // Toggle polling for the repository
  const togglePolling = async () => {
    if (!currentRepoId || togglePollingLoading) {
      return;
    }

    setTogglePollingLoading(true);
    try {
      if (isPolling) {
        // Stop polling
        await apiClient.repositories.stopPolling(currentRepoId);
        setIsPolling(false);
        toast({
          title: "Polling Stopped",
          description: "Repository polling has been stopped",
        });
      } else {
        // Start polling
        await apiClient.repositories.startPolling(currentRepoId);
        setIsPolling(true);
        toast({
          title: "Polling Started",
          description: "Repository polling has been started",
        });
      }

      // Refresh repository info to get updated polling status
      fetchRepositoryInfo();
    } catch (error) {
      toast({
        title: isPolling ? "Failed to Stop Polling" : "Failed to Start Polling",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    } finally {
      setTogglePollingLoading(false);
    }
  };

  // Trigger sync operation on the repository
  const handleSync = async () => {
    if (!currentRepoId || syncing) {
      return;
    }

    setSyncing(true);
    try {
      // Use the type-agnostic API endpoint
      await apiClient.repositories.syncRepository(currentRepoId);

      toast({
        title: "Sync Initiated",
        description: "Repository synchronization has been initiated",
      });

      // Start polling for status updates
      const pollInterval = setInterval(async () => {
        try {
          // Use the type-agnostic API endpoint
          const response = await apiClient.repositories.getRepositoryStatus(
            currentRepoId
          );

          // Extract status from response
          const statusData = response.status || response;
          // Handle both camelCase and PascalCase property names
          const syncInProgress = statusData.syncInProgress;

          // If sync is no longer in progress, stop polling
          if (!syncInProgress) {
            clearInterval(pollInterval);
            setSyncing(false);
            fetchStatus(); // Fetch final status

            toast({
              title: "Sync Completed",
              description: "Repository synchronization has completed",
            });
          }
        } catch (error) {
          console.error("Error polling repository status:", error);
        }
      }, 2000);

      // Set a timeout to stop polling after 30 seconds to prevent infinite polling
      setTimeout(() => {
        clearInterval(pollInterval);
        setSyncing(false);
        fetchStatus();
      }, 30000);
    } catch (error) {
      toast({
        title: "Sync Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
      setSyncing(false);
    }
  };

  // Effect to fetch status when the repository changes or on manual refresh
  useEffect(() => {
    fetchRepositoryInfo();
    fetchStatus();
  }, [currentRepoId]);

  // Add a separate effect to log when repoType changes
  useEffect(() => {
  }, [repoType]);

  // Render loading state if context is still loading or there's no repository selected
  if (contextLoading || (!repoId && !selectedRepoId)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </CardTitle>
          {/* Replace CardDescription with div to avoid invalid nesting */}
          <div className="text-sm text-muted-foreground mt-1.5">
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
        <CardFooter>
          <Skeleton className="h-8 w-20" />
        </CardFooter>
      </Card>
    );
  }

  // Render the repository type icon based on the repository type
  const renderRepositoryTypeIcon = () => {
    // Check if the current repository has Bitbucket properties
    const currentRepo = repositories.find((repo) => repo.id === currentRepoId);
    if (currentRepo) {
      // If the repository has Bitbucket properties, force the icon to show Bitbucket
      if (currentRepo.type === "bitbucket") {
        return <Server className="h-6 w-6 text-blue-500" />;
      }
    }

    // Use the repoType state as a fallback
    if (repoType === "bitbucket") {
      return <Server className="h-6 w-6 text-blue-500" />;
    }

    return <GitBranch className="h-6 w-6 text-orange-500" />;
  };

  // Render repository type badge
  const renderRepositoryTypeBadge = () => {
    // Check if the current repository has Bitbucket properties
    const currentRepo = repositories.find((repo) => repo.id === currentRepoId);
    if (currentRepo) {
      // If the repository has Bitbucket properties, force the badge to show Bitbucket
      if (currentRepo.type === "bitbucket") {
        return <Badge variant="secondary">Bitbucket</Badge>;
      }
    }

    // Use the repoType state as a fallback
    if (repoType === "bitbucket") {
      return <Badge variant="secondary">Bitbucket</Badge>;
    }

    return <Badge variant="default">GitLab</Badge>;
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              {renderRepositoryTypeIcon()}
              <span>{repoName || "Repository"}</span>
              {renderRepositoryTypeBadge()}
            </div>
            <Button
              size="icon"
              variant="ghost"
              onClick={fetchStatus}
              disabled={loading || syncing}
              title="Refresh Status"
            >
              <RefreshCw
                className={`h-4 w-4 ${
                  loading || syncing ? "animate-spin" : ""
                }`}
              />
            </Button>
          </CardTitle>
          <div className="text-sm text-muted-foreground mt-1.5">
            Repository status and synchronization
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ) : status ? (
            <div className="grid gap-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Last Sync:
                </span>
                <span className="text-sm font-medium">
                  {status.lastSync
                    ? (() => {
                        try {
                          // Try to parse the date and format it
                          return new Date(status.lastSync).toLocaleString();
                        } catch (e) {
                          console.error("Failed to parse last sync date:", e);
                          return status.lastSync;
                        }
                      })()
                    : "Never"}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Last Commit:
                </span>
                <span className="text-sm font-medium">
                  {status.lastCommit
                    ? status.lastCommit.length > 8
                      ? status.lastCommit.substring(0, 8)
                      : status.lastCommit
                    : "None"}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Status:</span>
                <div className="flex items-center">
                  {status.hasChanges ? (
                    <>
                      <AlertCircle className="h-4 w-4 text-amber-500 mr-1" />
                      <span className="text-sm font-medium text-amber-500">
                        Changes Available
                      </span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm font-medium text-green-500">
                        Synchronized
                      </span>
                    </>
                  )}
                </div>
              </div>
              {status.syncInProgress && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Sync Status:
                  </span>
                  <div className="flex items-center">
                    <RefreshCw className="h-4 w-4 text-blue-500 mr-1 animate-spin" />
                    <span className="text-sm font-medium text-blue-500">
                      Sync in Progress
                    </span>
                  </div>
                </div>
              )}

              {/* Polling Information */}
              <div className="flex justify-between items-center mt-2">
                <span className="text-sm text-muted-foreground">Polling:</span>
                <div className="flex items-center">
                  {isPolling ? (
                    <>
                      <Clock className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm font-medium text-green-500">
                        Active ({pollFrequency}s)
                      </span>
                    </>
                  ) : (
                    <>
                      <Clock className="h-4 w-4 text-gray-500 mr-1" />
                      <span className="text-sm font-medium text-gray-500">
                        Inactive
                      </span>
                    </>
                  )}
                </div>
              </div>

              {/* Toggle Polling Button */}
              <Button
                variant="outline"
                size="sm"
                className="mt-2 w-full flex items-center justify-center"
                onClick={togglePolling}
                disabled={togglePollingLoading}
              >
                {togglePollingLoading && (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                )}
                {!togglePollingLoading &&
                  (isPolling ? (
                    <Pause className="h-4 w-4 mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  ))}
                {isPolling ? "Stop Polling" : "Start Polling"}
              </Button>

              {/* View Sync Details Button */}
              <Button
                variant="ghost"
                size="sm"
                className="mt-2 w-full flex items-center justify-center"
                onClick={() => setShowSyncModal(true)}
              >
                <FileText className="h-4 w-4 mr-2" />
                View Sync Details
              </Button>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-muted-foreground">
                No repository status available
              </p>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleSync}
            disabled={loading || syncing || !currentRepoId}
            className="w-full"
          >
            {syncing && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
            Sync Repository
          </Button>
        </CardFooter>
      </Card>

      {/* Sync Details Modal */}
      <SyncDetailsModal
        open={showSyncModal}
        onOpenChange={setShowSyncModal}
        repoId={currentRepoId || ""}
        repoName={repoName || "Repository"}
      />
    </>
  );
};

export default RepositoryStatusCard;
