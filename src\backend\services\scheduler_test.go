package services

import (
	"encoding/json"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"adgitops-ui/src/backend/models"
)

// MockDataProcessor is a mock implementation of the DataProcessor for testing
type MockDataProcessor struct {
	mock.Mock
}

func (m *MockDataProcessor) SavePreset(preset models.ReportPreset) (models.ReportPreset, error) {
	args := m.Called(preset)
	if fn, ok := args.Get(0).(func(models.ReportPreset) models.ReportPreset); ok {
		return fn(preset), args.Error(1)
	}
	return args.Get(0).(models.ReportPreset), args.Error(1)
}

func (m *MockDataProcessor) UpdatePresetInPlace(preset models.ReportPreset) (models.ReportPreset, error) {
	args := m.Called(preset)
	if fn, ok := args.Get(0).(func(models.ReportPreset) models.ReportPreset); ok {
		return fn(preset), args.Error(1)
	}
	return args.Get(0).(models.ReportPreset), args.Error(1)
}

func (m *MockDataProcessor) GetPresets() ([]models.ReportPreset, error) {
	args := m.Called()
	return args.Get(0).([]models.ReportPreset), args.Error(1)
}

func (m *MockDataProcessor) GetPreset(id string) (models.ReportPreset, error) {
	args := m.Called(id)
	return args.Get(0).(models.ReportPreset), args.Error(1)
}

func (m *MockDataProcessor) ParseJSONFiles(repoPath string) ([]models.Group, error) {
	args := m.Called(repoPath)
	return args.Get(0).([]models.Group), args.Error(1)
}

func (m *MockDataProcessor) GenerateReport(groups []models.Group, reportType string, query models.QueryParams, presetID string, presetName string) (models.Report, error) {
	args := m.Called(groups, reportType, query, presetID, presetName)
	return args.Get(0).(models.Report), args.Error(1)
}

func (m *MockDataProcessor) saveReportMetadata(report models.Report) error {
	args := m.Called(report)
	return args.Error(0)
}

// MockRepoInstance is a mock implementation of the RepoInstance interface
type MockRepoInstance struct {
	mock.Mock
}

func (m *MockRepoInstance) GetID() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockRepoInstance) GetLocalRepoPath() string {
	args := m.Called()
	return args.String(0)
}

// MockRepoManager is a mock implementation of the repository manager
type MockRepoManager struct {
	mock.Mock
}

func (m *MockRepoManager) GetRepoInstances() ([]RepoInstance, error) {
	args := m.Called()
	return args.Get(0).([]RepoInstance), args.Error(1)
}

// setupTestSchedulerEnvironment creates a test environment for scheduler testing
func setupTestSchedulerEnvironment(t *testing.T) (*MockDataProcessor, *MockRepoManager, string, func()) {
	// Create a temporary test directory
	tempDir, err := os.MkdirTemp("", "scheduler_test")
	assert.NoError(t, err)

	// Create mock data processor
	mockDP := new(MockDataProcessor)

	// Create mock repo manager
	mockRM := new(MockRepoManager)

	// Return cleanup function
	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return mockDP, mockRM, tempDir, cleanup
}

// TestSchedulerService_Start tests the Start method of the scheduler service
func TestSchedulerService_Start(t *testing.T) {
	mockDP, mockRM, tempDir, cleanup := setupTestSchedulerEnvironment(t)
	defer cleanup()

	// Mock GetPresets to return empty slice
	mockDP.On("GetPresets").Return([]models.ReportPreset{}, nil)

	// Create scheduler service
	scheduler := NewSchedulerService(mockDP, mockRM, tempDir)

	// Start the scheduler
	err := scheduler.Start()
	assert.NoError(t, err)
	assert.True(t, scheduler.isRunning)

	// Stop the scheduler
	scheduler.Stop()
	assert.False(t, scheduler.isRunning)
}

// TestSchedulerService_CalculateNextRun tests the calculateNextRun method
func TestSchedulerService_CalculateNextRun(t *testing.T) {
	mockDP, mockRM, tempDir, cleanup := setupTestSchedulerEnvironment(t)
	defer cleanup()

	// Create scheduler service
	scheduler := NewSchedulerService(mockDP, mockRM, tempDir)

	// Test daily schedule
	now := time.Date(2023, 1, 1, 10, 0, 0, 0, time.Local)
	schedule := models.ScheduleConfig{
		Enabled:   true,
		Frequency: "daily",
		Hour:      12,
		Minute:    0,
	}

	// If current time is before scheduled time, next run should be today
	next := scheduler.calculateNextRun(schedule, now)
	expected := time.Date(2023, 1, 1, 12, 0, 0, 0, time.Local)
	assert.Equal(t, expected, next)

	// If current time is after scheduled time, next run should be tomorrow
	now = time.Date(2023, 1, 1, 14, 0, 0, 0, time.Local)
	next = scheduler.calculateNextRun(schedule, now)
	expected = time.Date(2023, 1, 2, 12, 0, 0, 0, time.Local)
	assert.Equal(t, expected, next)

	// Test weekly schedule
	schedule = models.ScheduleConfig{
		Enabled:   true,
		Frequency: "weekly",
		DayOfWeek: 3, // Wednesday (0 = Sunday)
		Hour:      12,
		Minute:    0,
	}

	// If current day is before scheduled day, next run should be this week
	now = time.Date(2023, 1, 2, 10, 0, 0, 0, time.Local) // Monday
	next = scheduler.calculateNextRun(schedule, now)
	expected = time.Date(2023, 1, 4, 12, 0, 0, 0, time.Local) // Wednesday
	assert.Equal(t, expected, next)

	// If current day is after scheduled day, next run should be next week
	now = time.Date(2023, 1, 5, 10, 0, 0, 0, time.Local) // Thursday
	next = scheduler.calculateNextRun(schedule, now)
	expected = time.Date(2023, 1, 11, 12, 0, 0, 0, time.Local) // Next Wednesday
	assert.Equal(t, expected, next)

	// Test hourly schedule
	schedule = models.ScheduleConfig{
		Enabled:   true,
		Frequency: "hourly",
		Minute:    30,
	}

	// If current minute is before scheduled minute, next run should be this hour
	now = time.Date(2023, 1, 1, 10, 15, 0, 0, time.Local)
	next = scheduler.calculateNextRun(schedule, now)
	expected = time.Date(2023, 1, 1, 10, 30, 0, 0, time.Local)
	assert.Equal(t, expected, next)

	// If current minute is after scheduled minute, next run should be next hour
	now = time.Date(2023, 1, 1, 10, 45, 0, 0, time.Local)
	next = scheduler.calculateNextRun(schedule, now)
	expected = time.Date(2023, 1, 1, 11, 30, 0, 0, time.Local)
	assert.Equal(t, expected, next)

	// Test monthly schedule
	schedule = models.ScheduleConfig{
		Enabled:    true,
		Frequency:  "monthly",
		DayOfMonth: 15,
		Hour:       12,
		Minute:     0,
	}

	// If current day is before scheduled day, next run should be this month
	now = time.Date(2023, 1, 10, 10, 0, 0, 0, time.Local)
	next = scheduler.calculateNextRun(schedule, now)
	expected = time.Date(2023, 1, 15, 12, 0, 0, 0, time.Local)
	assert.Equal(t, expected, next)

	// If current day is after scheduled day, next run should be next month
	now = time.Date(2023, 1, 20, 10, 0, 0, 0, time.Local)
	next = scheduler.calculateNextRun(schedule, now)
	expected = time.Date(2023, 2, 15, 12, 0, 0, 0, time.Local)
	assert.Equal(t, expected, next)

	// Test edge case: day of month that doesn't exist in some months (e.g., 31)
	schedule = models.ScheduleConfig{
		Enabled:    true,
		Frequency:  "monthly",
		DayOfMonth: 31,
		Hour:       12,
		Minute:     0,
	}

	// February doesn't have 31 days, so it should adjust to the last day
	now = time.Date(2023, 1, 31, 10, 0, 0, 0, time.Local)
	next = scheduler.calculateNextRun(schedule, now)
	expected = time.Date(2023, 2, 28, 12, 0, 0, 0, time.Local)
	assert.Equal(t, expected, next)
}

// TestSchedulerService_CheckSchedules tests the checkSchedules method
func TestSchedulerService_CheckSchedules(t *testing.T) {
	mockDP, mockRM, tempDir, cleanup := setupTestSchedulerEnvironment(t)
	defer cleanup()

	// Create scheduler service
	scheduler := NewSchedulerService(mockDP, mockRM, tempDir)

	// Create test presets
	now := time.Now()
	pastTime := now.Add(-1 * time.Hour).Format(time.RFC3339)
	futureTime := now.Add(1 * time.Hour).Format(time.RFC3339)

	presets := []models.ReportPreset{
		{
			ID:         "preset1",
			Name:       "Preset 1",
			ReportType: "groups",
			IsActive:   true,
			Schedule: models.ScheduleConfig{
				Enabled:   true,
				NextRun:   pastTime, // Due to run
				Hour:      now.Hour(),
				Minute:    now.Minute(),
				Frequency: "daily",
			},
		},
		{
			ID:         "preset2",
			Name:       "Preset 2",
			ReportType: "users",
			IsActive:   true,
			Schedule: models.ScheduleConfig{
				Enabled:   true,
				NextRun:   futureTime, // Not due to run
				Hour:      now.Hour(),
				Minute:    now.Minute(),
				Frequency: "daily",
			},
		},
		{
			ID:         "preset3",
			Name:       "Preset 3",
			ReportType: "both",
			IsActive:   false, // Inactive preset
			Schedule: models.ScheduleConfig{
				Enabled:   true,
				NextRun:   pastTime,
				Hour:      now.Hour(),
				Minute:    now.Minute(),
				Frequency: "daily",
			},
		},
		{
			ID:         "preset4",
			Name:       "Preset 4",
			ReportType: "groups",
			IsActive:   true,
			Schedule: models.ScheduleConfig{
				Enabled:   false, // Disabled schedule
				NextRun:   pastTime,
				Hour:      now.Hour(),
				Minute:    now.Minute(),
				Frequency: "daily",
			},
		},
	}

	// Mock GetPresets to return our test presets
	mockDP.On("GetPresets").Return(presets, nil)

	// Mock SavePreset to return the preset with updated NextRun
	mockDP.On("SavePreset", mock.AnythingOfType("models.ReportPreset")).Return(models.ReportPreset{}, nil)

	// Create mock repositories
	mockRepo := new(MockRepoInstance)
	mockRepo.On("GetID").Return("repo1")
	mockRepo.On("GetLocalRepoPath").Return(filepath.Join(tempDir, "repo1"))

	// Mock GetRepoInstances to return our mock repo
	mockRM.On("GetRepoInstances").Return([]RepoInstance{mockRepo}, nil)

	// Mock ParseJSONFiles to return test groups
	testGroups := []models.Group{
		{
			Groupname: "group1",
			Lob:       "Marketing",
			Type:      "security",
		},
	}
	mockDP.On("ParseJSONFiles", filepath.Join(tempDir, "repo1")).Return(testGroups, nil)

	// Mock GenerateReport to return a test report
	testReport := models.Report{
		ID:          "report1",
		PresetID:    "preset1",
		PresetName:  "Preset 1",
		Filename:    "report1.json",
		Type:        "groups",
		CreatedAt:   time.Now().Format(time.RFC3339),
		DownloadURL: "/api/data/reports/download/report1.json",
	}
	mockDP.On("GenerateReport", testGroups, "groups", presets[0].Query, "preset1", "Preset 1").Return(testReport, nil)

	// Mock saveReportMetadata
	mockDP.On("saveReportMetadata", mock.AnythingOfType("models.Report")).Return(nil)

	// Run checkSchedules
	scheduler.checkSchedules()

	// Verify that SavePreset was called for preset1 (due to run)
	mockDP.AssertCalled(t, "SavePreset", mock.MatchedBy(func(preset models.ReportPreset) bool {
		return preset.ID == "preset1"
	}))

	// Verify that GenerateReport was called for preset1
	mockDP.AssertCalled(t, "GenerateReport", testGroups, "groups", presets[0].Query, "preset1", "Preset 1")

	// Verify that saveReportMetadata was called with a report that has Scheduled=true
	mockDP.AssertCalled(t, "saveReportMetadata", mock.MatchedBy(func(report models.Report) bool {
		return report.Scheduled == true
	}))

	// Verify that SavePreset was not called for preset3 (inactive) or preset4 (disabled schedule)
	mockDP.AssertNotCalled(t, "SavePreset", mock.MatchedBy(func(preset models.ReportPreset) bool {
		return preset.ID == "preset3" || preset.ID == "preset4"
	}))
}

// TestSchedulerService_RecordExecution tests the recordExecution method
func TestSchedulerService_RecordExecution(t *testing.T) {
	mockDP, mockRM, tempDir, cleanup := setupTestSchedulerEnvironment(t)
	defer cleanup()

	// Create scheduler service
	scheduler := NewSchedulerService(mockDP, mockRM, tempDir)

	// Create test preset
	preset := models.ReportPreset{
		ID:         "preset1",
		Name:       "Preset 1",
		ReportType: "groups",
	}

	// Record a successful execution
	scheduler.recordExecution(preset, ExecutionStatusSuccess, "report1", "")

	// Verify that the execution was recorded
	executions := scheduler.GetExecutions()
	assert.Len(t, executions, 1)
	assert.Equal(t, "preset1", executions[0].PresetID)
	assert.Equal(t, "Preset 1", executions[0].PresetName)
	assert.Equal(t, ExecutionStatusSuccess, executions[0].Status)
	assert.Equal(t, "report1", executions[0].ReportID)
	assert.Empty(t, executions[0].ErrorMessage)

	// Record a failed execution
	scheduler.recordExecution(preset, ExecutionStatusFailed, "", "Error message")

	// Verify that both executions were recorded
	executions = scheduler.GetExecutions()
	assert.Len(t, executions, 2)
	assert.Equal(t, ExecutionStatusFailed, executions[1].Status)
	assert.Empty(t, executions[1].ReportID)
	assert.Equal(t, "Error message", executions[1].ErrorMessage)

	// Test GetExecutions
	allExecutions := scheduler.GetExecutions()
	assert.Len(t, allExecutions, 2)

	// Filter manually for preset1
	var preset1Executions []models.ReportExecution
	for _, execution := range allExecutions {
		if execution.PresetID == "preset1" {
			preset1Executions = append(preset1Executions, execution)
		}
	}
	assert.Len(t, preset1Executions, 2)

	// Filter manually for non-existent preset
	var nonExistentExecutions []models.ReportExecution
	for _, execution := range allExecutions {
		if execution.PresetID == "non-existent" {
			nonExistentExecutions = append(nonExistentExecutions, execution)
		}
	}
	assert.Len(t, nonExistentExecutions, 0)
}

// TestSchedulerService_SaveLoadExecutions tests saving and loading executions
func TestSchedulerService_SaveLoadExecutions(t *testing.T) {
	mockDP, mockRM, tempDir, cleanup := setupTestSchedulerEnvironment(t)
	defer cleanup()

	// Create scheduler service
	scheduler := NewSchedulerService(mockDP, mockRM, tempDir)

	// Create test preset
	preset := models.ReportPreset{
		ID:         "preset1",
		Name:       "Preset 1",
		ReportType: "groups",
	}

	// Record some executions
	scheduler.recordExecution(preset, ExecutionStatusSuccess, "report1", "")
	scheduler.recordExecution(preset, ExecutionStatusFailed, "", "Error message")

	// Create a new scheduler service that should load the executions
	scheduler2 := NewSchedulerService(mockDP, mockRM, tempDir)

	// No need to load executions explicitly anymore

	// Verify that the executions were loaded
	executions := scheduler2.GetExecutions()
	assert.Len(t, executions, 2)
	assert.Equal(t, "preset1", executions[0].PresetID)
	assert.Equal(t, ExecutionStatusSuccess, executions[0].Status)
	assert.Equal(t, "report1", executions[0].ReportID)
	assert.Equal(t, ExecutionStatusFailed, executions[1].Status)
	assert.Equal(t, "Error message", executions[1].ErrorMessage)
}

// TestSchedulerService_DeleteExecutionsForPreset tests the DeleteExecutionsForPreset method
func TestSchedulerService_DeleteExecutionsForPreset(t *testing.T) {
	mockDP, mockRM, tempDir, cleanup := setupTestSchedulerEnvironment(t)
	defer cleanup()

	// Create scheduler service
	scheduler := NewSchedulerService(mockDP, mockRM, tempDir)

	// Create test presets
	preset1 := models.ReportPreset{
		ID:         "preset1",
		Name:       "Preset 1",
		ReportType: "groups",
	}

	preset2 := models.ReportPreset{
		ID:         "preset2",
		Name:       "Preset 2",
		ReportType: "users",
	}

	// Mock GetPreset to return our test presets
	mockDP.On("GetPreset", "preset1").Return(preset1, nil)
	mockDP.On("GetPreset", "preset2").Return(preset2, nil)
	mockDP.On("GetPreset", "non-existent").Return(models.ReportPreset{}, assert.AnError)

	// Record executions for both presets
	scheduler.recordExecution(preset1, ExecutionStatusSuccess, "report1", "")
	scheduler.recordExecution(preset1, ExecutionStatusFailed, "", "Error message")
	scheduler.recordExecution(preset2, ExecutionStatusSuccess, "report2", "")

	// Verify that executions were recorded
	executions := scheduler.GetExecutions()
	assert.Len(t, executions, 3)

	// Get all executions
	allExecutions := scheduler.GetExecutions()
	assert.Len(t, allExecutions, 3)

	// Filter manually for preset1
	var preset1Executions []models.ReportExecution
	for _, execution := range allExecutions {
		if execution.PresetID == "preset1" {
			preset1Executions = append(preset1Executions, execution)
		}
	}
	assert.Len(t, preset1Executions, 2)

	// Filter manually for preset2
	var preset2Executions []models.ReportExecution
	for _, execution := range allExecutions {
		if execution.PresetID == "preset2" {
			preset2Executions = append(preset2Executions, execution)
		}
	}
	assert.Len(t, preset2Executions, 1)

	// Delete executions for preset1
	err := scheduler.DeleteExecutionsForPreset("preset1")
	assert.NoError(t, err)

	// Get all executions again
	allExecutions = scheduler.GetExecutions()

	// Filter manually for preset1 again
	preset1Executions = nil
	for _, execution := range allExecutions {
		if execution.PresetID == "preset1" {
			preset1Executions = append(preset1Executions, execution)
		}
	}
	assert.Len(t, preset1Executions, 0)

	// Filter manually for preset2 again
	preset2Executions = nil
	for _, execution := range allExecutions {
		if execution.PresetID == "preset2" {
			preset2Executions = append(preset2Executions, execution)
		}
	}
	assert.Len(t, preset2Executions, 1)

	// Verify that the preset1 execution history file was deleted
	preset1File := filepath.Join(scheduler.executionsDir, "preset1_execution_history.json")
	_, err = os.Stat(preset1File)
	assert.True(t, os.IsNotExist(err), "Preset1 execution history file should be deleted")

	// Verify that the preset2 execution history file still exists
	preset2File := filepath.Join(scheduler.executionsDir, "preset2_execution_history.json")
	_, err = os.Stat(preset2File)
	assert.NoError(t, err, "Preset2 execution history file should still exist")

	// Test deleting non-existent preset
	err = scheduler.DeleteExecutionsForPreset("non-existent")
	assert.NoError(t, err, "Deleting non-existent preset should not return an error")
}

// TestSchedulerService_DeleteExecutionsForPresetWithSharedID tests the DeleteExecutionsForPreset method with shared IDs
func TestSchedulerService_DeleteExecutionsForPresetWithSharedID(t *testing.T) {
	mockDP, mockRM, tempDir, cleanup := setupTestSchedulerEnvironment(t)
	defer cleanup()

	// Create scheduler service
	scheduler := NewSchedulerService(mockDP, mockRM, tempDir)

	// Create test presets with shared IDs
	preset1 := models.ReportPreset{
		ID:         "preset1",
		Name:       "Preset 1",
		ReportType: "groups",
		SharedID:   "shared1",
	}

	preset2 := models.ReportPreset{
		ID:         "preset2",
		Name:       "Preset 2",
		ReportType: "users",
		SharedID:   "shared2",
	}

	preset3 := models.ReportPreset{
		ID:         "preset3",
		Name:       "Preset 3",
		ReportType: "groups",
		SharedID:   "shared1", // Same shared ID as preset1
	}

	// Mock GetPreset to return our test presets
	mockDP.On("GetPreset", "preset1").Return(preset1, nil)
	mockDP.On("GetPreset", "preset2").Return(preset2, nil)
	mockDP.On("GetPreset", "preset3").Return(preset3, nil)

	// Mock GetPresets to return all presets for shared ID lookup
	mockDP.On("GetPresets").Return([]models.ReportPreset{preset1, preset2, preset3}, nil)

	// Record executions for all presets
	scheduler.recordExecution(preset1, ExecutionStatusSuccess, "report1", "")
	scheduler.recordExecution(preset3, ExecutionStatusSuccess, "report3", "")
	scheduler.recordExecution(preset2, ExecutionStatusSuccess, "report2", "")

	// Verify that executions were recorded in the shared ID files
	sharedFile1 := filepath.Join(scheduler.executionsDir, "shared1_execution_history.json")
	_, err := os.Stat(sharedFile1)
	assert.NoError(t, err, "Shared1 execution history file should exist")

	// Delete executions for preset1
	err = scheduler.DeleteExecutionsForPreset("preset1")
	assert.NoError(t, err)

	// Verify that preset1 executions were removed from the shared file
	// but the file still exists because preset3 still uses it
	_, err = os.Stat(sharedFile1)
	assert.NoError(t, err, "Shared1 execution history file should still exist")

	// Delete executions for preset3
	err = scheduler.DeleteExecutionsForPreset("preset3")
	assert.NoError(t, err)

	// Now the shared1 execution history file should contain an empty array
	data, err := os.ReadFile(sharedFile1)
	assert.NoError(t, err)

	var executions []models.ReportExecution
	err = json.Unmarshal(data, &executions)
	assert.NoError(t, err)
	assert.Len(t, executions, 0, "Shared1 execution history file should be empty")
}
