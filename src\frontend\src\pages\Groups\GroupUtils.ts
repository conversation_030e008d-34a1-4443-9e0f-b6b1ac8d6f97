import { Group } from './GroupTypes';

// Extract LOBs from groups
export const extractLobsFromGroups = (groups: Group[]): string[] => {
  const uniqueLobs = new Set<string>();

  groups.forEach(group => {
    // Check all possible LOB field formats
    if (group.LOB && typeof group.LOB === 'string') {
      uniqueLobs.add(group.LOB);
    } else if (group.Lob && typeof group.Lob === 'string') {
      uniqueLobs.add(group.Lob);
    } else if (group.lob && typeof group.lob === 'string') {
      uniqueLobs.add(group.lob);
    } else if (group.Lobs && Array.isArray(group.Lobs)) {
      group.Lobs.forEach(lob => {
        if (lob && typeof lob === 'string') {
          uniqueLobs.add(lob);
        }
      });
    }
  });

  return Array.from(uniqueLobs).sort();
};

// Process groups data
export const processGroups = (apiGroups: any[]): Group[] => {
  if (!apiGroups || !Array.isArray(apiGroups)) {
    return [];
  }

  // Process group data
  return apiGroups.map(apiGroup => {
    // Create normalized group object
    const group: Group = {
      Groupname: '',
      Type: '',
      Description: '',
      Members: []
    };

    // Extract fields
    if (typeof apiGroup === 'object' && apiGroup !== null) {
      // Extract Groupname
      if ('Groupname' in apiGroup) group.Groupname = apiGroup.Groupname || '';
      else if ('groupname' in apiGroup) group.Groupname = (apiGroup as any).groupname || '';
      else if ('GroupName' in apiGroup) group.Groupname = (apiGroup as any).GroupName || '';
      else if ('groupName' in apiGroup) group.Groupname = (apiGroup as any).groupName || '';
      else if ('Name' in apiGroup) group.Groupname = (apiGroup as any).Name || '';
      else if ('name' in apiGroup) group.Groupname = (apiGroup as any).name || '';

      // Extract Type
      if ('Type' in apiGroup) group.Type = apiGroup.Type || '';
      else if ('type' in apiGroup) group.Type = (apiGroup as any).type || '';

      // Extract Description
      if ('Description' in apiGroup) group.Description = apiGroup.Description || '';
      else if ('description' in apiGroup) group.Description = (apiGroup as any).description || '';

      // Extract Members
      if ('Members' in apiGroup && (Array.isArray(apiGroup.Members) || typeof apiGroup.Members === 'object')) {
        group.Members = apiGroup.Members;
      } else if ('members' in apiGroup && (Array.isArray((apiGroup as any).members) || typeof (apiGroup as any).members === 'object')) {
        group.Members = (apiGroup as any).members;
      }

      // Extract LOB fields
      if ('LOB' in apiGroup) group.LOB = apiGroup.LOB;
      if ('Lob' in apiGroup) group.Lob = apiGroup.Lob;
      if ('lob' in apiGroup) group.lob = (apiGroup as any).lob;
      if ('Lobs' in apiGroup) group.Lobs = apiGroup.Lobs;
      if ('lobs' in apiGroup) group.Lobs = (apiGroup as any).lobs;
    }

    return { ...apiGroup, ...group };
  });
};
