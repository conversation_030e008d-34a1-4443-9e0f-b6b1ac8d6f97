import { Edit, ArrowUpRight, Layers as Layers<PERSON><PERSON>, Clock, FileText, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { PresetCardActions } from './PresetCardActions'
import { PresetCardProps } from './types'
import { formatDate } from '@/lib/utils'
import { formatScheduleFrequency } from '@/utils/countdownUtils'
import { CountdownTimer } from '@/components/ui/countdown-timer'

export function PresetCard({ preset, onEdit, onView, onGenerate, onOpenHistory }: PresetCardProps) {
  return (
    <Card
      key={preset.id}
      id={`preset-${preset.id}`}
      className={`transition-all hover:shadow-md ${preset.isActive === false ? 'opacity-60' : ''}`}
      onClick={() => onView(preset)}
    >
      <CardHeader className="pb-2">
        <CardTitle
          className="text-base flex items-center justify-between cursor-pointer hover:text-primary transition-colors"
          onClick={(e) => {
            e.stopPropagation();
            // Directly call the view function
            onView(preset);
          }}
        >
          <span className="truncate">{preset.name}</span>
          {preset.version && (
            <Badge variant="outline" className="ml-2 text-xs">v{preset.version}</Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-2">
          <div className="text-sm text-muted-foreground line-clamp-2">
            {preset.description || <span className="italic text-gray-400">No description</span>}
          </div>

          <div className="flex items-start">
            <span className="font-medium text-gray-700 mr-2 min-w-[50px]">Type:</span>
            <span className="text-muted-foreground">
              {preset.reportType === 'users' ? 'Users Only' :
               preset.reportType === 'groups' ? 'Groups Only' :
               'Users & Groups'}
            </span>
          </div>

          <div className="flex items-start">
            <span className="font-medium text-gray-700 mr-2 min-w-[50px]">Query:</span>
            <span className="text-muted-foreground truncate">
              {preset.searchQuery || <span className="italic text-gray-400">No query</span>}
            </span>
          </div>

          <div className="flex flex-wrap gap-1 mb-2">
            {preset.isActive === false && (
              <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 shadow-sm text-xs">
                Inactive
              </Badge>
            )}
            {preset.flattenMembership && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 shadow-sm text-xs">
                <LayersIcon className="h-3 w-3 mr-1" /> Flatten Membership
              </Badge>
            )}
            {preset.schedule?.enabled === true && (
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200 shadow-sm text-xs cursor-pointer hover:bg-green-100 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  onOpenHistory(preset);
                }}
                title="Click to view execution history"
              >
                <Clock className="h-3 w-3 mr-1" /> Scheduled
              </Badge>
            )}
            {preset.hasReports && (
              <Badge
                variant="outline"
                className="bg-purple-50 text-purple-700 border-purple-200 shadow-sm text-xs cursor-pointer hover:bg-purple-100 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  // Use sharedId if available, otherwise fall back to preset.id
                  const queryParam = preset.sharedId ? `sharedPresetId=${preset.sharedId}` : `presetId=${preset.id}`;
                  window.location.href = `/reports?${queryParam}`;
                }}
                title="View all reports generated from this preset (including all versions)"
              >
                <FileText className="h-3 w-3 mr-1" /> View All Reports
              </Badge>
            )}
          </div>

          {/* Schedule information */}
          {preset.schedule?.enabled === true && (
            <div className="mt-2 pt-2 border-t border-gray-100">
              <div className="flex items-start">
                <span className="font-medium text-gray-700 mr-2 min-w-[50px]">Schedule:</span>
                <span className="text-muted-foreground flex items-center">
                  <Clock className="h-3 w-3 mr-1 inline" />
                  {formatScheduleFrequency(preset.schedule)}
                </span>
              </div>

              {(preset.schedule as any).nextRun && (
                <div className="flex items-start mt-1">
                  <span className="font-medium text-gray-700 mr-2 min-w-[50px]">Next run:</span>
                  <span className="text-muted-foreground">
                    <span className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1 inline" />
                      {formatDate((preset.schedule as any).nextRun)}
                    </span>
                    <span className="ml-4 text-xs text-green-600 font-medium">
                      <CountdownTimer
                        targetDate={(preset.schedule as any).nextRun}
                        refreshInterval={1000} // Update every second for smoother countdown
                      />
                    </span>
                  </span>
                </div>
              )}
            </div>
          )}
          <div className="flex items-start">
            <span className="font-medium text-gray-700 mr-2 min-w-[50px]">Created:</span>
            <span className="text-muted-foreground">{preset.createdAt && formatDate(preset.createdAt)}</span>
          </div>
          {preset.updatedAt && preset.updatedAt !== preset.createdAt && (
            <div className="flex items-start">
              <span className="font-medium text-gray-700 mr-2 min-w-[50px]">Updated:</span>
              <span className="text-muted-foreground">{formatDate(preset.updatedAt)}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2 pb-3 border-t border-gray-100 mt-0">
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onEdit(preset);
            }}
            disabled={preset.isActive === false}
            className="transition-all hover:bg-gray-100 h-8 text-xs px-2"
          >
            <Edit className="h-3.5 w-3.5 mr-1" /> Edit
          </Button>

          <PresetCardActions
            preset={preset}
            selectedRepoId={preset.repositoryId || ''}
            onToggleActivation={(presetId) => window.dispatchEvent(new CustomEvent('togglePresetActivation', { detail: presetId }))}
            onDeleteClick={(presetId) => window.dispatchEvent(new CustomEvent('deletePreset', { detail: presetId }))}
          />
        </div>

        {/* Generate report button */}
        <Button
          variant="default"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            onGenerate(preset);
          }}
          disabled={preset.isActive === false}
          className="transition-all shadow-sm hover:shadow-md bg-gradient-to-r from-primary to-primary/90 h-8 text-xs px-3"
        >
          <ArrowUpRight className="h-3.5 w-3.5 mr-1" /> Generate
        </Button>
      </CardFooter>
    </Card>
  )
}
