package search

// This file serves as a central import file for the bleve search functionality.
// The implementation is split across multiple files for better maintainability:
// - bleve_search_service.go: Main service definition and initialization
// - bleve_search_index.go: Indexing operations
// - bleve_search_groups.go: Group search functionality
// - bleve_search_users.go: User search functionality
// - bleve_search_query.go: Query building and preprocessing
