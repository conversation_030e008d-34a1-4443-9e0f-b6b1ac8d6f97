package api

import (
	"context"
	"log"

	"github.com/gin-gonic/gin"
)

// CancellationMiddleware adds context cancellation detection to requests
// This middleware will detect when a client disconnects and cancel the request context
func CancellationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the request context which will be cancelled when the client disconnects
		ctx := c.Request.Context()

		// We'll use the original context directly without creating a new one
		// This way we won't get false cancellation signals when the request completes normally
		c.Request = c.Request.WithContext(ctx)

		// We don't need to watch for context cancellation in a goroutine
		// We'll check for cancellation directly in the CheckCancellation function

		// Add a helper function to check if the request has been cancelled
		c.Set("IsCancelled", func() bool {
			select {
			case <-ctx.Done():
				return true
			default:
				return false
			}
		})

		// Process the request
		c.Next()
	}
}

// CheckCancellation is a helper function to check if a request has been cancelled
// It can be used in long-running operations to stop processing early
// Returns true if the request is cancelled and aborts the request
func CheckCancellation(c *gin.Context) bool {
	// Check if the request context is done
	select {
	case <-c.Request.Context().Done():
		// Context is done, check if it was cancelled or just completed normally
		if c.Request.Context().Err() == context.Canceled {
			// This was an actual cancellation, not just a completed request
			log.Printf("Request cancelled by client: %s", c.Request.URL.Path)

			// Set a response status to indicate the request was cancelled
			c.AbortWithStatusJSON(499, gin.H{
				"error": "Request cancelled by client",
				"path":  c.Request.URL.Path,
			})

			return true
		}
		// Context is done but not cancelled (e.g., timeout)
		return false
	default:
		// Context is still active, request is not cancelled
		return false
	}
}
