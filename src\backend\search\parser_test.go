package search

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseQueryWithInvalidSyntax(t *testing.T) {
	parser := NewParser()

	tests := []struct {
		name        string
		query       string
		expectError bool
		errorMsg    string
	}{
		{"Empty query", "", false, ""},
		{"Valid query", "type:security", false, ""},
		{"Valid tilde syntax", "type:~security", false, ""},
		{"Invalid tilde syntax", "name~cas", true, "invalid syntax: tilde (~) must be used after a field name and colon"},
		{"Invalid tilde syntax 2", "~admin", true, "invalid syntax: tilde (~) must be used after a field name and colon"},
		{"Missing closing parenthesis", "(type:security AND lob:it", true, "invalid syntax: missing closing parenthesis"},
		{"Unexpected closing parenthesis", "type:security AND lob:it)", true, "invalid syntax: unexpected closing parenthesis"},
		{"Multiple unbalanced parentheses", "((type:security) AND lob:it", true, "invalid syntax: missing closing parenthesis"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := parser.ParseQuery(tt.query)

			if tt.expectError {
				assert.Error(t, err)
				if err != nil && tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
