import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { formatDate } from '@/lib/utils'
import { ViewPresetDialogProps } from './types'
import { Calendar } from 'lucide-react'

export function ViewPresetDialog({
  open,
  onOpenChange,
  preset,
  availableVersions,
  onNavigateToVersion,
  versionInputValue,
  onVersionInputChange,
  onVersionInputSubmit,
  versionNavigating,
}: ViewPresetDialogProps) {
  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft' && !versionNavigating) {
      // Navigate to previous version (lower number = newer version)
      navigateToPrevVersion()
    } else if (e.key === 'ArrowRight' && !versionNavigating) {
      // Navigate to next version (higher number = older version)
      navigateToNextVersion()
    }
  }

  // Navigate to previous version
  const navigateToPrevVersion = () => {
    const currentVersion = preset?.version || 1
    // Sort versions in descending order and find the next lower version
    const sortedVersions = [...availableVersions].sort((a, b) => b - a)
    const prevVersionIndex = sortedVersions.findIndex(v => v === currentVersion) + 1
    const prevVersion = sortedVersions[prevVersionIndex]

    if (prevVersion) {
      onNavigateToVersion(prevVersion)
    }
  }

  // Navigate to next version
  const navigateToNextVersion = () => {
    const currentVersion = preset?.version || 1
    // Sort versions in descending order and find the next higher version
    const sortedVersions = [...availableVersions].sort((a, b) => b - a)
    const nextVersionIndex = sortedVersions.findIndex(v => v === currentVersion) - 1
    const nextVersion = sortedVersions[nextVersionIndex]

    if (nextVersion && nextVersionIndex >= 0) {
      onNavigateToVersion(nextVersion)
    }
  }

  // Update version input value when preset changes
  useEffect(() => {
    if (preset?.version) {
      onVersionInputChange({ target: { value: preset.version.toString() } } as React.ChangeEvent<HTMLInputElement>)
    }
  }, [preset, onVersionInputChange])

  if (!preset) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-2xl max-h-[90vh] overflow-y-auto"
        onKeyDown={handleKeyDown}
        tabIndex={0} // Make the dialog focusable to capture key events
      >
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>View Report Preset</span>
            {preset.parentId && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={navigateToPrevVersion}
                  disabled={versionNavigating || !availableVersions.some(v => v < (preset?.version || 1))}
                  title="Previous version"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M15 18l-6-6 6-6" />
                  </svg>
                </Button>

                <div className="flex items-center space-x-1">
                  <span className="text-sm text-muted-foreground">Version</span>
                  <div className="relative w-16">
                    <Input
                      value={versionInputValue}
                      onChange={onVersionInputChange}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          onVersionInputSubmit();
                        }
                      }}
                      className="h-8 text-center"
                      size={4}
                    />
                  </div>
                  <span className="text-sm text-muted-foreground">of {Math.max(...availableVersions, 1)}</span>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={navigateToNextVersion}
                  disabled={versionNavigating || !availableVersions.some(v => v > (preset?.version || 1))}
                  title="Next version"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M9 18l6-6-6-6" />
                  </svg>
                </Button>
              </div>
            )}
          </DialogTitle>
          <DialogDescription>
            {preset.version && `Version ${preset.version} of this preset.`} This is a read-only view.
            {preset.parentId && <span className="ml-1">Use arrow keys ← → to navigate between versions.</span>}
            {preset.createdAt && (
              <div className="mt-1 text-xs text-muted-foreground">
                Created: {formatDate(preset.createdAt)}
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-3">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="view-name">Name</Label>
              <Input
                id="view-name"
                value={preset.name || ''}
                readOnly
                className="bg-gray-50"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="view-reportType">Report Type</Label>
              <Input
                id="view-reportType"
                value={preset.reportType === 'users' ? 'Users Only' : preset.reportType === 'groups' ? 'Groups Only' : 'Users & Groups'}
                readOnly
                className="bg-gray-50"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="view-description">Description</Label>
            <Textarea
              id="view-description"
              value={preset.description || ''}
              readOnly
              className="bg-gray-50"
              rows={2}
            />
          </div>

          <Separator className="my-2" />

          <div className="space-y-2">
            <Label htmlFor="view-searchQuery">Search Query</Label>
            <Input
              id="view-searchQuery"
              value={preset.searchQuery || ''}
              readOnly
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label>Schedule</Label>
            <div className="bg-gray-50 p-3 rounded border">
              {preset.schedule?.enabled === true ? (
                <div className="space-y-1">
                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Frequency:</span>
                    <span className="text-muted-foreground capitalize">{preset.schedule.frequency}</span>
                  </div>

                  {preset.schedule.frequency === 'interval' && (
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Interval:</span>
                      <span className="text-muted-foreground">
                        {preset.schedule.intervalHours || 0} hours {preset.schedule.intervalMinutes || 0} minutes
                      </span>
                    </div>
                  )}

                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Time:</span>
                    <span className="text-muted-foreground">
                      {String(preset.schedule.hour).padStart(2, '0')}:{String(preset.schedule.minute).padStart(2, '0')}
                    </span>
                  </div>

                  {preset.schedule.frequency === 'weekly' && (
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Day:</span>
                      <span className="text-muted-foreground">
                        {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][preset.schedule.dayOfWeek || 0]}
                      </span>
                    </div>
                  )}

                  {preset.schedule.frequency === 'monthly' && (
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Day:</span>
                      <span className="text-muted-foreground">
                        {preset.schedule.dayOfMonth || 1}
                      </span>
                    </div>
                  )}

                  {(preset.schedule as any).nextRun && (
                    <div className="flex items-center mt-2 pt-2 border-t border-gray-200">
                      <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Next Run:</span>
                      <span className="text-muted-foreground flex items-center">
                        <Calendar className="h-4 w-4 mr-1 inline" />
                        {formatDate((preset.schedule as any).nextRun)}
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <span className="text-muted-foreground">Not scheduled</span>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Columns</Label>
            <div className="bg-gray-50 p-3 rounded border">
              {preset.reportType !== 'users' && (
                <div className="mb-3">
                  <h4 className="font-medium text-sm mb-1">Group Columns</h4>
                  <div className="flex flex-wrap gap-1">
                    {preset.groupColumns?.map(column => (
                      <span key={column} className="bg-blue-50 text-blue-700 px-2 py-0.5 rounded text-xs capitalize">
                        {column}
                      </span>
                    ))}
                    {(!preset.groupColumns || preset.groupColumns.length === 0) && (
                      <span className="text-muted-foreground text-xs">No columns selected</span>
                    )}
                  </div>
                </div>
              )}
              {preset.reportType !== 'groups' && (
                <div>
                  <h4 className="font-medium text-sm mb-1">User Columns</h4>
                  <div className="flex flex-wrap gap-1">
                    {preset.userColumns?.map(column => (
                      <span key={column} className="bg-green-50 text-green-700 px-2 py-0.5 rounded text-xs capitalize">
                        {column}
                      </span>
                    ))}
                    {(!preset.userColumns || preset.userColumns.length === 0) && (
                      <span className="text-muted-foreground text-xs">No columns selected</span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {(preset.lob !== 'all_lobs' || preset.types || preset.groupIds || preset.userIds) && (
            <>
              <Separator className="my-2" />
              <div className="space-y-2">
                <Label>Legacy Filters</Label>
                <div className="bg-gray-50 p-3 rounded border space-y-2">
                  {preset.lob !== 'all_lobs' && (
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 mr-2 min-w-[80px]">LOB:</span>
                      <span className="text-muted-foreground">{preset.lob}</span>
                    </div>
                  )}
                  {preset.types && (
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Types:</span>
                      <span className="text-muted-foreground">{preset.types}</span>
                    </div>
                  )}
                  {preset.groupIds && (
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Groups:</span>
                      <span className="text-muted-foreground">{preset.groupIds}</span>
                    </div>
                  )}
                  {preset.userIds && (
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700 mr-2 min-w-[80px]">Users:</span>
                      <span className="text-muted-foreground">{preset.userIds}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
