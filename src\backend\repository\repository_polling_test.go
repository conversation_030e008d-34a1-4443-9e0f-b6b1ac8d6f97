package repository

import (
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"

	"github.com/stretchr/testify/assert"
)

// MockRepository is a mock implementation of the Repository interface for testing
type MockRepository struct {
	config         models.RepositoryConfig
	localRepoDir   string
	lastCommit     string
	isPolling      bool
	syncInProgress bool
	syncMutex      sync.Mutex
	callbacks      []func()
	syncLogger     *services.SyncLogger
	repoID         string

	// Mock methods
	mockGetLatestCommitHash func() (string, error)
	mockSyncRepository      func() error
}

func (m *MockRepository) GetConfig() models.RepositoryConfig {
	return m.config
}

func (m *MockRepository) GetLocalRepoPath() string {
	return m.localRepoDir
}

func (m *MockRepository) SyncRepository() error {
	if m.mockSyncRepository != nil {
		return m.mockSyncRepository()
	}
	return nil
}

func (m *MockRepository) EnsureLocalRepoExists() error {
	return nil
}

func (m *MockRepository) StartPolling() error {
	m.isPolling = true
	go func() {
		for m.isPolling {
			hasChanges, _ := m.checkForChanges()
			if hasChanges {
				m.syncLogger.AddLog(m.repoID, "Repository changes detected, triggering callbacks", models.LogLevelInfo)
				for _, callback := range m.callbacks {
					callback()
				}
			}
			time.Sleep(time.Duration(m.config.PollFrequency) * time.Second)
		}
	}()
	return nil
}

func (m *MockRepository) StopPolling() {
	m.isPolling = false
}

func (m *MockRepository) RegisterChangeCallback(callback func()) {
	m.callbacks = append(m.callbacks, callback)
}

func (m *MockRepository) checkForChanges() (bool, error) {
	latestCommit, _ := m.getLatestCommitHash()
	hasChanges := m.lastCommit != latestCommit && m.lastCommit != ""

	if hasChanges {
		m.syncLogger.AddLog(m.repoID, "Repository has changes available", models.LogLevelInfo)
	} else {
		m.syncLogger.AddLog(m.repoID, "Repository is up to date", models.LogLevelInfo)
	}

	return hasChanges, nil
}

func (m *MockRepository) getLatestCommitHash() (string, error) {
	if m.mockGetLatestCommitHash != nil {
		return m.mockGetLatestCommitHash()
	}
	return "", nil
}

// TestRepositoryPolling tests the repository polling functionality
func TestRepositoryPolling(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "repo-polling-test")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a sync logger
	syncLogger := services.NewSyncLogger()

	// Create a mock repository
	repo := &MockRepository{
		config: models.RepositoryConfig{
			ID:            "test-repo",
			Name:          "Test Repository",
			Type:          models.Bitbucket,
			IsActive:      true,
			PollFrequency: 1, // 1 second for faster testing
		},
		localRepoDir:   tempDir,
		lastCommit:     "initial-commit-hash",
		isPolling:      false,
		syncInProgress: false,
		syncMutex:      sync.Mutex{},
		callbacks:      []func(){},
		syncLogger:     syncLogger,
		repoID:         "test-repo",
	}

	// Create a channel to signal when the callback is called
	callbackCalled := make(chan bool, 1)

	// Register a callback
	repo.RegisterChangeCallback(func() {
		callbackCalled <- true
	})

	// Create a COMMIT_HASH file with an initial commit hash
	initialCommit := "initial-commit-hash"
	commitFile := filepath.Join(tempDir, "COMMIT_HASH")
	err = os.WriteFile(commitFile, []byte(initialCommit), 0644)
	if err != nil {
		t.Fatalf("Failed to write commit hash file: %v", err)
	}

	// Set up the mock to return a different commit hash
	repo.mockGetLatestCommitHash = func() (string, error) {
		return "new-commit-hash", nil
	}

	// Start polling
	err = repo.StartPolling()
	assert.NoError(t, err)
	assert.True(t, repo.isPolling)

	// Wait for the callback to be called or timeout
	select {
	case <-callbackCalled:
		// Callback was called, test passed
	case <-time.After(5 * time.Second):
		t.Fatal("Timeout waiting for callback to be called")
	}

	// Stop polling
	repo.StopPolling()
	assert.False(t, repo.isPolling)
}

// TestRepositoryCallbackSync tests that the callback triggers a sync
func TestRepositoryCallbackSync(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "repo-callback-test")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a sync logger
	syncLogger := services.NewSyncLogger()

	// Create a mock repository
	repo := &MockRepository{
		config: models.RepositoryConfig{
			ID:            "test-repo",
			Name:          "Test Repository",
			Type:          models.Bitbucket,
			IsActive:      true,
			PollFrequency: 1, // 1 second for faster testing
		},
		localRepoDir:   tempDir,
		lastCommit:     "initial-commit-hash",
		isPolling:      false,
		syncInProgress: false,
		syncMutex:      sync.Mutex{},
		callbacks:      []func(){},
		syncLogger:     syncLogger,
		repoID:         "test-repo",
	}

	// Create a channel to signal when the sync is called
	syncCalled := make(chan bool, 1)

	// Set up the mock to signal when sync is called
	repo.mockSyncRepository = func() error {
		syncCalled <- true
		return nil
	}

	// Register a callback that calls SyncRepository
	repo.RegisterChangeCallback(func() {
		go repo.SyncRepository()
	})

	// Create a COMMIT_HASH file with an initial commit hash
	initialCommit := "initial-commit-hash"
	commitFile := filepath.Join(tempDir, "COMMIT_HASH")
	err = os.WriteFile(commitFile, []byte(initialCommit), 0644)
	if err != nil {
		t.Fatalf("Failed to write commit hash file: %v", err)
	}

	// Set up the mock to return a different commit hash
	repo.mockGetLatestCommitHash = func() (string, error) {
		return "new-commit-hash", nil
	}

	// Start polling
	err = repo.StartPolling()
	assert.NoError(t, err)
	assert.True(t, repo.isPolling)

	// Wait for the sync to be called or timeout
	select {
	case <-syncCalled:
		// Sync was called, test passed
	case <-time.After(5 * time.Second):
		t.Fatal("Timeout waiting for sync to be called")
	}

	// Stop polling
	repo.StopPolling()
	assert.False(t, repo.isPolling)
}
