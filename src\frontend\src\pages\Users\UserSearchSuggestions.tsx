import React, { useState, useEffect } from 'react';
import SearchInputWithSuggestions from '@/components/SearchInputWithSuggestions';
import { User } from './UserTypes';

interface UserSearchSuggestionsProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  onSearch: (value: string) => void;
  placeholder?: string;
  className?: string;
  searchInputRef: React.RefObject<HTMLInputElement>;
  users: User[];
  lobs: string[];
}

const UserSearchSuggestions: React.FC<UserSearchSuggestionsProps> = ({
  inputValue,
  setInputValue,
  onSearch,
  placeholder = 'Search users...',
  className = '',
  searchInputRef,
  users,
  lobs
}) => {
  // Extract unique group names from all users' groups
  const [groupNames, setGroupNames] = useState<string[]>([]);

  // Extract user names for suggestions
  const [userNames, setUserNames] = useState<string[]>([]);

  // Process users to extract suggestion data
  useEffect(() => {
    if (!users || users.length === 0) return;

    // Extract unique group names
    const groups = new Set<string>();
    // Extract user names
    const names = new Set<string>();

    users.forEach(user => {
      // Add user name
      if (user.name) {
        names.add(user.name);
      }

      // Process groups
      if (user.groups && Array.isArray(user.groups)) {
        user.groups.forEach(group => {
          if (group) {
            groups.add(group);
          }
        });
      }
    });

    setGroupNames(Array.from(groups).sort());
    setUserNames(Array.from(names).sort());
  }, [users]);

  // Create suggestions array for the SearchInputWithSuggestions component
  const suggestions = [
    // Filter type suggestions
    { type: 'name', value: '', display: 'name:' },
    { type: 'groups', value: '', display: 'groups:' },
    { type: 'lob', value: '', display: 'lob:' },

    // LOB value suggestions
    ...lobs.map(lob => ({
      type: 'lob',
      value: lob,
      display: lob
    })),

    // User name value suggestions
    ...userNames.slice(0, 100).map(name => ({
      type: 'name',
      value: name,
      display: name
    })),

    // Group name value suggestions
    ...groupNames.slice(0, 100).map(name => ({
      type: 'groups',
      value: name,
      display: name
    }))
  ];

  // Log the input value for debugging

  return (
    <SearchInputWithSuggestions
      ref={searchInputRef}
      placeholder={placeholder}
      initialValue={inputValue}
      value={inputValue} // Add explicit value prop for controlled component
      onChange={setInputValue}
      onSearch={onSearch}
      className={className}
      suggestions={suggestions}
      autoFocus={false}
    />
  );
};

export default UserSearchSuggestions;
