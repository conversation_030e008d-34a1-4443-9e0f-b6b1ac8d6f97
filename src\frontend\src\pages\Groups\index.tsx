import { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useRepository } from '@/context/RepositoryContext';
import { useFilters } from '@/components/layout/Layout';
import { apiClient, ApiError } from '@/api/client';

// Import components
import GroupFilters from './GroupFilters';
import GroupList from './GroupList';
import GroupPagination from './GroupPagination';

// Import types and utilities
import { Group, PaginationInfo, GroupRequestParams } from './GroupTypes';
import { processGroups, extractLobsFromGroups } from './GroupUtils';

const GroupsPage = () => {
  const { toast } = useToast();
  const { selectedRepoId, onRepositoryChange } = useRepository();
  const [filteredGroups, setFilteredGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  // Input value for the search field
  const [inputValue, setInputValue] = useState('');

  const [selectedLob, setSelectedLob] = useState<string>('');
  const [lobs, setLobs] = useState<string[]>([]);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const navigate = useNavigate();
  const location = useLocation();

  // Flag to prevent showing unfiltered data even momentarily
  const [dataReady, setDataReady] = useState(false);

  // Set default page size to 50, but display options in ascending order
  const pageSizeOptions = [10, 25, 50, 100];

  // Pagination state with 50 as default page size
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 50,
    totalItems: 0,
    totalPages: 0
  });

  // Track if component is mounted to prevent state updates after unmount
  const isMounted = useRef(true);

  // Single initialization ref to enforce only one fetch on page load
  const hasInitialized = useRef(false);

  // Ref to the search input element for direct focus control
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Track the last successful API request to avoid redundant calls
  const lastRequest = useRef<GroupRequestParams>({
    repoId: '',
    page: 0,
    pageSize: 0,
    lob: '',
    search: ''
  });

  // State for page jump input
  // Pagination input value is not currently used but may be needed in the future
  // const [pageInputValue, setPageInputValue] = useState<number | undefined>();

  // Component cleanup
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Listen for repository changes and refresh data
  useEffect(() => {
    // Register a callback for repository changes
    const unsubscribe = onRepositoryChange((newRepoId, prevRepoId) => {
      if (newRepoId !== prevRepoId && newRepoId) {
        // Reset state for the new repository
        setLobs([]);
        setFilteredGroups([]);

        // Force a refresh by resetting the initialization flag
        hasInitialized.current = false;

        // Get current pagination and search parameters
        const params = new URLSearchParams(location.search);
        const page = parseInt(params.get('page') || '1', 10);
        const pageSize = parseInt(params.get('pageSize') || '50', 10);
        const searchValue = params.get('query') || params.get('search') || '';
        const lobValue = params.get('lob') || '';

        // Fetch data for the new repository
        fetchGroupsData(newRepoId, page, pageSize, lobValue, searchValue);
      }
    });

    // Clean up the subscription when the component unmounts
    return unsubscribe;
  }, [onRepositoryChange, location.search]);

  // Toggle row expansion
  const toggleRowExpansion = (groupName: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      // Update URL directly
      const params = new URLSearchParams(location.search);
      params.set('page', String(newPage));
      navigate(`${location.pathname}?${params.toString()}`, { replace: true });

      // Update internal pagination state directly
      setPagination(prev => ({...prev, page: newPage}));

      // Fetch data directly
      if (selectedRepoId) {
        fetchGroupsData(selectedRepoId, newPage, pagination.pageSize, selectedLob, inputValue);
      }
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    // Update URL directly
    const params = new URLSearchParams(location.search);
    params.set('pageSize', String(newPageSize));
    params.set('page', '1'); // Reset to first page when changing page size
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });

    // Update internal pagination state directly
    setPagination(prev => ({...prev, pageSize: newPageSize, page: 1}));

    // Fetch data directly
    if (selectedRepoId) {
      fetchGroupsData(selectedRepoId, 1, newPageSize, selectedLob, inputValue);
    }
  };

  // Handle refresh click
  const handleRefreshClick = () => {
    if (!selectedRepoId || loading) return;

    // Force refresh by resetting initialization flag
    hasInitialized.current = false;

    // Update URL to trigger a fetch with page 1
    const params = new URLSearchParams(location.search);
    // Keep the existing search query
    if (inputValue) {
      params.set('query', inputValue);
      params.set('search', inputValue);
    } else {
      params.delete('query');
      params.delete('search');
    }
    if (selectedLob) {
      params.set('lob', selectedLob);
    } else {
      params.delete('lob');
    }
    params.set('page', '1');
    params.set('pageSize', pagination.pageSize.toString());

    // Use replace:true to avoid focus issues
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });

    // Fetch data directly
    fetchGroupsData(selectedRepoId, 1, pagination.pageSize, selectedLob, inputValue);
  };

  // Fetch groups data
  const fetchGroupsData = async (repoId: string, page: number, pageSize: number, lob?: string, search?: string) => {
    try {

      // Show loading state
      setLoading(true);
      setDataReady(false);

      // Only cancel previous requests if we're actually searching
      if (search) {
        // Cancel any previous requests for groups search
        // This ensures only the latest search query is processed
        const endpoint = `/api/search/groups`;
        const cancelCount = apiClient.utils.cancelRequestsByPattern(endpoint);
        if (cancelCount > 0) {
                  }
      }

      // Build the search query
      let query = search || "";

      // Add LOB filter if specified
      if (lob) {
        if (query) {
          query = `(${query}) AND lob:${lob}`;
        } else {
          query = `lob:${lob}`;
        }
      }


      // Get groups data from the API using Bleve search
      const response = await apiClient.search.searchGroups(query, page, pageSize, repoId);


      // Handle the Bleve search response format
      const groupItems = response?.groups || [];
      const paginationInfo = {
        page: response?.page || page,
        pageSize: response?.pageSize || pageSize,
        totalItems: response?.total || groupItems.length,
        totalPages: response?.totalPages || Math.ceil(groupItems.length / pageSize)
      };


      // Only update pagination if values are different to prevent infinite loops
      const shouldUpdatePagination =
        paginationInfo.page !== pagination.page ||
        paginationInfo.pageSize !== pagination.pageSize ||
        paginationInfo.totalItems !== pagination.totalItems ||
        paginationInfo.totalPages !== pagination.totalPages;

      if (shouldUpdatePagination) {
        setPagination(paginationInfo);
      }

      // For empty results, set empty state and return early
      if (groupItems.length === 0) {
                setFilteredGroups([]);
        if (isMounted.current) {
          setDataReady(true);
          setLoading(false);
        }
        return;
      }

      // Process groups
      const processedGroups = processGroups(groupItems);

      // Extract LOBs for filter dropdown (only if not already populated)
      if (lobs.length === 0 && processedGroups.length > 0) {
        const extractedLobs = extractLobsFromGroups(processedGroups);
        setLobs(extractedLobs);
      }

      // Update groups state
      setFilteredGroups(processedGroups);

      // Finish loading
      if (isMounted.current) {
        setDataReady(true);
        setLoading(false);
      }
    } catch (error) {
      // Check if this is a cancelled request
      if (error instanceof ApiError && error.status === -1) {
                // Don't update UI state for cancelled requests
        return;
      }

      console.error('Error fetching groups data:', error);
      // Get the error message from the API response if available
      let errorMessage = "Failed to fetch groups data";
      if (error instanceof ApiError && error.data && error.data.error) {
        errorMessage = error.data.error;
      }

      toast({
        title: "Search Error",
        description: errorMessage,
        variant: "destructive",
      });
      setFilteredGroups([]);
      setDataReady(true);
      setLoading(false);
    }
  };

  // Use a memoized GroupFilters component to prevent unnecessary re-renders
  const MemoizedGroupFilters = useCallback(() => {
    return (
      <GroupFilters
        inputValue={inputValue}
        setInputValue={setInputValue}
        selectedLob={selectedLob}
        setSelectedLob={setSelectedLob}
        lobs={lobs}
        loading={loading}
        searchInputRef={searchInputRef}
        selectedRepoId={selectedRepoId}
        handleRefreshClick={handleRefreshClick}
        fetchGroupsData={fetchGroupsData}
        pagination={pagination}
        filteredGroups={filteredGroups}
      />
    );
  // Remove inputValue from the dependency array to prevent re-renders when typing
  // Only re-render when these other dependencies change
  }, [selectedLob, lobs, loading, selectedRepoId, pagination, filteredGroups]);

  // Get access to the filters context
  const { setFilters } = useFilters();

  // Set the filters in the top header
  useEffect(() => {
    setFilters(<MemoizedGroupFilters />);

    // Cleanup on unmount
    return () => setFilters(null);
  }, [setFilters, MemoizedGroupFilters]);

  // Single source of truth for data fetching - handles URL params, pagination, and data fetching
  useEffect(() => {
    // Only proceed if component is mounted
    if (!isMounted.current) return;

    // Parse URL parameters
    const params = new URLSearchParams(location.search);

    // Get search query - check both 'query' and 'search' parameters
    const queryParam = params.get('query');
    const searchParam = params.get('search');
    const effectiveSearchParam = queryParam || searchParam;

    if (effectiveSearchParam) {
            setInputValue(effectiveSearchParam);
    } else {
            setInputValue('');
    }

    // Get pagination parameters
    let page = 1;
    let pageSize = 50;

    const pageParam = params.get('page');
    if (pageParam) {
      const parsedPage = parseInt(pageParam, 10);
      if (!isNaN(parsedPage) && parsedPage > 0) {
        page = parsedPage;
      }
    }

    const pageSizeParam = params.get('pageSize');
    if (pageSizeParam) {
      const parsedPageSize = parseInt(pageSizeParam, 10);
      if (!isNaN(parsedPageSize) && pageSizeOptions.includes(parsedPageSize)) {
        pageSize = parsedPageSize;
      }
    }

    // Update pagination state without triggering a re-render if values are the same
    const paginationChanged =
      pagination.page !== page ||
      pagination.pageSize !== pageSize;

    if (paginationChanged) {
      setPagination(prev => ({
        ...prev,
        page,
        pageSize
      }));
    }

    // Skip if we don't have a repository ID
    if (!selectedRepoId) {
      if (loading) setLoading(false);
      return;
    }

    // Check if this is exactly the same request as last time
    const currentRequest = {
      repoId: selectedRepoId,
      page,
      pageSize,
      lob: selectedLob,
      search: effectiveSearchParam || ''
    };

    // Skip duplicate requests unless forced by hasInitialized being false
    if (hasInitialized.current &&
        currentRequest.repoId === lastRequest.current.repoId &&
        currentRequest.page === lastRequest.current.page &&
        currentRequest.pageSize === lastRequest.current.pageSize &&
        currentRequest.lob === lastRequest.current.lob &&
        currentRequest.search === lastRequest.current.search) {
            return;
    }

    // Now we're going to make a real API request - update our state
    lastRequest.current = { ...currentRequest };
    hasInitialized.current = true;

    // Show loading state
    setLoading(true);
    setDataReady(false);

    // Always fetch data when the page loads or URL changes
    const searchValue = effectiveSearchParam || '';
    const lobValue = params.get('lob') || '';

    // Update selectedLob if it's different from the URL parameter
    if (lobValue !== selectedLob) {
      setSelectedLob(lobValue);
    }

    fetchGroupsData(selectedRepoId, page, pageSize, lobValue, searchValue);

  }, [location.search, selectedRepoId]);

  return (
    <div className="flex flex-col h-full">
      {/* Column Headers */}
      <div className="flex-none border-b bg-gray-50 dark:bg-gray-800 rounded-t-md">
        <div className="grid grid-cols-4 gap-4 py-3 px-4">
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Group</div>
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Type</div>
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">LOB</div>
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Description</div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-grow overflow-auto bg-white dark:bg-gray-900 rounded-b-md">
        <GroupList
          loading={loading}
          dataReady={dataReady}
          filteredGroups={filteredGroups}
          expandedRows={expandedRows}
          toggleRowExpansion={toggleRowExpansion}
          selectedRepoId={selectedRepoId}
          selectedLob={selectedLob}
          pagination={pagination}
          setInputValue={setInputValue}
          fetchGroupsData={fetchGroupsData}
        />
      </div>

      {/* Fixed Table Controls */}
      <GroupPagination
        pagination={pagination}
        loading={loading}
        pageSizeOptions={pageSizeOptions}
        handlePageChange={handlePageChange}
        handlePageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};

export default GroupsPage;
