package search

import (
	"testing"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
)

func TestFilterGroups(t *testing.T) {
	// Create test groups
	groups := []models.Group{
		{
			Groupname: "group1",
			Lob:       "Marketing",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group2",
			Lob:       "Engineering",
			Type:      "distribution",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group3",
			Lob:       "Finance",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
	}

	// Create a simple implementation of FilterGroups for testing
	filterGroups := func(groups []models.Group, query models.QueryParams) []models.Group {
		var result []models.Group

		for _, group := range groups {
			// Filter by LOB
			if query.LOB != "" && group.Lob != query.LOB {
				continue
			}

			// Filter by types
			if len(query.Types) > 0 {
				typeMatch := false
				for _, t := range query.Types {
					if group.Type == t {
						typeMatch = true
						break
					}
				}
				if !typeMatch {
					continue
				}
			}

			// Filter by group IDs
			if len(query.GroupIDs) > 0 {
				groupMatch := false
				for _, id := range query.GroupIDs {
					if group.Groupname == id {
						groupMatch = true
						break
					}
				}
				if !groupMatch {
					continue
				}
			}

			// All filters passed, add to result
			result = append(result, group)
		}

		return result
	}

	// Test LOB filter
	filteredGroups := filterGroups(groups, models.QueryParams{
		LOB: "Marketing",
	})
	assert.Len(t, filteredGroups, 1)
	assert.Equal(t, "group1", filteredGroups[0].Groupname)

	// Test type filter
	filteredGroups = filterGroups(groups, models.QueryParams{
		Types: []string{"security"},
	})
	assert.Len(t, filteredGroups, 2)
	assert.Contains(t, []string{"group1", "group3"}, filteredGroups[0].Groupname)
	assert.Contains(t, []string{"group1", "group3"}, filteredGroups[1].Groupname)

	// Test group ID filter
	filteredGroups = filterGroups(groups, models.QueryParams{
		GroupIDs: []string{"group2", "group3"},
	})
	assert.Len(t, filteredGroups, 2)
	assert.Contains(t, []string{"group2", "group3"}, filteredGroups[0].Groupname)
	assert.Contains(t, []string{"group2", "group3"}, filteredGroups[1].Groupname)

	// Test combined filters
	filteredGroups = filterGroups(groups, models.QueryParams{
		Types:    []string{"security"},
		GroupIDs: []string{"group1", "group2"},
	})
	assert.Len(t, filteredGroups, 1)
	assert.Equal(t, "group1", filteredGroups[0].Groupname)
}
