import React, { useState, useRef, useEffect, useLayoutEffect } from 'react'
import { Search, X, HelpCircle } from 'lucide-react'

interface Suggestion {
  type: string
  value: string
  display: string
}

interface SearchInputWithSuggestionsProps {
  onSearch: (value: string) => void
  placeholder?: string
  initialValue?: string
  debounceTime?: number
  className?: string
  suggestions?: Suggestion[]
  helpContent?: React.ReactNode
  autoFocus?: boolean
  // Legacy props for backward compatibility
  value?: string
  onChange?: (value: string) => void
}

const SearchInputWithSuggestions = React.forwardRef(function SearchInputWithSuggestionsInner(
  props: SearchInputWithSuggestionsProps,
  ref: React.ForwardedRef<HTMLInputElement>
) {
  const {
    onSearch,
    placeholder = 'Search...',
    initialValue = '',
    className = '',
    suggestions = [],
    helpContent,
    autoFocus = false,
    // Legacy props
    value: propValue,
    onChange
  } = props;

  // State for the input value
  const [inputValue, setInputValue] = useState(initialValue);

  // Internal ref for the input element
  const inputRef = useRef<HTMLInputElement>(null);

  // Update input value when initialValue or value prop changes
  useLayoutEffect(() => {
    // If value prop is provided, use it (for controlled component)
    if (propValue !== undefined) {
      setInputValue(propValue);
          } else {
      // Otherwise use initialValue
      setInputValue(initialValue);
          }
  }, [initialValue, propValue]);

  // Ref for the debounce timer
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Track if the input is focused
  const [isFocused, setIsFocused] = useState(false);

  // State for suggestions
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<Suggestion[]>([]);
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState(-1);
  const [cursorPosition, setCursorPosition] = useState(0);

  // Group suggestions by type
  const groupedSuggestions: Record<string, Suggestion[]> = {};
  suggestions.forEach(suggestion => {
    if (!groupedSuggestions[suggestion.type]) {
      groupedSuggestions[suggestion.type] = [];
    }
    groupedSuggestions[suggestion.type].push(suggestion);
  });

  // Handle focus and blur events
  const handleFocus = () => {
    setIsFocused(true);
    updateFilteredSuggestions();
    setShowSuggestions(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  // Update filtered suggestions based on current input
  const updateFilteredSuggestions = (newValue = inputValue) => {
    // Get the current word being typed
    const cursorPos = inputRef.current?.selectionStart || cursorPosition;
    const textBeforeCursor = newValue.substring(0, cursorPos);

    // Find the start of the current term
    const lastSpaceBeforeCursor = textBeforeCursor.lastIndexOf(' ');
    const currentTermStart = lastSpaceBeforeCursor === -1 ? 0 : lastSpaceBeforeCursor + 1;

    // Extract the current term being typed
    const currentTerm = textBeforeCursor.substring(currentTermStart);

    // If we're at the end of a word that was just completed, we want to show suggestions for the next word
    const isAtEndOfWord = cursorPos === textBeforeCursor.length &&
                         textBeforeCursor.endsWith(' ');

    // Define syntax suggestions
    const syntaxSuggestions = [
      { type: 'operator', value: 'AND', display: 'AND' },
      { type: 'operator', value: 'OR', display: 'OR' },
      { type: 'operator', value: 'NOT', display: 'NOT' },
      { type: 'syntax', value: '\"\"', display: '\"\"' }, // For exact match quotes
      { type: 'syntax', value: '()', display: '()' }   // For grouping
    ];

    // Check if we're typing a filter (e.g., "lob:")
    const colonIndex = currentTerm.indexOf(':');

    if (colonIndex !== -1) {
      // We're typing a value for a filter
      const filterType = currentTerm.substring(0, colonIndex);
      const filterValue = currentTerm.substring(colonIndex + 1);

      // Filter suggestions by type and value
      const filtered = suggestions.filter(suggestion =>
        suggestion.type === filterType &&
        suggestion.value.toLowerCase().includes(filterValue.toLowerCase())
      );

      setFilteredSuggestions(filtered);
    } else {
      // Check if we're at the beginning of a term, after a space, or at the end of a word
      const isStartOfTerm = currentTermStart === cursorPos || currentTerm === '' || isAtEndOfWord;

      // If we're at the start of a term and there's already content, suggest operators
      if ((isStartOfTerm || currentTerm === '') && textBeforeCursor.trim() !== '') {
        // Check if there's already a filter in the query and no separator word (AND, OR, NOT)
        const hasFilterWithoutSeparator = textBeforeCursor.includes(':') &&
          !/(AND|OR|NOT)\s*$/i.test(textBeforeCursor.trim());

        // Only show operators if we don't have a filter without a separator
        const operatorSuggestions = hasFilterWithoutSeparator ?
          syntaxSuggestions.filter(s =>
            s.type === 'operator' &&
            (currentTerm === '' || s.value.toLowerCase().startsWith(currentTerm.toLowerCase()))
          ) : [];

        // Only include filter type suggestions if we don't have a filter without a separator
        // or if we're explicitly typing a filter name
        const filterSuggestions = !hasFilterWithoutSeparator || currentTerm !== '' ?
          suggestions.filter(suggestion =>
            suggestion.value === '' && // Only include filter types, not values
            (currentTerm === '' || suggestion.type.toLowerCase().includes(currentTerm.toLowerCase()))
          ).map(type => ({
            type: type.type,
            value: '',
            display: `${type.type}:`
          })) : [];

        setFilteredSuggestions([...operatorSuggestions, ...filterSuggestions]);
      } else {
        // We're typing a filter name or continuing a term
        const filtered = suggestions.filter(suggestion =>
          suggestion.type.toLowerCase().includes(currentTerm.toLowerCase())
        );

        // Only show unique filter types
        const uniqueTypes = Array.from(new Set(filtered.map(s => s.type)))
          .map(type => ({
            type: type,
            value: '',
            display: `${type}:`
          }));

        setFilteredSuggestions(uniqueTypes);
      }
    }

    setActiveSuggestionIndex(-1);
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: Suggestion) => {
    // Get the current word being typed
    const cursorPos = cursorPosition;
    const textBeforeCursor = inputValue.substring(0, cursorPos);
    const textAfterCursor = inputValue.substring(cursorPos);

    // Find the start of the current term
    const lastSpaceBeforeCursor = textBeforeCursor.lastIndexOf(' ');
    const currentTermStart = lastSpaceBeforeCursor === -1 ? 0 : lastSpaceBeforeCursor + 1;

    // Replace the current term with the suggestion
    let newValue;
    let newCursorPos;

    if (suggestion.type === 'operator') {
      // If it's an operator, add it with spaces around it
      const prefix = currentTermStart > 0 && textBeforeCursor[currentTermStart - 1] !== ' ' ? ' ' : '';
      const suffix = textAfterCursor.length > 0 && textAfterCursor[0] !== ' ' ? ' ' : '';

      newValue = textBeforeCursor.substring(0, currentTermStart) +
                prefix + suggestion.value + suffix +
                textAfterCursor;

      // Position cursor after the operator and space
      newCursorPos = currentTermStart + prefix.length + suggestion.value.length + suffix.length;
    } else if (suggestion.type === 'syntax') {
      // Special handling for syntax elements
      if (suggestion.value === '\"\"') {
        // For quotes, place cursor between the quotes
        newValue = textBeforeCursor.substring(0, currentTermStart) +
                  '\"\"' +
                  textAfterCursor;
        newCursorPos = currentTermStart + 1;
      } else if (suggestion.value === '()') {
        // For parentheses, place cursor between them
        newValue = textBeforeCursor.substring(0, currentTermStart) +
                  '()' +
                  textAfterCursor;
        newCursorPos = currentTermStart + 1;
      } else {
        // Default handling for other syntax elements
        newValue = textBeforeCursor.substring(0, currentTermStart) +
                  suggestion.value +
                  textAfterCursor;
        newCursorPos = currentTermStart + suggestion.value.length;
      }
    } else if (suggestion.value) {
      // If it's a value suggestion
      const colonIndex = textBeforeCursor.indexOf(':', currentTermStart);
      if (colonIndex !== -1) {
        const filterType = textBeforeCursor.substring(currentTermStart, colonIndex + 1);
        newValue = textBeforeCursor.substring(0, currentTermStart) +
                  filterType + suggestion.value +
                  textAfterCursor;
        newCursorPos = currentTermStart + filterType.length + suggestion.value.length;
      } else {
        // Fallback if no colon is found
        newValue = textBeforeCursor.substring(0, currentTermStart) +
                  suggestion.value +
                  textAfterCursor;
        newCursorPos = currentTermStart + suggestion.value.length;
      }
    } else {
      // If it's a filter type suggestion
      newValue = textBeforeCursor.substring(0, currentTermStart) +
                suggestion.display +
                textAfterCursor;
      newCursorPos = currentTermStart + suggestion.display.length;
    }

    // Update input value
    setInputValue(newValue);

    // Call onChange if provided (for backward compatibility)
    if (onChange) {
      onChange(newValue);
    }

    // No auto-search - search will only be triggered on Enter key press

    setShowSuggestions(false);

    // Focus the input and set cursor position after the inserted suggestion
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(newCursorPos, newCursorPos);
        setCursorPosition(newCursorPos);

        // Reset suggestion state and trigger a new suggestion update
        setShowSuggestions(true);
        updateFilteredSuggestions(newValue);
      }
    }, 10);
  };

  // Handle input changes without auto-search
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart || 0;

    // Update the input value immediately for UI responsiveness
    setInputValue(value);

    // Store cursor position
    setCursorPosition(cursorPos);

    // Update suggestions
    updateFilteredSuggestions(value);

    // Always show suggestions when typing
    if (value.trim() !== '') {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }

    // Call onChange if provided (for backward compatibility)
    // Do this after all other state updates to avoid unnecessary re-renders
    if (onChange) {
      // Use setTimeout to ensure this happens after the current event loop
      // This helps maintain focus by delaying the parent component re-render
      setTimeout(() => {
        onChange(value);
      }, 0);
    }

    // No auto-search - search will only be triggered on Enter key press
  };

  // Create a ref for the suggestions container
  const suggestionsContainerRef = useRef<HTMLDivElement>(null);

  // Effect to scroll active suggestion into view
  useEffect(() => {
    if (activeSuggestionIndex >= 0 && suggestionsContainerRef.current) {
      const activeElement = suggestionsContainerRef.current.querySelector(`[data-index="${activeSuggestionIndex}"]`);
      if (activeElement) {
        activeElement.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
      }
    }
  }, [activeSuggestionIndex]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Store cursor position
    const cursorPos = e.currentTarget.selectionStart || 0;
    setCursorPosition(cursorPos);

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setActiveSuggestionIndex(prev =>
        prev < filteredSuggestions.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setActiveSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Enter' && activeSuggestionIndex >= 0) {
      e.preventDefault();
      handleSuggestionClick(filteredSuggestions[activeSuggestionIndex]);
    } else if (e.key === 'Enter') {
      // Store current selection position
      const selectionStart = e.currentTarget.selectionStart;
      const selectionEnd = e.currentTarget.selectionEnd;

      // Call search immediately
      onSearch(inputValue);

      // Prevent default behavior
      e.preventDefault();

      // Ensure focus is maintained
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          // Restore cursor position
          if (selectionStart !== null && selectionEnd !== null) {
            inputRef.current.setSelectionRange(selectionStart, selectionEnd);
          }
        }
      }, 0);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    } else if (e.key === ' ') {
      // When space is pressed, we might want to show suggestions for the next word
      // We'll let the default space behavior happen, then update suggestions
      setTimeout(() => {
        if (inputRef.current) {
          const newValue = inputRef.current.value;
          const newCursorPos = inputRef.current.selectionStart || 0;
          setCursorPosition(newCursorPos);
          updateFilteredSuggestions(newValue);
          setShowSuggestions(true);
        }
      }, 10);
    }
  };

  // Clean up the timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <div className={`relative w-full ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          ref={(el) => {
            // Set the forwarded ref
            if (typeof ref === 'function') {
              ref(el);
            } else if (ref) {
              ref.current = el;
            }
            // Also set our internal ref
            if (el) {
              // Use non-null assertion to tell TypeScript this is safe
              (inputRef as React.MutableRefObject<HTMLInputElement | null>).current = el;
            }
          }}
          type="text"
          placeholder={placeholder}
          className="w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          autoComplete="off"
          autoFocus={autoFocus}
          data-focused={isFocused}
        />
        {inputValue && (
          <button
            type="button"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            onClick={() => {
              setInputValue('');
              // Don't trigger search automatically
              if (inputRef.current) {
                inputRef.current.focus();
              }
            }}
          >
            <X className="h-4 w-4" />
          </button>
        )}

        {helpContent && (
          <div className="absolute right-10 top-1/2 transform -translate-y-1/2">
            <div className="relative inline-block">
              <button
                type="button"
                className="text-gray-400 hover:text-gray-600 focus:outline-none"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <HelpCircle className="h-4 w-4" />
              </button>
              <div className="absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 hidden group-hover:block">
                {helpContent}
              </div>
            </div>
          </div>
        )}
      </div>

      {showSuggestions && filteredSuggestions.length > 0 && (
        <div
          ref={suggestionsContainerRef}
          className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
          {/* Special section for operators and syntax */}
          {filteredSuggestions.some(s => s.type === 'operator' || s.type === 'syntax') && (
            <div className="p-1">
              <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 px-2 py-1">
                QUERY SYNTAX
              </div>
              <div className="grid grid-cols-2 gap-1">
                {filteredSuggestions
                  .filter(s => s.type === 'operator' || s.type === 'syntax')
                  .map((suggestion) => {
                    const suggestionIndex = filteredSuggestions.findIndex(
                      fs => fs.type === suggestion.type && fs.value === suggestion.value
                    );
                    const isActive = suggestionIndex === activeSuggestionIndex;

                    return (
                      <div
                        key={`${suggestion.type}-${suggestion.value}`}
                        data-index={suggestionIndex}
                        className={`px-3 py-2 cursor-pointer flex items-center ${
                          isActive ? 'bg-gray-100 dark:bg-gray-700' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                        }`}
                        onClick={() => handleSuggestionClick(suggestion)}
                      >
                        <span className="font-mono">{suggestion.display}</span>
                        <span className="ml-2 text-xs text-gray-500">
                          {suggestion.type === 'operator' ? 'operator' : 'syntax'}
                        </span>
                      </div>
                    );
                  })}
              </div>
            </div>
          )}

          {/* Regular filter suggestions */}
          {Object.entries(groupedSuggestions).map(([type, typeSuggestions]) => {
            // Skip operator and syntax types as they're handled separately
            if (type === 'operator' || type === 'syntax') return null;

            // Only show this type if we have matching suggestions
            const matchingSuggestions = typeSuggestions.filter(s =>
              filteredSuggestions.some(fs => fs.type === s.type && fs.value === s.value)
            );

            if (matchingSuggestions.length === 0) return null;

            return (
              <div key={type} className="p-1">
                <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 px-2 py-1">
                  {type.toUpperCase()}
                </div>
                {matchingSuggestions.map((suggestion) => {
                  const suggestionIndex = filteredSuggestions.findIndex(
                    fs => fs.type === suggestion.type && fs.value === suggestion.value
                  );
                  const isActive = suggestionIndex === activeSuggestionIndex;

                  return (
                    <div
                      key={`${suggestion.type}-${suggestion.value}`}
                      data-index={suggestionIndex}
                      className={`px-3 py-2 cursor-pointer ${
                        isActive ? 'bg-gray-100 dark:bg-gray-700' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion.display}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
});

export default SearchInputWithSuggestions;
