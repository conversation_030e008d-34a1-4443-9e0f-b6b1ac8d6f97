package search

import (
	"strings"
)

// FixParentheses ensures that parentheses in the query are balanced
func FixParentheses(query string) string {
	// Count opening and closing parentheses
	openCount := strings.Count(query, "(")
	closeCount := strings.Count(query, ")")

	// Add missing closing parentheses
	for i := 0; i < openCount-closeCount; i++ {
		query += ")"
	}

	// Add missing opening parentheses at the beginning
	for i := 0; i < closeCount-openCount; i++ {
		query = "(" + query
	}

	return query
}

// CleanQuery removes special type filters and trims spaces
func CleanQuery(query string, entityType string) string {
	// Remove type filter from the query if present
	typeFilter := "_type:" + entityType
	cleanQuery := strings.ReplaceAll(query, "AND "+typeFilter, "")
	cleanQuery = strings.ReplaceAll(cleanQuery, typeFilter, "")
	cleanQuery = strings.TrimSpace(cleanQuery)
	
	return cleanQuery
}

// IsSimpleKeywordSearch checks if a query is a simple keyword search without special syntax
func IsSimpleKeywordSearch(query string) bool {
	return !strings.Contains(query, ":") && 
		!strings.Contains(query, " AND ") &&
		!strings.Contains(query, " OR ") && 
		!strings.Contains(query, " NOT ")
}
