package search

import (
	"adgitops-ui/src/backend/models"
	"context"
)

// Service provides search functionality
type Service struct {
	engine *Engine
	parser *Parser
}

// NewService creates a new search service
func NewService() *Service {
	return &Service{
		engine: NewEngine(),
		parser: NewParser(),
	}
}

// ParseQuery parses a search query string into a structured query
func (s *Service) ParseQuery(query string) (*SearchNode, error) {
	return s.parser.ParseQuery(query)
}

// ParseQueryToMap parses a search query into a map format for backward compatibility
func (s *Service) ParseQueryToMap(query string) (map[string][]string, error) {
	return s.parser.ParseQueryToMap(query)
}

// SearchGroups searches for groups matching the query
func (s *Service) SearchGroups(ctx context.Context, query string, groups []models.Group) ([]models.Group, error) {
	return s.engine.SearchGroups(ctx, query, groups)
}

// SearchUsers searches for users matching the query
func (s *Service) SearchUsers(ctx context.Context, query string, users []models.User) ([]models.User, error) {
	return s.engine.SearchUsers(ctx, query, users)
}

// MatchesAdvancedSearch checks if a group matches the search criteria (for backward compatibility)
func (s *Service) MatchesAdvancedSearch(group models.Group, searchCriteria map[string][]string) bool {
	// Convert the map to a search node
	node := s.mapToNode(searchCriteria)

	// Match the group
	return s.engine.matchesGroup(node, group)
}

// MatchesAdvancedUserSearch checks if a user matches the search criteria (for backward compatibility)
func (s *Service) MatchesAdvancedUserSearch(user models.User, searchCriteria map[string][]string) bool {
	// Convert the map to a search node
	node := s.mapToNode(searchCriteria)

	// Match the user
	return s.engine.matchesUser(node, user)
}

// mapToNode converts a search criteria map to a search node
func (s *Service) mapToNode(searchCriteria map[string][]string) *SearchNode {
	if len(searchCriteria) == 0 || (len(searchCriteria) == 1 && len(searchCriteria["_any"]) == 0) {
		return nil
	}

	var node *SearchNode

	// Process each field
	for field, values := range searchCriteria {
		if field == "_any" || field == "_exact" {
			continue
		}

		for _, value := range values {
			operator := "="
			searchValue := value

			if len(value) > 0 {
				switch value[0] {
				case '=':
					operator = "="
					searchValue = value[1:]
				case '~':
					operator = "~"
					searchValue = value[1:]
				case '^':
					operator = "^"
					searchValue = value[1:]
				}
			}

			termNode := NewTermNode(field, operator, searchValue, false)
			if node == nil {
				node = termNode
			} else {
				node = NewAndNode(node, termNode)
			}
		}
	}

	// Process _exact
	if exacts, ok := searchCriteria["_exact"]; ok && len(exacts) > 0 {
		for _, exact := range exacts {
			termNode := NewTermNode("_any", "=", exact, true)
			if node == nil {
				node = termNode
			} else {
				node = NewAndNode(node, termNode)
			}
		}
	}

	// Process _any
	if anys, ok := searchCriteria["_any"]; ok && len(anys) > 0 {
		for _, any := range anys {
			operator := "~"
			searchValue := any

			// Check for wildcard
			if len(any) > 0 && any[len(any)-1] == '*' {
				operator = "^"
				searchValue = any[:len(any)-1]
			}

			termNode := NewTermNode("_any", operator, searchValue, false)
			if node == nil {
				node = termNode
			} else {
				node = NewAndNode(node, termNode)
			}
		}
	}

	return node
}
