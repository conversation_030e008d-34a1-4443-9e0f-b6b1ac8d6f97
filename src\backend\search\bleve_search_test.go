package search

import (
	"adgitops-ui/src/backend/models"
	"context"
	"os"
	"path/filepath"
	"testing"
)

func TestBleveSearchService(t *testing.T) {
	// Create a temporary directory for the index
	tempDir, err := os.MkdirTemp("", "bleve-test")
	if err != nil {
		t.Fatalf("Failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a new search service
	indexPath := filepath.Join(tempDir, "test-index")
	service, err := NewBleveSearchService(indexPath)
	if err != nil {
		t.Fatalf("Failed to create search service: %v", err)
	}
	defer service.Close()

	// Create test data
	groups := []models.Group{
		{
			Groupname:   "admin_group",
			Type:        "security",
			Description: "Administrators for IT systems",
			Lob:         "it",
			Members: []models.Member{
				{Name: "john", Type: "user"},
				{Name: "jane", Type: "user"},
			},
			SourceFile: "it/admin_group.json",
		},
		{
			Groupname:   "dev_team",
			Type:        "technical",
			Description: "Developers for finance applications",
			Lob:         "finance",
			Members: []models.Member{
				{Name: "alice", Type: "user"},
				{Name: "bob", Type: "user"},
			},
			SourceFile: "finance/dev_team.json",
		},
		{
			Groupname:   "harbor_administrators",
			Type:        "org_group",
			Description: "Managed by Puppet",
			Lob:         "cloud",
			Members:     []models.Member{},
			SourceFile:  "cloud/harbor_administrators.json",
		},
		{
			Groupname:   "harbor_users",
			Type:        "org_group",
			Description: "Harbor registry users",
			Lob:         "fm",
			Members: []models.Member{
				{Name: "john", Type: "user"},
				{Name: "alice", Type: "user"},
			},
			SourceFile: "fm/harbor_users.json",
		},
		{
			Groupname:   "security_team",
			Type:        "security",
			Description: "Security team for cloud infrastructure",
			Lob:         "cloud",
			Members: []models.Member{
				{Name: "bob", Type: "user"},
				{Name: "charlie", Type: "user"},
			},
			SourceFile: "cloud/security_team.json",
		},
		{
			Groupname:   "finance_admins",
			Type:        "admin",
			Description: "Finance system administrators",
			Lob:         "finance",
			Members: []models.Member{
				{Name: "dave", Type: "user"},
				{Name: "eve", Type: "user"},
			},
			SourceFile: "finance/finance_admins.json",
		},
	}

	users := []models.User{
		{
			Name:   "john",
			Groups: []string{"admin_group", "harbor_users"},
			LOBs:   []string{"it", "fm"},
		},
		{
			Name:   "jane",
			Groups: []string{"admin_group"},
			LOBs:   []string{"it"},
		},
		{
			Name:   "alice",
			Groups: []string{"dev_team", "harbor_users"},
			LOBs:   []string{"finance", "fm"},
		},
		{
			Name:   "bob",
			Groups: []string{"dev_team", "security_team"},
			LOBs:   []string{"finance", "cloud"},
		},
		{
			Name:   "charlie",
			Groups: []string{"security_team"},
			LOBs:   []string{"cloud"},
		},
	}

	// Index the test data
	ctx := context.Background()
	if err := service.IndexGroups(ctx, groups); err != nil {
		t.Fatalf("Failed to index groups: %v", err)
	}
	if err := service.IndexUsers(ctx, users); err != nil {
		t.Fatalf("Failed to index users: %v", err)
	}

	// Verify indexing
	stats, err := service.GetIndexStats("")
	if err != nil {
		t.Fatalf("Failed to get index stats: %v", err)
	}
	t.Logf("Index stats: %+v", stats)

	// Test cases for group search
	groupTestCases := []struct {
		name     string
		query    string
		expected int
	}{
		// Basic search tests
		{"Empty query", "", 6},
		{"Simple match", "admin", 3}, // admin_group, finance_admins, and harbor_administrators

		// Field-specific search tests
		{"Field match - exact", "type:security", 2},            // admin_group and security_team
		{"Field match - case insensitive", "type:SECURITY", 2}, // admin_group and security_team
		{"Field match - lob", "lob:finance", 2},                // dev_team and finance_admins

		// Wildcard search tests
		{"Wildcard prefix", "groupname:admin*", 1},        // admin_group
		{"Wildcard suffix", "groupname:*team", 2},         // dev_team and security_team
		{"Wildcard contains", "groupname:*harbor*", 2},    // harbor_administrators and harbor_users
		{"Contains match with ~", "groupname:~harbor", 2}, // harbor_administrators and harbor_users

		// Logical operator tests
		{"AND operator", "type:security AND lob:it", 1},                    // admin_group
		{"OR operator", "lob:it OR lob:finance", 3},                        // admin_group, dev_team, finance_admins
		{"NOT operator", "NOT lob:cloud", 4},                               // admin_group, dev_team, harbor_users, finance_admins
		{"NOT operator with field", "groupname:*harbor* NOT lob:cloud", 1}, // harbor_users

		// Complex query tests
		{"Complex query - AND/OR", "(type:security OR type:technical) AND NOT lob:cloud", 2},      // admin_group, dev_team
		{"Complex query - multiple fields", "lob:finance AND type:technical OR type:admin", 2},    // dev_team and finance_admins
		{"Complex query - wildcards and operators", "groupname:*admin* AND NOT type:security", 2}, // harbor_administrators and finance_admins

		// Exact match with quotes
		{"Exact match with quotes", "lob:\"cloud\"", 2},                                        // harbor_administrators and security_team
		{"Exact match with quotes in description", "description:\"Harbor registry users\"", 1}, // harbor_users

		// Exact groupname match tests
		{"Exact groupname match - admin_group", "groupname:admin_group", 1},               // only admin_group
		{"Exact groupname match - dev_team", "groupname:dev_team", 1},                     // only dev_team
		{"Exact groupname match - harbor_users", "groupname:harbor_users", 1},             // only harbor_users
		{"Exact groupname match with quotes", "groupname:\"admin_group\"", 1},             // only admin_group
		{"Exact groupname match with quotes 2", "groupname:\"harbor_administrators\"", 1}, // only harbor_administrators

		// Member search tests
		{"Member search", "members:john", 2},                          // admin_group and harbor_users
		{"Member search with wildcard", "members:*ice", 2},            // dev_team and harbor_users (alice)
		{"Member search with wildcard both sides", "members:*oh*", 2}, // admin_group and harbor_users (john)
		{"Member search with partial name", "members:cas", 0},         // should return no results

		// No matches
		{"No matches", "groupname:nonexistent", 0},
	}

	// Run the tests
	// t.Skip("Skipping test until we fix the search implementation")

	for _, tc := range groupTestCases {
		t.Run(tc.name, func(t *testing.T) {
			// Add debug output
			t.Logf("Running test case: %s with query: %s", tc.name, tc.query)

			// Dump the index contents
			t.Logf("Index contents:")
			for _, group := range groups {
				t.Logf("Group: %s, Type: %s, LOB: %s", group.Groupname, group.Type, group.Lob)
			}

			result, err := service.SearchGroups(ctx, tc.query, groups)
			if err != nil {
				t.Fatalf("Failed to search groups: %v", err)
			}
			t.Logf("Got %d results", len(result))
			for _, r := range result {
				t.Logf("Result: %s, Type: %s, LOB: %s", r.Groupname, r.Type, r.Lob)
			}
			if len(result) != tc.expected {
				t.Errorf("Expected %d results, got %d for query: %s", tc.expected, len(result), tc.query)
			}
		})
	}

	// Test cases for user search
	userTestCases := []struct {
		name     string
		query    string
		expected int
	}{
		// Basic search tests
		{"Empty query", "", 5},
		{"Simple match", "john", 1}, // Exact match on name as per documentation

		// Field-specific search tests
		{"Field match - exact", "groups:admin_group", 2},            // john and jane
		{"Field match - case insensitive", "groups:ADMIN_GROUP", 2}, // john and jane
		{"Field match - lobs", "lobs:finance", 2},                   // alice and bob

		// Wildcard search tests
		{"Wildcard prefix", "name:j*", 2},        // john and jane
		{"Wildcard suffix", "name:*e", 3},        // jane, alice, and charlie
		{"Wildcard contains", "name:*li*", 2},    // alice and charlie
		{"Contains match with ~", "name:~li", 2}, // alice and charlie

		// Logical operator tests
		{"AND operator", "groups:admin_group AND lobs:it", 2},            // john and jane
		{"OR operator", "name:john OR name:jane", 2},                     // john and jane
		{"NOT operator", "NOT lobs:it", 3},                               // alice, bob, charlie
		{"NOT operator with field", "groups:dev_team NOT name:alice", 1}, // bob

		// Complex query tests
		{"Complex query - AND/OR", "(name:john OR name:jane) AND lobs:it", 2},                                 // john and jane
		{"Complex query - multiple fields", "lobs:finance AND (groups:dev_team OR groups:finance_admins)", 2}, // alice and bob
		{"Complex query - wildcards and operators", "name:*a* AND NOT lobs:it", 2},                            // alice and charlie

		// Exact match with quotes
		{"Exact match with quotes", "lobs:\"it\"", 2},                      // john and jane
		{"Exact match with quotes in groups", "groups:\"admin_group\"", 2}, // john and jane

		// Exact name match tests
		{"Exact name match - john", "name:john", 1},             // only john
		{"Exact name match - jane", "name:jane", 1},             // only jane
		{"Exact name match - alice", "name:alice", 1},           // only alice
		{"Exact name match with quotes", "name:\"john\"", 1},    // only john
		{"Exact name match with quotes 2", "name:\"alice\"", 1}, // only alice

		// No matches
		{"No matches", "name:nonexistent", 0},
	}

	for _, tc := range userTestCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := service.SearchUsers(ctx, tc.query, users)
			if err != nil {
				t.Fatalf("Failed to search users: %v", err)
			}
			if len(result) != tc.expected {
				t.Errorf("Expected %d results, got %d for query: %s", tc.expected, len(result), tc.query)
			}
		})
	}

	// Test reindexing
	if err := service.Reindex(ctx, groups, users); err != nil {
		t.Fatalf("Failed to reindex: %v", err)
	}

	// Test getting index stats
	stats2, err2 := service.GetIndexStats("")
	if err2 != nil {
		t.Fatalf("Failed to get index stats: %v", err2)
	}
	var docCount int
	switch v := stats2["doc_count"].(type) {
	case int:
		docCount = v
	case uint64:
		docCount = int(v)
	default:
		t.Fatalf("doc_count is not an int or uint64: %T", stats2["doc_count"])
	}

	if docCount != 11 { // 6 groups + 5 users
		t.Errorf("Expected 11 documents, got %d", docCount)
	}
}
