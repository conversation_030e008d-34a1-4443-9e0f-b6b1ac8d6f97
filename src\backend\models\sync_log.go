package models

import "time"

// LogLevel defines the severity level of a log entry
type LogLevel string

const (
	LogLevelInfo    LogLevel = "info"
	LogLevelWarning LogLevel = "warning"
	LogLevelError   LogLevel = "error"
	LogLevelSuccess LogLevel = "success"
)

// SyncLogEntry represents a single log entry for repository sync operations
type SyncLogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
	Level     LogLevel  `json:"level"`
	RepoID    string    `json:"repoId"`
}

// SyncLogResponse is the response format for sync log API calls
type SyncLogResponse struct {
	Logs []SyncLogEntry `json:"logs"`
}
