package search

// Token types for the search parser
const (
	// Special token types
	TokenUnknown = iota
	TokenAnd
	TokenOr
	TokenNot
	TokenOpenParen
	TokenCloseParen

	// Field specifiers
	TokenField
	TokenExactMatch    // field:value
	TokenContainsMatch // field:~value
	TokenPrefixMatch   // field:value*

	// String tokens
	TokenQuotedString // "exact phrase"

	// Value tokens
	TokenValue
)

// SearchToken represents a token in the search query
type SearchToken struct {
	Type  int
	Value string
	Field string // For field-specific tokens
}

// SearchNode represents a node in the search query AST
type NodeType int

const (
	NodeTerm NodeType = iota
	NodeAnd
	NodeOr
	NodeNot
)

// SearchNode represents a node in the search query AST
type SearchNode struct {
	Type      NodeType
	Field     string
	Operator  string // "=", "~", "^" (prefix)
	Value     string
	Left      *SearchNode
	Right     *SearchNode
	IsQuoted  bool
}

// NewTermNode creates a new term node
func NewTermNode(field, operator, value string, isQuoted bool) *SearchNode {
	return &SearchNode{
		Type:     NodeTerm,
		Field:    field,
		Operator: operator,
		Value:    value,
		IsQuoted: isQuoted,
	}
}

// NewAndNode creates a new AND node
func NewAndNode(left, right *SearchNode) *SearchNode {
	return &SearchNode{
		Type:  NodeAnd,
		Left:  left,
		Right: right,
	}
}

// NewOrNode creates a new OR node
func NewOrNode(left, right *SearchNode) *SearchNode {
	return &SearchNode{
		Type:  NodeOr,
		Left:  left,
		Right: right,
	}
}

// NewNotNode creates a new NOT node
func NewNotNode(right *SearchNode) *SearchNode {
	return &SearchNode{
		Type:  NodeNot,
		Right: right,
	}
}
