import { User } from './UserTypes';

// Process users data without relying on separate groups API call
export const processUsers = (apiUsers: any[]): User[] => {
  if (!apiUsers || !Array.isArray(apiUsers)) {
    return [];
  }

  // Create a set of group names from the users' groups
  // to help identify users that are actually groups
  const groupNames = new Set<string>();
  apiUsers.forEach(user => {
    if (user.Groups && Array.isArray(user.Groups)) {
      user.Groups.forEach((group: string) => {
        if (group) groupNames.add(group.toLowerCase());
      });
    } else if (user.groups && Array.isArray(user.groups)) {
      user.groups.forEach((group: string) => {
        if (group) groupNames.add(group.toLowerCase());
      });
    }

    // Also add the username itself to detect duplicates
    if (user.Username) {
      groupNames.add(user.Username.toLowerCase());
    } else if (user.username) {
      groupNames.add((user as any).username.toLowerCase());
    }
  });

  // Process user data
  const processedUsers = apiUsers
    .map(apiUser => {
      // Create normalized user object
      const user: User = {
        name: '',
        groups: [],
        groupLOBs: [],
        Lobs: [],
        description: ''
      };

      // Extract name
      if (typeof apiUser === 'object' && apiUser !== null) {
        if ('Username' in apiUser) user.name = apiUser.Username || '';
        else if ('username' in apiUser) user.name = (apiUser as any).username || '';
        else if ('Name' in apiUser) user.name = (apiUser as any).Name || '';
        else if ('name' in apiUser) user.name = (apiUser as any).name || '';

        // Extract groups
        if ('Groups' in apiUser && Array.isArray(apiUser.Groups)) {
          user.groups = apiUser.Groups;
        } else if ('groups' in apiUser && Array.isArray((apiUser as any).groups)) {
          user.groups = (apiUser as any).groups;
        }

        // Extract LOBs
        const userAny = apiUser as any;

        if (userAny.Lobs && Array.isArray(userAny.Lobs)) {
          user.Lobs = userAny.Lobs;
        } else if (userAny.lobs && Array.isArray(userAny.lobs)) {
          user.Lobs = userAny.lobs;
        } else if (userAny.LOB) {
          const lobValue = userAny.LOB;
          if (typeof lobValue === 'string' && lobValue.trim()) {
            user.Lobs = [lobValue.trim()];
          } else if (Array.isArray(lobValue)) {
            user.Lobs = lobValue.filter(Boolean);
          }
        } else if (userAny.Lob) {
          const lobValue = userAny.Lob;
          if (typeof lobValue === 'string' && lobValue.trim()) {
            user.Lobs = [lobValue.trim()];
          } else if (Array.isArray(lobValue)) {
            user.Lobs = lobValue.filter(Boolean);
          }
        } else if (userAny.lob) {
          const lobValue = userAny.lob;
          if (typeof lobValue === 'string' && lobValue.trim()) {
            user.Lobs = [lobValue.trim()];
          } else if (Array.isArray(lobValue)) {
            user.Lobs = lobValue.filter(Boolean);
          }
        }

        // Extract Description
        if (userAny.Description) {
          user.description = userAny.Description;
        } else if (userAny.description) {
          user.description = userAny.description;
        }
      }

      // We don't have group LOBs from a separate API call anymore,
      // so we'll assume user's direct LOBs for now
      user.groupLOBs = [];

      // Deduplicate LOBs
      const uniqueDirectLOBs: string[] = [];
      const seenDirectLOBs = new Set<string>();

      // Ensure user.Lobs is always an array
      const userLobs = user.Lobs || [];
      userLobs.forEach(lob => {
        if (!lob) return;
        const normalizedLob = lob.toLowerCase().trim();
        if (!seenDirectLOBs.has(normalizedLob)) {
          seenDirectLOBs.add(normalizedLob);
          uniqueDirectLOBs.push(lob);
        }
      });

      // Update the user with deduplicated LOBs
      user.Lobs = uniqueDirectLOBs;

      return { ...apiUser, ...user };
    })
    .filter(user => {
      // Filter out groups from user list

      // Skip empty names
      if (!user.name) return false;

      // Get normalized user name
      const normalizedName = user.name.toLowerCase();

      // Check if the user name appears in our list of group names
      // This suggests this "user" might actually be a group
      if (groupNames.has(normalizedName)) {
        // Only filter it out if it has multiple occurrences (is likely a group)
        const nameCount = apiUsers.filter(u =>
          (u.Username || u.username || u.Name || u.name || '').toLowerCase() === normalizedName
        ).length;

        if (nameCount > 1) {
          return false;
        }
      }

      // Check for variations with different separators
      const nameWithoutSeparators = normalizedName.replace(/[-_.\s]/g, '');
      const isDerivedFromGroup = Array.from(groupNames).some(groupName => {
        const normalizedGroupName = groupName.replace(/[-_.\s]/g, '');
        return nameWithoutSeparators === normalizedGroupName && normalizedName !== groupName;
      });

      if (isDerivedFromGroup) {
        return false;
      }

      // Special case for FM-Devops
      if (normalizedName === 'fm-devops' ||
          normalizedName === 'fmdevops' ||
          normalizedName === 'fm_devops') {
        return false;
      }

      return true;
    });

  return processedUsers;
};

// Extract LOBs from users
export const extractLobsFromUsers = (users: User[]): string[] => {
  const uniqueLobs = new Set<string>();

  users.forEach(user => {
    // Check all possible LOB field formats
    if (user.Lobs && Array.isArray(user.Lobs)) {
      user.Lobs.forEach(lob => {
        if (lob && typeof lob === 'string') {
          uniqueLobs.add(lob);
        }
      });
    }
  });

  return Array.from(uniqueLobs).sort();
};
