package services

import (
	"adgitops-ui/src/backend/models"
	"fmt"
	"sync"
	"time"
)

// SyncLogger is a service for managing repository sync logs
type SyncLogger struct {
	logs          map[string][]models.SyncLogEntry
	logsMu        sync.RWMutex
	maxLogEntries int
}

// NewSyncLogger creates a new sync logger service
func NewSyncLogger() *SyncLogger {
	return &SyncLogger{
		logs:          make(map[string][]models.SyncLogEntry),
		maxLogEntries: 100, // Keep last 100 log entries per repository
	}
}

// AddLog adds a new log entry for a repository
func (s *SyncLogger) AddLog(repoID, message string, level models.LogLevel) {
	s.logsMu.Lock()
	defer s.logsMu.Unlock()

	// Create a new log entry
	logEntry := models.SyncLogEntry{
		Timestamp: time.Now(),
		Message:   message,
		Level:     level,
		RepoID:    repoID,
	}

	// Add the log entry to the repository's logs
	if logs, ok := s.logs[repoID]; ok {
		// Add the new log entry
		s.logs[repoID] = append(logs, logEntry)

		// Trim logs if they exceed the maximum number
		if len(s.logs[repoID]) > s.maxLogEntries {
			// Keep only the most recent logs
			s.logs[repoID] = s.logs[repoID][len(s.logs[repoID])-s.maxLogEntries:]
		}
	} else {
		// No logs for this repository yet, create a new slice
		s.logs[repoID] = []models.SyncLogEntry{logEntry}
	}
}

// GetLogs returns all log entries for a repository
func (s *SyncLogger) GetLogs(repoID string) []models.SyncLogEntry {
	s.logsMu.RLock()
	defer s.logsMu.RUnlock()

	if logs, ok := s.logs[repoID]; ok {
		// Return a copy of the logs to avoid race conditions
		result := make([]models.SyncLogEntry, len(logs))
		copy(result, logs)
		return result
	}

	// No logs for this repository
	return []models.SyncLogEntry{}
}

// LogInitialSync logs the initial sync for a repository
func (s *SyncLogger) LogInitialSync(repoID, repoURL string) {
	s.AddLog(repoID, fmt.Sprintf("Starting initial sync for repository %s", repoURL), models.LogLevelInfo)
}

// LogSyncStart logs the start of a sync operation
func (s *SyncLogger) LogSyncStart(repoID string) {
	s.AddLog(repoID, "Starting repository synchronization", models.LogLevelInfo)
}

// LogSyncSuccess logs a successful sync
func (s *SyncLogger) LogSyncSuccess(repoID, commitID string, hasChanges bool) {
	if hasChanges {
		s.AddLog(repoID, fmt.Sprintf("Sync completed successfully with changes, commit: %s", commitID), models.LogLevelSuccess)
	} else {
		s.AddLog(repoID, "Sync completed successfully, no changes detected", models.LogLevelSuccess)
	}
}

// LogSyncError logs a sync failure
func (s *SyncLogger) LogSyncError(repoID string, err error) {
	s.AddLog(repoID, fmt.Sprintf("Sync failed: %v", err), models.LogLevelError)
}

// LogCloneSuccess logs a successful clone operation
func (s *SyncLogger) LogCloneSuccess(repoID string, path string) {
	s.AddLog(repoID, fmt.Sprintf("Repository cloned successfully to %s", path), models.LogLevelSuccess)
}

// LogCloneError logs a failed clone operation
func (s *SyncLogger) LogCloneError(repoID string, err error) {
	s.AddLog(repoID, fmt.Sprintf("Repository clone failed: %v", err), models.LogLevelError)
}

// LogPullSuccess logs a successful pull operation
func (s *SyncLogger) LogPullSuccess(repoID string) {
	s.AddLog(repoID, "Repository pull successful", models.LogLevelSuccess)
}

// LogPullError logs a failed pull operation
func (s *SyncLogger) LogPullError(repoID string, err error) {
	s.AddLog(repoID, fmt.Sprintf("Repository pull failed: %v", err), models.LogLevelError)
}

// LogPollingStart logs the start of repository polling
func (s *SyncLogger) LogPollingStart(repoID string, interval int) {
	s.AddLog(repoID, fmt.Sprintf("Started polling repository every %d seconds", interval), models.LogLevelInfo)
}

// LogPollingStop logs the end of repository polling
func (s *SyncLogger) LogPollingStop(repoID string) {
	s.AddLog(repoID, "Stopped polling repository", models.LogLevelInfo)
}
