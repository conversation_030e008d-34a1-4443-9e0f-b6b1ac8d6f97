# ADGitOps Extract

A web application for monitoring Git repositories (GitLab, Bitbucket), extracting user and group data, and providing powerful search, reporting, and export capabilities.

## Overview

ADGitOps Extract connects to Git repositories, processes user and group information, and provides a modern web interface for searching, analyzing, and exporting this data. It supports multiple repository types and offers advanced search functionality with a flexible query syntax.

## Key Features

- **Multi-Repository Support**: Connect to both GitLab and Bitbucket repositories
- **Advanced Search Engine**: Powerful search with support for logical operators, wildcards, and field-specific queries
- **Comprehensive Reporting**: Create, customize, and schedule reports for users and groups
- **Dashboard Analytics**: View repository status and statistics at a glance
- **Modern UI**: Built with React, Vite, and shadcn/ui for a responsive experience

## Project Structure

The application follows a client-server architecture:

- **Backend (Go)**: Handles repository integration, data processing, search functionality, and API endpoints
- **Frontend (React)**: Provides an intuitive user interface with advanced search and reporting capabilities

## Setup and Installation

### Prerequisites

- Go 1.20 or later
- Node.js 18 or later
- pnpm (recommended) or npm
- Git

### Quick Start with Runner

The application includes a unified runner for development, building, and starting the application:

```bash
# Development mode with hot-reload
runner.exe dev

# Build the application
runner.exe build

# Start the built application
runner.exe start
```

### Manual Setup

#### Backend

```bash
# Start the backend server in development mode
go run src/backend/main.go
```

Optional flags:
- `--port`: Server port (default: 8080)
- `--repos-dir`: Base directory for repository clones (default: "repos")
- `--data-dir`: Directory for data files (default: "data")
- `--configs-file`: Path to repositories configuration file (default: "configs/repositories.json")

#### Frontend

```bash
# Navigate to frontend directory
cd src/frontend

# Install dependencies
pnpm install

# Start development server
pnpm dev
```

## Search Functionality

The application features a powerful search engine with a flexible query syntax:

| Syntax | Description | Example |
|--------|-------------|---------|
| `field:value` | Exact match | `groupname:admins` |
| `field:~value` | Contains match | `groupname:~admin` |
| `field:value*` | Prefix match | `name:j*` |
| `"exact phrase"` | Match exact phrase | `"access control"` |
| Logical operators | AND, OR, NOT | `type:security AND lob:fm` |

## Reports and Scheduling

The reporting system allows you to:

- Create report presets with customizable parameters
- Generate reports for users, groups, or both
- Schedule automatic report generation
- Export data in JSON format
- Track report execution history

## Repository Management

The application supports multiple repository configurations:

- Connect to GitLab or Bitbucket repositories
- Configure polling frequency for automatic updates
- View repository status and synchronization logs
- Manually trigger repository synchronization

## API Endpoints

The backend provides RESTful API endpoints for all functionality:

- Repository management: `/api/repo/configurations`, `/api/repo/status/:id`
- Search: `/api/repo/:id/search/groups`, `/api/repo/:id/search/users`
- Reports: `/api/repo/:id/reports`, `/api/repo/:id/report-presets`
- Data export: `/api/repo/:id/reports/generate`

## Development

### Running in Development Mode

```bash
# Start both backend and frontend with hot-reload
runner.exe dev
```

### Building for Production

```bash
# Build the application with embedded frontend
runner.exe build
```

### Running Tests

```bash
# Run backend tests
go test ./src/backend/...

# Run search tests specifically
go test ./src/backend/search -v
```

## License

This project is proprietary and confidential.
