// User interface that matches the component's needs
export interface User {
  // Original API fields
  Username?: string;
  Groups?: string[];
  Lobs?: string[];
  Description?: string;

  // Normalized fields for UI consistency
  name: string;      // Maps to Username
  groups: string[];  // Maps to Groups
  groupLOBs: string[]; // Derived from Lobs and group LOBs
  description?: string; // User description
  [key: string]: any;
}

// Pagination information interface
export interface PaginationInfo {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

// Request parameters for fetching users
export interface UserRequestParams {
  repoId: string;
  page: number;
  pageSize: number;
  lob: string;
  search: string;
}
