import { HelpCircle } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const GroupSearchHelp = () => {
  return (
    <Popover>
      <PopoverTrigger>
        <HelpCircle className="h-4 w-4 text-gray-400 cursor-pointer" />
      </PopoverTrigger>
      <PopoverContent className="w-[80vw] max-w-[1200px]" side="bottom" align="center" sideOffset={5} avoidCollisions>
        <div className="p-3">
          <h3 className="text-2xs font-bold mb-0.5 border-b pb-0.5 text-blue-600">Search Syntax Guide</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
            <div className="bg-blue-50 rounded-lg p-2 border border-blue-200">
              <h4 className="font-medium text-blue-700 mb-0.5 text-7xs">Basic Search Syntax</h4>
              <ul className="space-y-1">
                <li className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">column:value</span>
                  <span className="text-8xs">Exact match within a specific column</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">column:~value</span>
                  <span className="text-8xs">Contains match (substring) within a column</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">column:value*</span>
                  <span className="text-8xs">Prefix match (starts with) within a column</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">"exact word"</span>
                  <span className="text-8xs">Match an exact word</span>
                </li>
              </ul>
            </div>

            <div className="bg-purple-50 rounded-lg p-2 border border-purple-200">
              <h4 className="font-medium text-purple-700 mb-0.5 text-7xs">Query Operators</h4>
              <ul className="space-y-1">
                <li className="flex items-start">
                  <span className="bg-purple-100 text-purple-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">AND</span>
                  <span className="text-8xs">Both terms must match (default operator)</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-purple-100 text-purple-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">OR</span>
                  <span className="text-8xs">Either term can match</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-purple-100 text-purple-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">NOT</span>
                  <span className="text-8xs">Exclude matches with this term</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-purple-100 text-purple-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">( )</span>
                  <span className="text-8xs">Group expressions for complex queries</span>
                </li>
              </ul>
            </div>

            <div className="bg-green-50 rounded-lg p-2 border border-green-200">
              <h4 className="font-medium text-green-700 mb-0.5 text-7xs">Searchable Columns</h4>
              <ul className="space-y-1">
                <li className="flex items-start">
                  <span className="bg-green-100 text-green-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">groupname:</span>
                  <span className="text-8xs">Group name</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-green-100 text-green-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">type:</span>
                  <span className="text-8xs">Group type</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-green-100 text-green-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">members:</span>
                  <span className="text-8xs">Group members</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-green-100 text-green-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">lob:</span>
                  <span className="text-8xs">Line of business</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-green-100 text-green-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">description:</span>
                  <span className="text-8xs">Group description</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-2 bg-yellow-50 rounded-lg p-1.5 border border-yellow-200">
            <h4 className="font-medium text-yellow-700 mb-0.5 text-7xs">Examples</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-1.5">
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">groupname:org</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find groups with name exactly "org"</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-6 flex items-center justify-center">
                  <code className="text-6xs leading-tight">groupname:~org</code>
                </div>
                <p className="text-6xs text-gray-600 leading-tight">Find groups with "org" anywhere in their name</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-6 flex items-center justify-center">
                  <code className="text-6xs leading-tight">groupname:org*</code>
                </div>
                <p className="text-6xs text-gray-600 leading-tight">Find groups with names starting with "org"</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-6 flex items-center justify-center">
                  <code className="text-6xs leading-tight">type:security</code>
                </div>
                <p className="text-6xs text-gray-600 leading-tight">Find security groups</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">members:john</code>
                </div>
                <p className="text-xs text-gray-600">Find groups with a member named "john"</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">lob:fm AND type:security</code>
                </div>
                <p className="text-xs text-gray-600">Find security groups in the FM LOB</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">type:security OR type:admin</code>
                </div>
                <p className="text-xs text-gray-600">Find groups that are either security or admin type</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">groupname:~admin NOT lob:fm</code>
                </div>
                <p className="text-xs text-gray-600">Find admin groups that are not in the FM LOB</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">"security" AND "admin"</code>
                </div>
                <p className="text-xs text-gray-600">Find groups with both exact words "security" and "admin"</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">members:john AND type:security</code>
                </div>
                <p className="text-xs text-gray-600">Find security groups that have a member named john</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">description:~"access control"</code>
                </div>
                <p className="text-xs text-gray-600">Find groups with "access control" in their description</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">groupname:* NOT members:admin</code>
                </div>
                <p className="text-xs text-gray-600">Find all groups that don't have a member named admin</p>
              </div>
              <div>
                <div className="bg-white p-2 rounded border border-yellow-300 mb-1">
                  <code className="text-sm">(type:security OR type:admin) AND lob:fm</code>
                </div>
                <p className="text-xs text-gray-600">Find security or admin groups in the FM line of business</p>
              </div>
            </div>
          </div>

          <div className="mt-1 text-9xs text-gray-500 italic border-t pt-0.5">
            <p className="leading-tight">Tip: All searches are case-insensitive. By default, column-specific searches (column:value) use exact matching. Use column:~value for substring matching or column:value* for prefix matching.</p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default GroupSearchHelp;
