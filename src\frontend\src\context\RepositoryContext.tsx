import { createContext, useContext, useState, ReactNode, useEffect, useCallback, useRef } from 'react';
import { apiClient, RepositoryType } from '@/api/client';
import { useToast } from '@/components/ui/use-toast';

// Define a type for the repository change callback
type RepositoryChangeCallback = (newRepoId: string, prevRepoId: string) => void;

interface RepositoryContextType {
  selectedRepoId: string;
  setSelectedRepoId: (id: string) => void;
  isLoading: boolean;
  repositories: Array<{
    id: string;
    name: string;
    isActive: boolean;
    type: RepositoryType;
  }>;
  duplicateRepository: (id: string) => Promise<void>;
  refreshRepositories: (forceRefresh?: boolean) => Promise<boolean>;
  // Add methods to register and unregister repository change callbacks
  onRepositoryChange: (callback: RepositoryChangeCallback) => () => void;
}

const RepositoryContext = createContext<RepositoryContextType | undefined>(undefined);

interface RepositoryProviderProps {
  children: ReactNode;
}

export function RepositoryProvider({ children }: RepositoryProviderProps) {
  // Try to load the last selected repo ID from localStorage
  const savedRepoId = localStorage.getItem('selectedRepoId') || '';
  const [selectedRepoId, setSelectedRepoId] = useState<string>(savedRepoId);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [repositories, setRepositories] = useState<Array<{
    id: string;
    name: string;
    isActive: boolean;
    type: RepositoryType;
  }>>([]);
  const { toast } = useToast();

  // Use refs to track state and prevent excessive API calls
  const initialLoadComplete = useRef(false);
  const lastRefreshTime = useRef<number>(0);
  const refreshTimeoutId = useRef<NodeJS.Timeout | null>(null);
  const isMounted = useRef(true);

  // Store repository change callbacks
  const repositoryChangeCallbacks = useRef<Set<RepositoryChangeCallback>>(new Set());

  // Clean up on unmount
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
      if (refreshTimeoutId.current) {
        clearTimeout(refreshTimeoutId.current);
      }
    };
  }, []);

  // Change repository function that also updates localStorage
  const changeRepository = useCallback((id: string) => {
    // Store the previous ID before updating
    const prevId = selectedRepoId;

    // Update the selected repository ID
    setSelectedRepoId(id);

    // Update localStorage
    if (id) {
      localStorage.setItem('selectedRepoId', id);
    } else {
      localStorage.removeItem('selectedRepoId');
    }

    // Notify all registered callbacks about the repository change
    if (id !== prevId) {
      repositoryChangeCallbacks.current.forEach(callback => {
        try {
          callback(id, prevId);
        } catch (error) {
          console.error('Error in repository change callback:', error);
        }
      });
    }
  }, [selectedRepoId]);

  // Function to duplicate a repository configuration
  const duplicateRepository = useCallback(async (id: string) => {
    try {
      setIsLoading(true);

      // Find the repository to duplicate
      const repToDuplicate = repositories.find(repo => repo.id === id);
      if (!repToDuplicate) {
        throw new Error("Repository not found");
      }

      // Create a new config based on the existing one
      // Get the full configuration from the API
      const { config: fullConfig } = await apiClient.repositories.getConfiguration(id);

      // Create a new config based on the existing one
      const newConfig = {
        ...fullConfig,
        id: undefined, // Let the server generate a new ID
        name: `${repToDuplicate.name} (Copy)`,
      };

      // Call the API to add the new repository
      const response = await apiClient.repositories.addConfiguration(newConfig);

      // Show success toast
      toast({
        title: 'Repository Duplicated',
        description: `Created "${response.config.name}" from "${repToDuplicate.name}"`,
      });

      // Refresh the repository list
      const data = await apiClient.repositories.getConfigurations();
      setRepositories(
        data.configs.map(config => ({
          id: config.id || '',
          name: config.name || '',
          isActive: config.isActive || false, // lowercase to match backend model
          type: config.type || 'gitlab'
        }))
      );

    } catch (error) {
      toast({
        title: 'Failed to duplicate repository',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [repositories, toast]);

  // Function to refresh the repository list with throttling
  const refreshRepositories = useCallback(async (forceRefresh = false) => {
    // Throttle API calls - don't allow more than one refresh every 2 seconds
    // unless forceRefresh is explicitly set to true
    const now = Date.now();
    const THROTTLE_MS = 2000; // 2 seconds

    if (!forceRefresh && now - lastRefreshTime.current < THROTTLE_MS) {
      // If a refresh was recently performed, schedule a delayed refresh instead of immediate
      if (refreshTimeoutId.current) {
        clearTimeout(refreshTimeoutId.current);
      }

      // Return a promise that will resolve when the delayed refresh completes
      return new Promise<boolean>((resolve) => {
        refreshTimeoutId.current = setTimeout(() => {
          // Only perform refresh if component is still mounted
          if (isMounted.current) {
            refreshRepositories(true).then(resolve);
          } else {
            resolve(false);
          }
        }, THROTTLE_MS - (now - lastRefreshTime.current));
      });
    }

    try {
      // Update the last refresh time
      lastRefreshTime.current = now;

      // Don't set isLoading to true for refreshes to avoid disabling the UI
      // This allows users to continue interacting while data refreshes in the background
      const data = await apiClient.repositories.getConfigurations();

      // Only update state if component is still mounted
      if (!isMounted.current) return false;

      // Update repositories with new data
            setRepositories(
        data.configs.map(config => {
          // Ensure we correctly detect the repository type
          let repoType: RepositoryType = config.type || 'gitlab';

          // If type is not explicitly set but has Bitbucket properties, set as Bitbucket
          if (repoType !== 'bitbucket' && (config.bitbucketWorkspace || config.bitbucketRepoSlug)) {
                        repoType = 'bitbucket';
          }

          return {
            id: config.id || '',
            name: config.name || '',
            isActive: config.isActive || false, // lowercase to match backend model
            type: repoType
          };
        })
      );

      // If we have repositories but no selected one, select the first one
      if (data.configs.length > 0 && !data.configs.find(r => r.id === selectedRepoId)) {
        setSelectedRepoId(data.configs[0].id!);
      }

      return true;
    } catch (error) {
      // Only show toast if component is still mounted
      if (isMounted.current) {
        toast({
          title: 'Failed to refresh repositories',
          description: error instanceof Error ? error.message : 'Unknown error',
          variant: 'destructive',
        });
      }
      console.error('Failed to refresh repositories:', error);
      return false;
    }
  }, [toast, selectedRepoId, setSelectedRepoId]);

  // Fetch repositories only once on mount
  useEffect(() => {
    // Only run this effect once
    if (initialLoadComplete.current) return;

    // Always load repositories on mount, regardless of initialLoadComplete
    const initializeRepositories = async () => {
      // Set last refresh time to prevent immediate subsequent refreshes
      lastRefreshTime.current = Date.now();

      try {
        setIsLoading(true);
        // Use the repositories API instead of gitlab API to support both GitLab and Bitbucket
        const data = await apiClient.repositories.getConfigurations();

        // Store repositories for later use
                setRepositories(
          data.configs.map(config => {
            // Ensure we correctly detect the repository type
            let repoType: RepositoryType = config.type || 'gitlab';

            // If type is not explicitly set but has Bitbucket properties, set as Bitbucket
            if (repoType !== 'bitbucket' && (config.bitbucketWorkspace || config.bitbucketRepoSlug)) {
                            repoType = 'bitbucket';
            }

            return {
              id: config.id || '',
              name: config.name || '',
              isActive: config.isActive || false, // lowercase to match backend model
              type: repoType
            };
          })
        );

        // Check if we have any repositories configured
        if (data.configs.length > 0) {
          // If the saved repo ID is not valid or not present, use the first repository
          if (!savedRepoId || !data.configs.find(r => r.id === savedRepoId)) {
            changeRepository(data.configs[0].id!);
          }
        } else {
          // If no repositories, clear the selected repo ID
          changeRepository('');
        }

        // Mark initial load as complete
        initialLoadComplete.current = true;
      } catch (error) {
        if (isMounted.current) {
          toast({
            title: 'Failed to load repositories',
            description: error instanceof Error ? error.message : 'Unknown error',
            variant: 'destructive',
          });
          console.error('Failed to load repositories:', error);
        }
        // Even on error, mark initial load as complete to prevent repeated attempts
        initialLoadComplete.current = true;
      } finally {
        if (isMounted.current) {
          setIsLoading(false);
        }
      }
    };

    // Start initialization if component is mounted
    if (isMounted.current) {
      initializeRepositories();
    }

    // This effect should only run once, so no dependencies
  }, []);

  // Add a separate effect to handle selection changes
  useEffect(() => {
    // Skip if initial load hasn't completed yet
    if (!initialLoadComplete.current) return;

    // If repositories are loaded and we have a selected ID,
    // make sure it exists in the loaded repositories
    if (repositories.length > 0) {
      // If no repository is selected or the selected one doesn't exist, select the first one
      const repoExists = selectedRepoId && repositories.some(repo => repo.id === selectedRepoId);
      if (!selectedRepoId || !repoExists) {
        changeRepository(repositories[0].id);
      }
    }
  }, [repositories, selectedRepoId, changeRepository]);

  // Function to register a callback for repository changes
  const onRepositoryChange = useCallback((callback: RepositoryChangeCallback) => {
    // Add the callback to the set
    repositoryChangeCallbacks.current.add(callback);

    // Return a function to unregister the callback
    return () => {
      repositoryChangeCallbacks.current.delete(callback);
    };
  }, []);

  const value = {
    selectedRepoId,
    setSelectedRepoId: changeRepository,
    isLoading,
    repositories,
    duplicateRepository,
    refreshRepositories,
    onRepositoryChange,
  };

  return (
    <RepositoryContext.Provider value={value}>
      {children}
    </RepositoryContext.Provider>
  );
}

export function useRepository() {
  const context = useContext(RepositoryContext);
  if (context === undefined) {
    throw new Error('useRepository must be used within a RepositoryProvider');
  }
  return context;
}
