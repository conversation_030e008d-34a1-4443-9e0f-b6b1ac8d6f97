# Search Behavior Documentation

This document describes the search behavior implemented in the ADGitOps application. It serves as a reference for developers working on the search functionality.

## Search Syntax

The search engine supports various search patterns and operators to provide flexible and powerful search capabilities.

### Basic Search

- **Simple keyword search**: Typing a word without any special syntax will search for that word across all fields.
  - Example: `admin` - Searches for "admin" in all fields (name, description, type, lob, members)
  - This should be exaxct match by default (e.g., "admin" shouldn't match "administrator")

### Field-Specific Search

- **Exact match**: `fieldname:value` - Searches for exact matches of "value" in the specified field.
  - Example: `groupname:admin_group` - Matches groups with the exact name "admin_group"
  - Example: `lob:finance` - Matches items with the exact LOB "finance"
  - Example: `name:john` - Matches users with the exact name "john"

- **Quoted exact match**: `fieldname:"value"` - Same as exact match, but explicitly using quotes.
  - Example: `groupname:"admin_group"` - Matches groups with the exact name "admin_group"
  - Example: `description:"Harbor registry users"` - Matches the exact description

### Wildcard Search

- **Prefix wildcard**: `fieldname:value*` - Matches items where the field starts with "value".
  - Example: `groupname:admin*` - Matches groups with names starting with "admin"

- **Suffix wildcard**: `fieldname:*value` - Matches items where the field ends with "value".
  - Example: `groupname:*team` - Matches groups with names ending with "team"

- **Contains wildcard**: `fieldname:*value*` - Matches items where the field contains "value".
  - Example: `groupname:*harbor*` - Matches groups with "harbor" anywhere in the name

- **Contains operator**: `fieldname:~value` - Alternative syntax for contains search.
  - Example: `groupname:~harbor` - Matches groups with "harbor" anywhere in the name
  - This is equivalent to `fieldname:*value*`

### Logical Operators

- **AND**: `query1 AND query2` - Matches items that satisfy both conditions.
  - Example: `type:security AND lob:it` - Matches security groups in the IT LOB
  - Case-insensitive: `and` is also accepted

- **OR**: `query1 OR query2` - Matches items that satisfy either condition.
  - Example: `lob:it OR lob:finance` - Matches items in either IT or Finance LOB
  - Case-insensitive: `or` is also accepted

- **NOT**: `NOT query` - Excludes items that match the condition.
  - Example: `NOT lob:cloud` - Matches items that are not in the Cloud LOB
  - Case-insensitive: `not` is also accepted

### Complex Queries

- **Parentheses**: `(query1 OR query2) AND query3` - Groups conditions for complex logic.
  - Example: `(type:security OR type:technical) AND NOT lob:cloud` - Matches security or technical groups that are not in the Cloud LOB

- **Multiple conditions**: Combine various search patterns.
  - Example: `groupname:*admin* AND NOT type:security` - Matches groups with "admin" in the name that are not security groups
  - Example: `lobs:finance AND (groups:dev_team OR groups:finance_admins)` - Matches users in the Finance LOB who are in either the dev_team or finance_admins groups

## Special Field Handling

### Groups

- **Name field**: The `groupname` field is treated as an exact match field by default.
  - `groupname:fm` searches for groups with the exact name "fm"
  - Use wildcards for partial matching: `groupname:fm*` or `groupname:*fm*`

### Users

- **Name field**: The `name` field is treated as an exact match field by default.
  - `name:john` searches for users with the exact name "john"
  - Use wildcards for partial matching: `name:j*` or `name:*john*`

### LOB field

- **LOB field**: The `lob` field (for groups) and `lobs` field (for users) are treated as exact match fields by default.
  - `lob:finance` searches for groups with the exact LOB "finance"
  - `lobs:it` searches for users with the exact LOB "it"

### Member search

- **Member search**: The `members` field supports searching for group members.
  - `members:john` searches for groups that have a member named "john"
  - `members:*ice` searches for groups with members whose names end with "ice" (e.g., "alice")
  - `members:*oh*` searches for groups with members whose names contain "oh" (e.g., "john")

## Implementation Notes

1. The search engine uses Bleve as the underlying search technology.
2. Field-specific searches for `name`, `groupname`, and `lob` fields are treated as exact matches by default.
3. Other fields use wildcard matching by default for better user experience.
4. The search is case-insensitive for all fields and operators.
5. Simple keyword searches (without field specifiers) should match partial words.

## Expected Behavior for Test Cases

### Group Search

- `admin` should match groups with "admin" in any field (name, description, type, etc.)
- `type:security` should match groups with the exact type "security"
- `lob:finance` should match groups with the exact LOB "finance"
- `groupname:admin_group` should match the group with the exact name "admin_group"
- `groupname:admin*` should match groups with names starting with "admin"
- `groupname:*team` should match groups with names ending with "team"
- `groupname:*harbor*` should match groups with "harbor" anywhere in the name
- `members:john` should match groups that have a member named "john"

### User Search

- `john` should match users with "john" in any field
- `groups:admin_group` should match users who are members of the "admin_group" group
- `lobs:finance` should match users in the Finance LOB
- `name:j*` should match users whose names start with "j"
- `name:*e` should match users whose names end with "e"
- `name:*li*` should match users whose names contain "li"
