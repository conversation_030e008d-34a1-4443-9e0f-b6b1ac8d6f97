package search

import (
	"adgitops-ui/src/backend/models"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/blevesearch/bleve/v2"
)

// IndexGroups indexes a list of groups
func (s *BleveSearchService) IndexGroups(ctx context.Context, groups []models.Group) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Debug output
	fmt.Printf("IndexGroups: indexing %d groups\n", len(groups))

	if !s.initialized {
		return fmt.Errorf("search service not initialized")
	}

	// Create a batch for indexing
	batch := s.index.NewBatch()

	// Add each group to the batch
	for _, group := range groups {
		// Create a document ID
		docID := fmt.Sprintf("group:%s", group.Groupname)

		// Debug output
		fmt.Printf("IndexGroups: indexing group %s, type=%s, lob=%s\n", group.Groupname, group.Type, group.Lob)

		// Create a document with _type field
		doc := map[string]interface{}{
			"_type":       "group",
			"groupname":   group.Groupname,
			"type":        group.Type,
			"description": group.Description,
			"lob":         group.Lob,
			"sourceFile":  group.SourceFile,
			"repoId":      group.RepoID,
		}

		// Add members as separate fields for better search
		memberNames := []string{}
		for _, member := range group.Members {
			memberNames = append(memberNames, member.Name)
			// Debug output
			fmt.Printf("IndexGroups: adding member %s to group %s\n", member.Name, group.Groupname)
			// Special debug for cdrew
			if strings.Contains(strings.ToLower(member.Name), "cdrew") {
				fmt.Printf("FOUND CDREW: adding member %s to group %s\n", member.Name, group.Groupname)
			}
		}
		doc["members"] = memberNames

		// Also add each member as a separate field for better wildcard search
		for i, member := range group.Members {
			doc[fmt.Sprintf("member_%d", i)] = member.Name
		}

		// Add the group to the batch
		if err := batch.Index(docID, doc); err != nil {
			return fmt.Errorf("failed to index group %s: %w", group.Groupname, err)
		}
	}

	// Execute the batch
	if err := s.index.Batch(batch); err != nil {
		return fmt.Errorf("failed to execute batch: %w", err)
	}

	// Debug output
	fmt.Printf("IndexGroups: successfully indexed %d groups\n", len(groups))
	return nil
}

// IndexUsers indexes a list of users
func (s *BleveSearchService) IndexUsers(ctx context.Context, users []models.User) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Debug output
	fmt.Printf("IndexUsers: indexing %d users\n", len(users))

	if !s.initialized {
		return fmt.Errorf("search service not initialized")
	}

	// Create a batch for indexing
	batch := s.index.NewBatch()

	// Add each user to the batch
	for _, user := range users {
		// Create a document ID
		docID := fmt.Sprintf("user:%s", user.Name)

		// Debug output
		fmt.Printf("IndexUsers: indexing user %s, groups=%v, lobs=%v\n", user.Name, user.Groups, user.LOBs)

		// Create a document with _type field
		doc := map[string]interface{}{
			"_type":  "user",
			"name":   user.Name,
			"groups": user.Groups,
			"lobs":   user.LOBs,
			"repoId": user.RepoID,
		}

		// Add the user to the batch
		if err := batch.Index(docID, doc); err != nil {
			return fmt.Errorf("failed to index user %s: %w", user.Name, err)
		}
	}

	// Execute the batch
	if err := s.index.Batch(batch); err != nil {
		return fmt.Errorf("failed to execute batch: %w", err)
	}

	// Debug output
	fmt.Printf("IndexUsers: successfully indexed %d users\n", len(users))
	return nil
}

// Reindex reindexes all groups and users
func (s *BleveSearchService) Reindex(ctx context.Context, groups []models.Group, users []models.User) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.initialized {
		return fmt.Errorf("search service not initialized")
	}

	// Close the current index
	if err := s.index.Close(); err != nil {
		return fmt.Errorf("failed to close index: %w", err)
	}

	// Delete the index directory
	if err := os.RemoveAll(s.indexPath); err != nil {
		return fmt.Errorf("failed to delete index directory: %w", err)
	}

	// Create a new index
	indexMapping := s.createIndexMapping()
	index, err := bleve.New(s.indexPath, indexMapping)
	if err != nil {
		return fmt.Errorf("failed to create index: %w", err)
	}
	s.index = index

	// Index the groups
	batch := s.index.NewBatch()
	for _, group := range groups {
		docID := fmt.Sprintf("group:%s", group.Groupname)
		doc := map[string]interface{}{
			"_type":       "group",
			"groupname":   group.Groupname,
			"type":        group.Type,
			"description": group.Description,
			"lob":         group.Lob,
			"sourceFile":  group.SourceFile,
		}

		// Add members as separate fields for better search
		memberNames := []string{}
		for _, member := range group.Members {
			memberNames = append(memberNames, member.Name)
		}
		doc["members"] = memberNames

		if err := batch.Index(docID, doc); err != nil {
			return fmt.Errorf("failed to index group %s: %w", group.Groupname, err)
		}
	}

	// Index the users
	for _, user := range users {
		docID := fmt.Sprintf("user:%s", user.Name)
		doc := map[string]interface{}{
			"_type":  "user",
			"name":   user.Name,
			"groups": user.Groups,
			"lobs":   user.LOBs,
		}
		if err := batch.Index(docID, doc); err != nil {
			return fmt.Errorf("failed to index user %s: %w", user.Name, err)
		}
	}

	// Execute the batch
	if err := s.index.Batch(batch); err != nil {
		return fmt.Errorf("failed to execute batch: %w", err)
	}

	return nil
}

// GetIndexStats returns statistics about the index
// If repoId is provided, only count documents for that repository
func (s *BleveSearchService) GetIndexStats(repoId string) (map[string]interface{}, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.initialized {
		return nil, fmt.Errorf("search service not initialized")
	}

	// Get the index stats
	stats := s.index.Stats()
	if stats == nil {
		return nil, fmt.Errorf("failed to get index stats")
	}

	// Initialize the stats map
	statsMap := map[string]interface{}{
		"update_time": time.Now().Format(time.RFC3339),
		"index_path":  s.indexPath,
		"initialized": s.initialized,
		"index_size":  0, // Not available in the current version
	}

	// If no repository ID is provided, return global stats
	if repoId == "" {
		// Get the document count from the index stats
		// Use a query that matches all documents
		allDocsQuery := bleve.NewMatchAllQuery()
		allDocsRequest := bleve.NewSearchRequest(allDocsQuery)
		allDocsRequest.Size = 0 // We only need the count, not the actual documents

		// Execute the query
		allDocsResult, err := s.index.Search(allDocsRequest)
		if err != nil {
			return nil, fmt.Errorf("failed to get document count: %w", err)
		}

		statsMap["doc_count"] = allDocsResult.Total
		return statsMap, nil
	}

	// If repository ID is provided, count documents for that repository
	// We need to perform a search to get the count

	// Create a query for groups in this repository
	groupQuery := fmt.Sprintf("_type:group AND repoId:%s", repoId)
	groupRequest := bleve.NewSearchRequest(bleve.NewQueryStringQuery(groupQuery))
	groupRequest.Size = 0 // We only need the count, not the actual documents

	// Execute the query
	groupResult, err := s.index.Search(groupRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to search for groups: %w", err)
	}

	// Create a query for users in this repository
	userQuery := fmt.Sprintf("_type:user AND repoId:%s", repoId)
	userRequest := bleve.NewSearchRequest(bleve.NewQueryStringQuery(userQuery))
	userRequest.Size = 0 // We only need the count, not the actual documents

	// Execute the query
	userResult, err := s.index.Search(userRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to search for users: %w", err)
	}

	// Calculate total document count for this repository
	docCount := groupResult.Total + userResult.Total
	statsMap["doc_count"] = docCount

	// Add detailed counts
	statsMap["group_count"] = groupResult.Total
	statsMap["user_count"] = userResult.Total

	// Add repository information
	statsMap["repositories"] = []string{repoId}

	return statsMap, nil
}

// ClearIndex clears the search index
func (s *BleveSearchService) ClearIndex(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.initialized {
		return fmt.Errorf("search service not initialized")
	}

	// Close the current index
	if err := s.index.Close(); err != nil {
		return fmt.Errorf("failed to close index: %w", err)
	}

	// Delete the index directory
	if err := os.RemoveAll(s.indexPath); err != nil {
		return fmt.Errorf("failed to delete index directory: %w", err)
	}

	// Create a new index
	indexMapping := s.createIndexMapping()
	index, err := bleve.New(s.indexPath, indexMapping)
	if err != nil {
		s.initialized = false
		return fmt.Errorf("failed to create new index: %w", err)
	}

	// Set the new index
	s.index = index

	return nil
}
