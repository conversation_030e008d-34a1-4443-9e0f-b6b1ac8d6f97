# ADGitOps Reports & Exports Feature

This document provides comprehensive documentation for the Reports and Exports feature in the ADGitOps application.

## Overview

The Reports & Exports feature allows users to:

1. Create, manage, and customize report templates (presets)
2. Generate comprehensive reports for users, groups, or both
3. Filter reports by different criteria (LOB, group types, specific IDs)
4. Download reports as JSON files
5. View and manage previously generated reports

## Key Components

### Report Presets

Report presets are saved configurations that define the type of data to include in a report and the filtering criteria to apply.

**Preset Properties:**
- **Name**: Descriptive name for the preset
- **Description**: Optional detailed explanation of the preset's purpose
- **Report Type**: Users, Groups, or Both
- **Query Parameters**:
  - LOB: Filter by line of business
  - Types: Filter by group types
  - Group IDs: Specific groups to include
  - User IDs: Specific users to include

### Reports

Generated reports based on either presets or ad-hoc criteria.

**Report Properties:**
- **ID**: Unique identifier
- **Preset ID/Name**: Reference to the preset used (if applicable)
- **Filename**: Name of the generated file
- **Type**: Users, Groups, or Both
- **Size**: File size in bytes
- **Created At**: Timestamp of creation
- **Download URL**: Direct link to download the report

## Usage Guide

### Creating a Report Preset

1. Navigate to **Report Presets** in the sidebar
2. Click **Create New Preset**
3. Fill in the required details:
   - Name (required)
   - Description (optional)
   - Report Type (Users, Groups, or Both)
   - Query Parameters (all optional)
4. Click **Create Preset**

### Creating a Preset from Current View

1. Navigate to **Users** or **Groups**
2. Apply any desired filters (LOB, search terms)
3. Click **Save as Preset**
4. Fill in the preset details
5. Click **Save Preset**

### Generating a Report

**From a Preset:**
1. Navigate to **Report Presets**
2. Find the desired preset
3. Click **Generate**

**Directly from Users/Groups Pages:**
1. Navigate to **Users** or **Groups**
2. Apply any desired filters
3. Click **Export**

### Managing Reports

1. Navigate to **Reports** in the sidebar
2. View all generated reports
3. Search or filter the reports
4. Download or delete reports as needed

## API Endpoints

### Report Presets

- `GET /data/reports/presets` - Get all report presets
- `GET /data/reports/presets/:id` - Get a specific report preset
- `POST /data/reports/presets` - Create a new report preset
- `PUT /data/reports/presets/:id` - Update an existing report preset
- `DELETE /data/reports/presets/:id` - Delete a report preset

### Reports

- `GET /data/reports` - Get all reports
- `POST /data/reports/generate` - Generate a new report
- `POST /data/reports/generate/:presetId` - Generate a report from a preset
- `DELETE /data/reports/:id` - Delete a report
- `GET /exports/download/:filename` - Download a report file

## Query Parameters

When creating a report preset or generating a report, the following query parameters can be used:

| Parameter | Description | Example |
|-----------|-------------|---------|
| lob | Filter by line of business | "Marketing" |
| types | Filter groups by type | ["security", "distribution"] |
| groupIds | Include specific groups | ["group1", "group2"] |
| userIds | Include specific users | ["user1", "user2"] |

## Frontend Components

- **ReportPresets.tsx**: Page for managing report presets
- **Reports.tsx**: Page for viewing and managing generated reports
- **Dashboard.tsx**: Shows recent reports
- **Users.tsx** & **Groups.tsx**: Includes "Save as Preset" and "Export" functionality

## Data Flow

1. User creates a report preset or triggers direct report generation
2. Frontend sends the query parameters to the backend
3. Backend filters the data according to parameters
4. Backend generates a JSON file with the filtered data
5. Backend creates a report record with metadata
6. Frontend displays the report in the Reports page
7. User can download the generated report file

## Best Practices

1. **Create Presets for Common Queries**: Save time by creating presets for frequently used reports
2. **Use Specific Naming**: Give presets clear names that indicate their contents
3. **Combine Filters**: Use multiple filter criteria for more targeted reports
4. **Regular Cleanup**: Delete old reports that are no longer needed

## Troubleshooting

| Issue | Solution |
|-------|----------|
| Report generation fails | Check if the repository is selected and contains data |
| No data in report | Verify that your filter criteria match existing data |
| Cannot download a report | Ensure the report file still exists on the server |
| Preset not showing in list | Refresh the page or check if it was deleted |

## Technical Implementation

The Reports feature uses a file-based approach:
- Report presets are stored as JSON in a dedicated file
- Generated reports are saved as JSON files in the exports directory
- Report metadata is maintained to track and manage the generated files

## Future Enhancements

Planned improvements for future releases:
1. Additional export formats (CSV, Excel)
2. Report scheduling
3. Email distribution of reports
4. Data visualization options
5. More advanced filtering and aggregation capabilities
