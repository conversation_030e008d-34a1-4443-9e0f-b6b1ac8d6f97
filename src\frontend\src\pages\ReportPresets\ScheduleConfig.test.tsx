import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import ScheduleConfigComponent from './ScheduleConfig'
import { ScheduleConfig } from '@/api/client'

describe('ScheduleConfigComponent', () => {
  it('renders correctly with default values', () => {
    const mockOnChange = vi.fn()
    render(<ScheduleConfigComponent schedule={undefined} onChange={mockOnChange} />)

    // Check that the component renders with default values
    expect(screen.getByText('Automatic Scheduling')).toBeInTheDocument()
    expect(screen.getByRole('switch')).not.toBeChecked()
  })

  it('renders correctly with provided schedule', () => {
    const mockOnChange = vi.fn()
    const schedule: ScheduleConfig = {
      enabled: true,
      frequency: 'daily',
      hour: 12,
      minute: 30,
    }
    render(<ScheduleConfigComponent schedule={schedule} onChange={mockOnChange} />)

    // Check that the component renders with provided values
    expect(screen.getByRole('switch')).toBeChecked()
    expect(screen.getByText('Frequency')).toBeInTheDocument()
  })

  it('calls onChange when schedule is toggled', () => {
    const mockOnChange = vi.fn()
    render(<ScheduleConfigComponent schedule={undefined} onChange={mockOnChange} />)

    // Toggle the schedule
    fireEvent.click(screen.getByRole('switch'))

    // Check that onChange was called with the updated schedule
    expect(mockOnChange).toHaveBeenCalledWith(expect.objectContaining({
      enabled: true,
    }))
  })

  it('disables controls in read-only mode', () => {
    const mockOnChange = vi.fn()
    const schedule: ScheduleConfig = {
      enabled: true,
      frequency: 'daily',
      hour: 12,
      minute: 30,
    }
    render(<ScheduleConfigComponent schedule={schedule} onChange={mockOnChange} readOnly={true} />)

    // Check that the switch is disabled
    expect(screen.getByRole('switch')).toBeDisabled()
  })

  it('displays next run time when provided', () => {
    const mockOnChange = vi.fn()
    const schedule: ScheduleConfig = {
      enabled: true,
      frequency: 'daily',
      hour: 12,
      minute: 30,
      nextRun: '2023-01-01T12:30:00Z',
    }
    render(<ScheduleConfigComponent schedule={schedule} onChange={mockOnChange} />)

    // Check that the next run time is displayed
    expect(screen.getByText(/Next run:/)).toBeInTheDocument()
  })
})
