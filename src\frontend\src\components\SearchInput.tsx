import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';
import { Search } from 'lucide-react';

interface SearchInputProps {
  onSearch: (value: string) => void;
  placeholder?: string;
  initialValue?: string;
}

/**
 * A search input component that triggers search on Enter key press
 */
const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>((
  {
    onSearch,
    placeholder = 'Search...',
    initialValue = ''
  },
  ref
) => {
  // State for the input value
  const [inputValue, setInputValue] = useState(initialValue);

  // Internal ref for the input element
  const inputRef = useRef<HTMLInputElement>(null);

  // Update input value when initialValue prop changes
  useLayoutEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  // Ref for the debounce timer
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Track if the input is focused
  const [isFocused, setIsFocused] = useState(false);

  // Handle focus and blur events
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  // Handle input changes without auto-search
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart || 0;

    // Update the input value immediately for UI responsiveness
    setInputValue(value);

    // Clear any existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }

    // Store cursor position for later use
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(cursorPos, cursorPos);
      }
    }, 0);

    // No auto-search - search will only be triggered on Enter key press
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Clear any existing timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }

      // Store current selection position
      const selectionStart = e.currentTarget.selectionStart;
      const selectionEnd = e.currentTarget.selectionEnd;

      // Call search immediately
      onSearch(inputValue);

      // Prevent default behavior
      e.preventDefault();

      // Ensure focus is maintained
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          // Restore cursor position
          if (selectionStart !== null && selectionEnd !== null) {
            inputRef.current.setSelectionRange(selectionStart, selectionEnd);
          }
        }
      }, 0);

      // Use requestAnimationFrame as a backup
      requestAnimationFrame(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          // Restore cursor position
          if (selectionStart !== null && selectionEnd !== null) {
            inputRef.current.setSelectionRange(selectionStart, selectionEnd);
          }
        }
      });
    }
  };

  // Clean up the timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <div className="relative w-full">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      <input
        ref={(el) => {
          // Set the forwarded ref
          if (typeof ref === 'function') {
            ref(el);
          } else if (ref) {
            ref.current = el;
          }
          // Also set our internal ref
          if (el) {
            // Use non-null assertion to tell TypeScript this is safe
            (inputRef as React.MutableRefObject<HTMLInputElement | null>).current = el;
          }
        }}
        type="text"
        placeholder={placeholder}
        className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        autoComplete="off"
        data-focused={isFocused}
      />
    </div>
  );
});

export default SearchInput;
