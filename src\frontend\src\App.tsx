import { Routes, Route } from 'react-router-dom'
import { Toaster } from './components/ui/toaster'
import Layout from './components/layout/Layout'
import Dashboard from './pages/Dashboard'
import Groups from './pages/Groups'
import Users from './pages/Users'
import ReportPresets from './pages/ReportPresets'
import Reports from './pages/Reports'
import Settings from './pages/Settings'
import NotFound from './pages/NotFound'
import { RepositoryProvider } from './context/RepositoryContext'

function App() {
  return (
    <RepositoryProvider>
      <Toaster />
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="groups" element={<Groups />} />
          <Route path="users" element={<Users />} />
          <Route path="report-presets" element={<ReportPresets />} />
          <Route path="reports" element={<Reports />} />
          <Route path="settings" element={<Settings />} />
          {/* Catch all route for 404 errors */}
          <Route path="*" element={<NotFound />} />
        </Route>
      </Routes>
    </RepositoryProvider>
  )
}

export default App
