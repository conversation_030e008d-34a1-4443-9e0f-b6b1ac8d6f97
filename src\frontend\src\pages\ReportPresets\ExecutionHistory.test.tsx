import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { MemoryRouter } from 'react-router-dom'
import ExecutionHistory from './ExecutionHistory'
import { apiClient } from '@/api/client'

// Mock the API client
vi.mock('@/api/client', () => ({
  apiClient: {
    getReportExecutionsByPreset: vi.fn(),
  },
}))

// Mock the useToast hook
vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock the useNavigate hook
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  }
})

describe('ExecutionHistory', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders loading state initially', () => {
    // Mock the API response
    vi.mocked(apiClient.getReportExecutionsByPreset).mockResolvedValue([])

    render(
      <MemoryRouter>
        <ExecutionHistory presetId="preset1" />
      </MemoryRouter>
    )

    // Check that loading state is rendered
    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument()
  })

  it('renders empty state when no executions', async () => {
    // Mock the API response
    vi.mocked(apiClient.getReportExecutionsByPreset).mockResolvedValue([])

    render(
      <MemoryRouter>
        <ExecutionHistory presetId="preset1" />
      </MemoryRouter>
    )

    // Wait for the API call to resolve
    await waitFor(() => {
      expect(screen.getByText(/No execution history available/)).toBeInTheDocument()
    })

    // Check that the API was called with the correct preset ID
    expect(apiClient.getReportExecutionsByPreset).toHaveBeenCalledWith('preset1')
  })

  it('renders executions when available', async () => {
    // Mock the API response
    const mockExecutions = [
      {
        id: 'exec1',
        presetId: 'preset1',
        presetName: 'Preset 1',
        presetVersion: 1,
        sharedPresetId: 'shared_preset1',
        status: 'success',
        executedAt: '2023-01-01T12:00:00Z',
        reportId: 'report1',
      },
      {
        id: 'exec2',
        presetId: 'preset1',
        presetName: 'Preset 1',
        presetVersion: 2,
        sharedPresetId: 'shared_preset1',
        status: 'failed',
        executedAt: '2023-01-02T12:00:00Z',
        errorMessage: 'Error message',
      },
    ]
    vi.mocked(apiClient.getReportExecutionsByPreset).mockResolvedValue(mockExecutions)

    render(
      <MemoryRouter>
        <ExecutionHistory presetId="preset1" />
      </MemoryRouter>
    )

    // Wait for the API call to resolve
    await waitFor(() => {
      expect(screen.getByText('Status')).toBeInTheDocument()
    })

    // Check that the column headers are rendered
    expect(screen.getByText('Version')).toBeInTheDocument()
    expect(screen.getByText('Executed At')).toBeInTheDocument()
    expect(screen.getByText('Report')).toBeInTheDocument()

    // Check that the executions are rendered
    expect(screen.getByText('Success')).toBeInTheDocument()
    expect(screen.getByText('Failed')).toBeInTheDocument()
    expect(screen.getByText('View Report')).toBeInTheDocument()
    expect(screen.getByText('Error message')).toBeInTheDocument()

    // Check that the version numbers are displayed
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument()
  })

  it('handles API error', async () => {
    // Mock the API response to throw an error
    vi.mocked(apiClient.getReportExecutionsByPreset).mockRejectedValue(new Error('API error'))

    render(
      <MemoryRouter>
        <ExecutionHistory presetId="preset1" />
      </MemoryRouter>
    )

    // Wait for the API call to resolve
    await waitFor(() => {
      expect(screen.getByText(/No execution history available/)).toBeInTheDocument()
    })
  })
})
