package controllers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"adgitops-ui/src/backend/models"
)

// MockDataProcessor is a mock implementation of the data processor
type MockDataProcessor struct {
	mock.Mock
}

// TestDataProcessor is a test-specific DataProcessor that implements DataProcessorInterface
// but only implements the methods needed for the test
type TestDataProcessor struct {
	mockDP *MockDataProcessor
}

// TestDataProcessor is a test-specific implementation

// GetPreset delegates to the mock
func (t *TestDataProcessor) GetPreset(id string) (models.ReportPreset, error) {
	return t.mockDP.GetPreset(id)
}

// GetPresets delegates to the mock
func (t *TestDataProcessor) GetPresets() ([]models.ReportPreset, error) {
	return t.mockDP.GetPresets()
}

// DeletePreset delegates to the mock
func (t *TestDataProcessor) DeletePreset(presetID string, schedulerService interface{}) error {
	return t.mockDP.DeletePreset(presetID, schedulerService)
}

func (m *MockDataProcessor) GetPreset(id string) (models.ReportPreset, error) {
	args := m.Called(id)
	return args.Get(0).(models.ReportPreset), args.Error(1)
}

func (m *MockDataProcessor) GetPresets() ([]models.ReportPreset, error) {
	args := m.Called()
	return args.Get(0).([]models.ReportPreset), args.Error(1)
}

func (m *MockDataProcessor) DeletePreset(presetID string, schedulerService interface{}) error {
	args := m.Called(presetID, schedulerService)
	return args.Error(0)
}

// MockRepositoryManager is a mock implementation of the repository manager
type MockRepositoryManager struct {
	mock.Mock
}

// MockSchedulerService is a mock implementation of the scheduler service
type MockSchedulerService struct {
	mock.Mock
}

func (m *MockSchedulerService) GetExecutions(sharedId ...string) []models.ReportExecution {
	if len(sharedId) > 0 && sharedId[0] != "" {
		args := m.Called(sharedId[0])
		return args.Get(0).([]models.ReportExecution)
	}
	args := m.Called()
	return args.Get(0).([]models.ReportExecution)
}

func (m *MockSchedulerService) GetExecutionsByPreset(presetID string) []models.ReportExecution {
	args := m.Called(presetID)
	return args.Get(0).([]models.ReportExecution)
}

func (m *MockSchedulerService) GetExecutionsBySharedPresetID(sharedPresetID string) []models.ReportExecution {
	args := m.Called(sharedPresetID)
	return args.Get(0).([]models.ReportExecution)
}

func (m *MockSchedulerService) DeleteExecutionsForPreset(presetID string) error {
	args := m.Called(presetID)
	return args.Error(0)
}

// TestDataController_GetReportExecutionsByPreset tests the getReportExecutionsByPreset endpoint
func TestDataController_GetReportExecutionsByPreset(t *testing.T) {
	// Create a custom DataController with our mock scheduler service
	mockSS := new(MockSchedulerService)

	// Create test executions with preset version and shared preset ID
	executions := []models.ReportExecution{
		{
			ID:             "exec1",
			PresetID:       "preset1",
			PresetName:     "Preset 1",
			PresetVersion:  1,
			SharedPresetID: "shared_preset1",
			Status:         "success",
			ExecutedAt:     "2023-01-01T12:00:00Z",
			ReportID:       "report1",
		},
		{
			ID:             "exec2",
			PresetID:       "preset1",
			PresetName:     "Preset 1",
			PresetVersion:  1,
			SharedPresetID: "shared_preset1",
			Status:         "failed",
			ExecutedAt:     "2023-01-02T12:00:00Z",
			ErrorMessage:   "Error message",
		},
	}

	// Setup mock expectations for regular preset ID (not using shared ID in this test)
	// The controller now calls GetExecutions with the preset ID as a parameter
	mockSS.On("GetExecutions", "preset1").Return(executions)

	// We'll skip the data processor mock for this test and just test the scheduler service

	// Create a controller with just the mock scheduler
	controller := &DataController{
		schedulerService: mockSS,
	}

	// Set the dataProcessor field directly
	controller.dataProcessor = nil

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	api := router.Group("/api")
	dataRoutes := api.Group("/data")
	dataRoutes.GET("/repositories/:id/reports/presets/:presetId/executions", controller.getReportExecutionsByPreset)

	// Create test request
	req, _ := http.NewRequest("GET", "/api/data/repositories/repo_1/reports/presets/preset1/executions", nil)
	resp := httptest.NewRecorder()

	// Perform the request
	router.ServeHTTP(resp, req)

	// Check response
	assert.Equal(t, http.StatusOK, resp.Code)

	// Parse response body
	var responseExecutions []models.ReportExecution
	err := json.Unmarshal(resp.Body.Bytes(), &responseExecutions)
	assert.NoError(t, err)

	// Verify response
	assert.Len(t, responseExecutions, 2)
	assert.Equal(t, "exec1", responseExecutions[0].ID)
	assert.Equal(t, "preset1", responseExecutions[0].PresetID)
	assert.Equal(t, 1, responseExecutions[0].PresetVersion)
	assert.Equal(t, "shared_preset1", responseExecutions[0].SharedPresetID)
	assert.Equal(t, "success", responseExecutions[0].Status)
	assert.Equal(t, "report1", responseExecutions[0].ReportID)
	assert.Equal(t, "exec2", responseExecutions[1].ID)
	assert.Equal(t, "preset1", responseExecutions[1].PresetID)
	assert.Equal(t, 1, responseExecutions[1].PresetVersion)
	assert.Equal(t, "shared_preset1", responseExecutions[1].SharedPresetID)
	assert.Equal(t, "failed", responseExecutions[1].Status)
	assert.Equal(t, "Error message", responseExecutions[1].ErrorMessage)

	// Verify mock expectations
	mockSS.AssertExpectations(t)
}

// TestDataController_SchedulerServiceNotInitialized tests the behavior when scheduler service is not initialized
func TestDataController_SchedulerServiceNotInitialized(t *testing.T) {
	// Create a controller with nil scheduler service
	controller := &DataController{
		schedulerService: nil,
	}

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	api := router.Group("/api")
	dataRoutes := api.Group("/data")
	// Register the endpoint
	dataRoutes.GET("/repositories/:id/reports/presets/:presetId/executions", controller.getReportExecutionsByPreset)

	// Test getReportExecutionsByPreset
	req, _ := http.NewRequest("GET", "/api/data/repositories/repo_1/reports/presets/preset1/executions", nil)
	resp := httptest.NewRecorder()
	router.ServeHTTP(resp, req)
	assert.Equal(t, http.StatusInternalServerError, resp.Code)
	assert.Contains(t, resp.Body.String(), "Scheduler service not initialized")
}

// This method is already defined above

// Implement the remaining methods required by DataProcessorInterface
func (m *MockDataProcessor) SavePreset(preset models.ReportPreset) (models.ReportPreset, error) {
	args := m.Called(preset)
	return args.Get(0).(models.ReportPreset), args.Error(1)
}

func (m *MockDataProcessor) UpdatePresetInPlace(preset models.ReportPreset) (models.ReportPreset, error) {
	args := m.Called(preset)
	return args.Get(0).(models.ReportPreset), args.Error(1)
}

func (m *MockDataProcessor) ParseJSONFiles(repoPath string) ([]models.Group, error) {
	args := m.Called(repoPath)
	return args.Get(0).([]models.Group), args.Error(1)
}

func (m *MockDataProcessor) GenerateReport(groups []models.Group, reportType string, query models.QueryParams, presetID string, presetName string) (models.Report, error) {
	args := m.Called(groups, reportType, query, presetID, presetName)
	return args.Get(0).(models.Report), args.Error(1)
}

// Implement the unexported method with the same name
func (m *MockDataProcessor) saveReportMetadata(report models.Report) error {
	args := m.Called(report)
	return args.Error(0)
}

// TestController is a simplified version of DataController for testing
type TestController struct {
	mockDP *MockDataProcessor
	mockSS *MockSchedulerService
}

// deleteReportPreset is a test implementation that only calls DeletePreset on the mock
func (c *TestController) deleteReportPreset(ctx *gin.Context) {
	presetID := ctx.Param("presetId")

	// Call DeletePreset on the mock
	err := c.mockDP.DeletePreset(presetID, c.mockSS)

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete/deactivate preset: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Preset deleted successfully",
	})
}

// TestDataController_DeleteReportPreset tests the deleteReportPreset endpoint
func TestDataController_DeleteReportPreset(t *testing.T) {
	// Create mocks
	mockDP := new(MockDataProcessor)
	mockSS := new(MockSchedulerService)

	// Setup mock expectations
	presetID := "preset1"
	mockDP.On("DeletePreset", presetID, mockSS).Return(nil)

	// Create test controller with mocks
	controller := &TestController{
		mockDP: mockDP,
		mockSS: mockSS,
	}

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	api := router.Group("/api")
	dataRoutes := api.Group("/data")
	dataRoutes.DELETE("/repositories/:id/reports/presets/:presetId", controller.deleteReportPreset)

	// Create test request
	req, _ := http.NewRequest("DELETE", "/api/data/repositories/repo_1/reports/presets/"+presetID, nil)
	resp := httptest.NewRecorder()

	// Perform the request
	router.ServeHTTP(resp, req)

	// Check response
	assert.Equal(t, http.StatusOK, resp.Code)
	assert.Contains(t, resp.Body.String(), "success")

	// Verify mock expectations
	mockDP.AssertExpectations(t)
	mockSS.AssertExpectations(t)

	// Test with error from DeletePreset
	mockDP2 := new(MockDataProcessor)
	mockSS2 := new(MockSchedulerService)
	mockDP2.On("DeletePreset", presetID, mockSS2).Return(assert.AnError)

	controller2 := &TestController{
		mockDP: mockDP2,
		mockSS: mockSS2,
	}

	router2 := gin.New()
	api2 := router2.Group("/api")
	dataRoutes2 := api2.Group("/data")
	dataRoutes2.DELETE("/repositories/:id/reports/presets/:presetId", controller2.deleteReportPreset)

	req2, _ := http.NewRequest("DELETE", "/api/data/repositories/repo_1/reports/presets/"+presetID, nil)
	resp2 := httptest.NewRecorder()

	router2.ServeHTTP(resp2, req2)

	assert.Equal(t, http.StatusInternalServerError, resp2.Code)
	assert.Contains(t, resp2.Body.String(), "Failed to delete/deactivate preset")

	mockDP2.AssertExpectations(t)
	mockSS2.AssertExpectations(t)
}
