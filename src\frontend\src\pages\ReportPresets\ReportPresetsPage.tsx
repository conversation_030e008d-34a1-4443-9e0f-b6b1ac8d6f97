import { useState, useEffect, useRef, useCallback } from 'react'
import { Plus } from 'lucide-react'
import { useLocation, useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { useRepository } from '@/context/RepositoryContext'
import { useFilters } from '@/components/layout/Layout'
import { apiClient } from '@/api/client'
import ReportPresetsHelp from './ReportPresetsHelp'
import { PresetsList } from './PresetsList'
import { CreatePresetDialog } from './CreatePresetDialog'
import { EditPresetDialog } from './EditPresetDialog'
import { ViewPresetDialog } from './ViewPresetDialog'
import { DeletePresetDialog } from './DeletePresetDialog'
import ExecutionHistoryDialog from './ExecutionHistoryDialog'
import { usePresets } from './hooks/usePresets'
import { PresetFormState, ReportPreset } from './types'

const ReportPresetsPage = () => {
  const { toast } = useToast()
  const { selectedRepoId } = useRepository()
  const location = useLocation()
  const navigate = useNavigate()
  const { setFilters } = useFilters()

  // Use the presets hook
  const {
    presets,
    setPresets,
    loading,
    error,
    // deletePresetId, // Unused but may be needed in the future
    showConfirmDelete,
    setDeletePresetId,
    setShowConfirmDelete,
    // fetchPresets, // Unused but may be needed in the future
    createPreset,
    updatePreset,
    deletePreset,
    togglePresetActivation,
    generateReport,
  } = usePresets(selectedRepoId)

  // Dialog state
  const [showCreateDialog, setShowCreateDialog] = useState<boolean>(false)
  const [showEditDialog, setShowEditDialog] = useState<boolean>(false)
  const [showViewDialog, setShowViewDialog] = useState<boolean>(false)
  const [currentPreset, setCurrentPreset] = useState<ReportPreset | null>(null)
  const [viewPreset, setViewPreset] = useState<ReportPreset | null>(null)
  const [versionInputValue, setVersionInputValue] = useState<string>('')
  const [availableVersions, setAvailableVersions] = useState<number[]>([])
  const [versionNavigating, setVersionNavigating] = useState<boolean>(false)

  // Execution history dialog state
  const [showHistoryDialog, setShowHistoryDialog] = useState<boolean>(false)
  const [historyPresetId, setHistoryPresetId] = useState<string>('')
  const [historyPresetName, setHistoryPresetName] = useState<string>('')
  const [historySharedPresetId, setHistorySharedPresetId] = useState<string>('')

  // Shared groups data to prevent multiple API calls
  const [groupsData, setGroupsData] = useState<any[]>([]);
  const [isLoadingGroups, setIsLoadingGroups] = useState(false);

  // Autocomplete suggestions
  const [availableLobs, setAvailableLobs] = useState<string[]>([])
  const [availableGroupTypes, setAvailableGroupTypes] = useState<string[]>([])
  const [availableGroups, setAvailableGroups] = useState<string[]>([])
  const [availableUsers, setAvailableUsers] = useState<string[]>([])

  // Ref to track if we've already handled URL parameters
  const handledUrlParamsRef = useRef<string | null>(null);

  // Store URL parameters in a ref to access them across effects
  const urlParamsRef = useRef<{
    createFromQuery: string | null;
    searchQuery: string | null;
    reportType: 'users' | 'groups' | 'both';
    presetId: string | null;
    versionParam: string | null;
    viewParam: string | null;
    urlParamsKey: string;
  } | null>(null);

  // Set the help button in the top bar
  const MemoizedFilters = useCallback(() => {
    return (
      <div className="flex items-center justify-end w-full">
        <ReportPresetsHelp />
      </div>
    );
  }, []);

  // Set filters in the top bar
  useEffect(() => {
    setFilters(<MemoizedFilters />)

    // Cleanup when component unmounts
    return () => {
      setFilters(null)
    }
  }, [setFilters, MemoizedFilters])

  // Fetch groups data
  const fetchGroupsData = async () => {
    if (isLoadingGroups || groupsData.length > 0) {
      return;
    }

    setIsLoadingGroups(true);
    try {
      const groups = await apiClient.data.getGroups(selectedRepoId);
      setGroupsData(groups.items || []);
    } catch (err) {
      console.error('Failed to fetch groups data:', err);
      toast({
        title: "Error",
        description: "Failed to fetch groups data",
        variant: "destructive",
      });
    } finally {
      setIsLoadingGroups(false);
    }
  };

  // Process groups data for LOBs and suggestions
  const processGroupsData = () => {
    // Extract unique LOBs
    const lobs = new Set<string>();
    // Extract unique group types
    const types = new Set<string>();
    // Extract group names
    const groups: string[] = [];
    // Extract user names
    const users = new Set<string>();

    groupsData.forEach(group => {
      // Add LOB
      if (group.lob) {
        lobs.add(group.lob);
      }

      // Add type
      if (group.type) {
        types.add(group.type);
      }

      // Add group name
      if (group.name) {
        groups.push(group.name);
      }

      // Add users
      if (group.members) {
        group.members.forEach((member: any) => {
          if (member.type === 'user' && member.name) {
            users.add(member.name);
          }
        });
      }
    });

    setAvailableLobs(Array.from(lobs));
    setAvailableGroupTypes(Array.from(types));
    setAvailableGroups(groups);
    setAvailableUsers(Array.from(users));
  };

  // We use a ref to track if we've already processed URL parameters
  const hasProcessedUrlRef = useRef<boolean>(false);

  // Fetch presets on mount and store URL parameters
  useEffect(() => {

    // Skip if we've already processed URL parameters
    if (hasProcessedUrlRef.current) {
            return;
    }

    // Check if we have any query parameters before proceeding
    if (!location.search) {
            hasProcessedUrlRef.current = true;
      return;
    }

    // Parse URL parameters
    const params = new URLSearchParams(location.search)
    const createFromQuery = params.get('createFromQuery')
    const searchQuery = params.get('searchQuery')
    const reportType = params.get('reportType') as 'users' | 'groups' | 'both' || 'both'
    const presetId = params.get('preset')
    const versionParam = params.get('version')
    const viewParam = params.get('view')

    // Check if we have any relevant parameters
    const hasRelevantParams = createFromQuery || searchQuery || presetId || versionParam || viewParam;

    if (!hasRelevantParams) {
            return;
    }

    // Create a unique key for this set of URL parameters to avoid re-processing
    const urlParamsKey = `${createFromQuery}-${searchQuery}-${reportType}-${presetId}-${versionParam}-${viewParam}`;


    // Skip if we've already handled these exact URL parameters
    if (handledUrlParamsRef.current === urlParamsKey) {
            return;
    }


    // Store the URL parameters in the ref for use in other effects
    urlParamsRef.current = {
      createFromQuery,
      searchQuery,
      reportType,
      presetId,
      versionParam,
      viewParam,
      urlParamsKey
    };

    // Mark these URL parameters as handled
    handledUrlParamsRef.current = urlParamsKey;

    if ((createFromQuery === 'true' && searchQuery) || (createFromQuery && createFromQuery !== 'true')) {
      // Pre-fill the form and open the create dialog
      // Handle both cases:
      // 1. createFromQuery=true&searchQuery=value
      // 2. createFromQuery=value (where value is the search query)
      // Note: The actual query is passed via initialValues to the dialog

      // Open the create dialog with pre-filled values
      setShowCreateDialog(true)
    } else if (presetId) {
      // Handle viewing a specific preset version after presets are loaded
      const handlePresetView = async () => {
        const version = versionParam ? parseInt(versionParam, 10) : undefined

        // Extract the base preset ID (without version suffix)
        const basePresetId = presetId.split('_')[0];

        // Find the preset in the loaded presets
        let foundPreset = presets ? presets.find(p => {
          // Check if this is the exact preset ID
          if (p.id === presetId) {
            return true;
          }

          // Check if this is the base preset ID
          if (p.id === basePresetId) {
            return true;
          }

          // Check if this preset has the same shared ID
          if (p.sharedId && p.sharedId === presetId) {
            return true;
          }

          // If version is specified, check for matching version with shared ID
          if (version && p.sharedId === presetId && p.version === version) {
            return true;
          }

          // If version is specified, check for matching version with parent ID
          if (version && p.parentId === basePresetId && p.version === version) {
            return true;
          }

          // Check if this is a version of the preset
          if (p.parentId === basePresetId) {
            return true;
          }

          return false;
        }) : undefined

        if (foundPreset) {
          // Scroll to the preset card and highlight it
          setTimeout(() => {
            const presetCard = document.getElementById(`preset-${foundPreset?.id}`)
            if (presetCard) {
              presetCard.scrollIntoView({ behavior: 'smooth', block: 'center' })
              // Add a highlight effect
              presetCard.classList.add('ring-2', 'ring-primary', 'ring-offset-2')
              setTimeout(() => {
                presetCard.classList.remove('ring-2', 'ring-primary', 'ring-offset-2')
              }, 3000)
            }
          }, 500)

          // Open the view dialog for this preset if view parameter is present
                    if (viewParam) {

            // First call viewPresetDetails to load the preset data
            viewPresetDetails(foundPreset);

            // Then force the dialog to be shown with a longer timeout to ensure everything is loaded
            setTimeout(() => {
                            setShowViewDialog(true);
            }, 800);
          }
        } else if (version && presets && presets.length > 0) {
          // If we can't find the preset in the current list, try to fetch it
          try {
            // Use the base preset ID or shared ID to fetch versions
            const basePresetId = presetId.split('_')[0];

            // Try to fetch versions using the ID as is (might be a shared ID)
            let versions = await apiClient.data.getReportPresetVersions(selectedRepoId, presetId);

            // If no versions found, try with the base ID
            if (!versions || versions.length === 0) {
              versions = await apiClient.data.getReportPresetVersions(selectedRepoId, basePresetId);
            }


            // Find the target version
            const targetVersion = versions.find(v => v.version === version);

            if (targetVersion) {
              // Add this version to the presets list if it's not already there
              setPresets((prev: ReportPreset[] | null) => {
                if (!prev || !prev.some((p: ReportPreset) => p.id === targetVersion.id)) {
                  return [...(prev || []), targetVersion]
                }
                return prev
              })

              // Scroll to it after a short delay
              setTimeout(() => {
                const presetCard = document.getElementById(`preset-${targetVersion.id}`)
                if (presetCard) {
                  presetCard.scrollIntoView({ behavior: 'smooth', block: 'center' })
                  // Add a highlight effect
                  presetCard.classList.add('ring-2', 'ring-primary', 'ring-offset-2')
                  setTimeout(() => {
                    presetCard.classList.remove('ring-2', 'ring-primary', 'ring-offset-2')
                  }, 3000)
                }
              }, 500)

              // Open the view dialog for this preset if view parameter is present
              if (viewParam) {
                // First call viewPresetDetails to load the preset data
                viewPresetDetails(targetVersion);

                // Then force the dialog to be shown with a longer timeout to ensure everything is loaded
                setTimeout(() => {
                                    setShowViewDialog(true);
                }, 800);
              }
            }
          } catch (err) {
            console.error('Failed to fetch preset version:', err)
          }
        }
      }

      // Wait for presets to be loaded before trying to view
      if (presets && presets.length > 0 && !loading) {
        handlePresetView();
        // Mark URL parameters as processed
        hasProcessedUrlRef.current = true;
      } else {
        // Set up a one-time effect to handle viewing after presets are loaded
        const checkPresetsLoaded = () => {
          if (presets && presets.length > 0 && !loading) {
            handlePresetView();
            // Mark URL parameters as processed
            hasProcessedUrlRef.current = true;
            return true; // Cleanup
          }
          return false;
        };

        // Check immediately
        if (!checkPresetsLoaded()) {
          // If not loaded yet, set up an interval to check
          const intervalId = setInterval(() => {
            if (checkPresetsLoaded()) {
              clearInterval(intervalId);
            }
          }, 200);

          // Cleanup the interval if component unmounts
          return () => {
            clearInterval(intervalId);
          };
        }
      }
    } else {
      // Mark URL parameters as processed for other cases
      hasProcessedUrlRef.current = true;
    }
  }, [location.search, presets, loading, selectedRepoId]);

  // Fetch groups data when needed (only when creating or editing a preset)
  useEffect(() => {
    // Only fetch groups data if we're creating or editing a preset
    if (selectedRepoId && (showCreateDialog || showEditDialog)) {
            fetchGroupsData();
    }
  }, [selectedRepoId, showCreateDialog, showEditDialog])

  // Process groups data for LOBs and suggestions when it changes
  useEffect(() => {
    if (groupsData.length > 0) {
      processGroupsData()
    }
  }, [groupsData])

  // Check for reports when presets are loaded
  const hasCheckedReportsRef = useRef<boolean>(false);
  const lastPresetsLengthRef = useRef<number>(0);

  useEffect(() => {
    // Skip if presets aren't loaded yet
    if (!presets || presets.length === 0) {
      return;
    }

    // Skip if we've already checked reports for this set of presets
    if (hasCheckedReportsRef.current && lastPresetsLengthRef.current === presets.length) {
      return;
    }

    // Update the last presets length
    lastPresetsLengthRef.current = presets.length;

        const fetchReports = async () => {
      try {
        // Mark as checked before making the API call to prevent loops
        hasCheckedReportsRef.current = true;

        const reports = await apiClient.data.getReports(selectedRepoId)

        // Create a map of presetIds that have reports
        const presetIdsWithReports = new Set();

        // First, collect all presetIds that have reports
        reports.forEach(report => {
          if (report.presetId) {
            presetIdsWithReports.add(report.presetId);

            // Also find the parent preset if this is a versioned preset
            const preset = presets.find(p => p.id === report.presetId);
            if (preset && preset.parentId) {
              presetIdsWithReports.add(preset.parentId);
            }
          }
        });


        // Add hasReports property to each preset
        if (presetIdsWithReports.size > 0 && presets) {
          const updatedPresets = presets.map(preset => {
            // Check if this preset has reports directly
            const hasDirectReports = presetIdsWithReports.has(preset.id);

            // Check if this is a parent preset with versions that have reports
            const isParentWithReports = preset.parentId ?
              presetIdsWithReports.has(preset.parentId) :
              presets.some(p => p.parentId === preset.id && presetIdsWithReports.has(p.id));

            return {
              ...preset,
              hasReports: hasDirectReports || isParentWithReports
            };
          });

          setPresets(updatedPresets);
                  }
      } catch (err) {
        console.error('Failed to check presets with reports:', err)
      }
    }

    fetchReports()

    // Reset the ref when the component unmounts or repository changes
    return () => {
      hasCheckedReportsRef.current = false;
    };
  }, [presets?.length, selectedRepoId, setPresets])

  // Listen for custom events from PresetCardActions
  useEffect(() => {
    const handleToggleActivation = (event: CustomEvent) => {
      const presetId = event.detail;
      togglePresetActivation(presetId);
    };

    const handleDeletePreset = (event: CustomEvent) => {
      const presetId = event.detail;
      setDeletePresetId(presetId);
      setShowConfirmDelete(true);
    };

    const handleOpenViewDialog = (event: CustomEvent) => {
      const presetId = event.detail;

      // Find the preset in the list
      const preset = presets?.find(p => p.id === presetId);
      if (preset) {
                // Set the view preset
        setViewPreset(preset);
        // Open the dialog
        setShowViewDialog(true);
      }
    };

    window.addEventListener('togglePresetActivation', handleToggleActivation as EventListener);
    window.addEventListener('deletePreset', handleDeletePreset as EventListener);
    window.addEventListener('openPresetViewDialog', handleOpenViewDialog as EventListener);

    return () => {
      window.removeEventListener('togglePresetActivation', handleToggleActivation as EventListener);
      window.removeEventListener('deletePreset', handleDeletePreset as EventListener);
      window.removeEventListener('openPresetViewDialog', handleOpenViewDialog as EventListener);
    };
  }, [togglePresetActivation, setDeletePresetId, setShowConfirmDelete, presets, setViewPreset]);

  // Handle creating a new preset
  const handleCreatePreset = async (formData: PresetFormState) => {
    try {
      await createPreset(formData)
      setShowCreateDialog(false)

      // Clear URL query parameters after successful creation
      // This prevents the creation form from showing again if the page is refreshed
      if (location.search) {
        // Use navigate to redirect to the preset list without query parameters
        navigate('/report-presets', { replace: true })
      }
    } catch (err) {
      console.error('Failed to create preset:', err)
    }
  }

  // Handle updating a preset
  const handleUpdatePreset = async (formData: PresetFormState) => {
    if (!currentPreset || !currentPreset.id) return

    try {
      await updatePreset(currentPreset.id, formData)
      setShowEditDialog(false)
      setCurrentPreset(null)
    } catch (err) {
      console.error('Failed to update preset:', err)
    }
  }

  // Open edit dialog
  const editPreset = (preset: ReportPreset) => {
    setCurrentPreset(preset)
    setShowEditDialog(true)
  }

  // View preset details
  const viewPresetDetails = async (preset: ReportPreset) => {

    // Set the view preset first
    setViewPreset(preset);

    // If the preset has a parent ID, fetch available versions
    if (preset.parentId) {
      try {
                const versions = await apiClient.data.getReportPresetVersions(selectedRepoId, preset.parentId);
        const versionNumbers = versions.map(v => v.version || 1);
                setAvailableVersions(versionNumbers);
        setVersionInputValue(preset.version?.toString() || '1');
      } catch (err) {
        console.error('Failed to fetch preset versions:', err);
        setAvailableVersions([preset.version || 1]);
      }
    } else if (preset.version) {
      // If the preset has a version but no parent ID, it might be the parent itself
      try {
                const versions = await apiClient.data.getReportPresetVersions(selectedRepoId, preset.id!);
        const versionNumbers = [1, ...versions.map(v => v.version || 1)];
                setAvailableVersions(versionNumbers);
        setVersionInputValue(preset.version?.toString() || '1');
      } catch (err) {
        console.error('Failed to fetch preset versions:', err);
        setAvailableVersions([preset.version || 1]);
      }
    } else {
      // If the preset has no parent ID or version, it's a single preset
            setAvailableVersions([1]);
      setVersionInputValue('1');
    }

    // Always open the view dialog
        setTimeout(() => {
      setShowViewDialog(true);
    }, 100);

    // Return a promise that resolves when everything is set up
    return Promise.resolve();
  }

  // Handle version input change
  const handleVersionInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setVersionInputValue(e.target.value)
  }

  // Handle version input submit
  const handleVersionInputSubmit = () => {
    const version = parseInt(versionInputValue, 10)
    if (isNaN(version) || !availableVersions.includes(version)) {
      // Reset to current version
      setVersionInputValue(viewPreset?.version?.toString() || '1')
      return
    }

    navigateToVersion(version)
  }

  // Navigate to a specific version
  const navigateToVersion = async (version: number) => {
    if (!viewPreset || !viewPreset.parentId) return

    setVersionNavigating(true)

    try {
      // Find the version in the current presets
      const existingVersion = presets?.find(p =>
        (p.parentId === viewPreset.parentId && p.version === version) ||
        (p.id === viewPreset.parentId && version === 1)
      )

      if (existingVersion) {
        setViewPreset(existingVersion)
        setVersionInputValue(version.toString())
      } else {
        // Fetch the version from the API
        const versions = await apiClient.data.getReportPresetVersions(selectedRepoId, viewPreset.parentId)
        const targetVersion = versions.find(v => v.version === version)

        if (targetVersion) {
          setViewPreset(targetVersion)
          setVersionInputValue(version.toString())

          // Add this version to the presets list if it's not already there
          setPresets((prev: ReportPreset[] | null) => {
            if (!prev || !prev.some((p: ReportPreset) => p.id === targetVersion.id)) {
              return [...(prev || []), targetVersion]
            }
            return prev
          })
        } else {
          // Reset to current version
          setVersionInputValue(viewPreset.version?.toString() || '1')
        }
      }
    } catch (err) {
      console.error('Failed to navigate to version:', err)
      // Reset to current version
      setVersionInputValue(viewPreset.version?.toString() || '1')
    } finally {
      setVersionNavigating(false)
    }
  }

  // Open execution history dialog
  const openHistoryDialog = (preset: ReportPreset) => {
    setHistoryPresetId(preset.id!)
    setHistoryPresetName(preset.name || '')
    setHistorySharedPresetId(preset.sharedId || '')
    setShowHistoryDialog(true)
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Report Presets</h1>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" /> Create New Preset
        </Button>
      </div>

      <PresetsList
        presets={presets}
        loading={loading}
        error={error}
        onEdit={editPreset}
        onView={viewPresetDetails}
        onGenerate={generateReport}
        onOpenHistory={openHistoryDialog}
      />

      {/* Create Preset Dialog */}
      <CreatePresetDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleCreatePreset}
        initialValues={
          urlParamsRef.current && urlParamsRef.current.createFromQuery
            ? {
                searchQuery: urlParamsRef.current.createFromQuery === 'true'
                  ? urlParamsRef.current.searchQuery || ''
                  : urlParamsRef.current.createFromQuery,
                reportType: urlParamsRef.current.reportType
              }
            : undefined
        }
        groupsData={groupsData}
        availableLobs={availableLobs}
        availableGroupTypes={availableGroupTypes}
        availableGroups={availableGroups}
        availableUsers={availableUsers}
      />

      {/* Edit Preset Dialog */}
      <EditPresetDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSubmit={handleUpdatePreset}
        preset={currentPreset}
        groupsData={groupsData}
        availableLobs={availableLobs}
        availableGroupTypes={availableGroupTypes}
        availableGroups={availableGroups}
        availableUsers={availableUsers}
      />

      {/* View Preset Dialog */}
      <ViewPresetDialog
        open={showViewDialog}
        onOpenChange={setShowViewDialog}
        preset={viewPreset}
        availableVersions={availableVersions}
        onNavigateToVersion={navigateToVersion}
        versionInputValue={versionInputValue}
        onVersionInputChange={handleVersionInputChange}
        onVersionInputSubmit={handleVersionInputSubmit}
        versionNavigating={versionNavigating}
      />

      {/* Confirm Delete Dialog */}
      <DeletePresetDialog
        open={showConfirmDelete}
        onOpenChange={setShowConfirmDelete}
        onConfirm={deletePreset}
      />

      {/* Execution History Dialog */}
      <ExecutionHistoryDialog
        presetId={historyPresetId}
        presetName={historyPresetName}
        open={showHistoryDialog}
        onOpenChange={setShowHistoryDialog}
        sharedPresetId={historySharedPresetId}
      />
    </div>
  )
}

export default ReportPresetsPage
