import { useState, useEffect, useCallback, useRef } from 'react'
import { Trash, Download, FileJson, Database, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { useFilters } from '@/components/layout/Layout'
import { useRepository } from '@/context/RepositoryContext'
import { apiClient } from '@/api/client'
import { Report } from '@/api/client'
import { useNavigate, useLocation } from 'react-router-dom'
import ReportFilters from './Reports/ReportFilters'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"

const Reports = () => {
  const { toast } = useToast()
  const navigate = useNavigate()
  const location = useLocation()
  const { setFilters } = useFilters()
  const { selectedRepoId } = useRepository()

  // State
  const [reports, setReports] = useState<Report[] | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [filteredReports, setFilteredReports] = useState<Report[]>([])
  const [deleteReportId, setDeleteReportId] = useState<string | null>(null)
  const [showConfirmDelete, setShowConfirmDelete] = useState<boolean>(false)
  const [isApiFetching, setIsApiFetching] = useState<boolean>(false)
  const [selectedReports, setSelectedReports] = useState<string[]>([])
  const [showBatchDeleteConfirm, setShowBatchDeleteConfirm] = useState<boolean>(false)

  // Reference to search input to maintain focus
  const searchInputRef = useRef<HTMLInputElement | null>(null)

  // Handle search submission - only called when user presses Enter
  const handleSearch = (value: string) => {

    // Update the search query
    setSearchQuery(value);

    // Update URL params
    const params = new URLSearchParams(location.search);

    if (value) {
      // Update both 'query' and 'search' parameters for compatibility
      params.set('query', value);
      params.set('search', value);
    } else {
      params.delete('query');
      params.delete('search');
    }

    // Update URL without causing a full navigation
    const newUrl = `${location.pathname}?${params.toString()}`;
    window.history.replaceState(null, '', newUrl);

    // Apply filtering immediately
    applyFilters(value);
  }

  // Memoized filters component to prevent unnecessary re-renders
  const MemoizedReportFilters = useCallback(() => {
    return (
      <ReportFilters
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        searchInputRef={searchInputRef}
        reports={reports || []}
        handleSearch={handleSearch}
      />
    );
  // Include searchQuery in the dependency array to ensure the component updates when the query changes
  // This is necessary for the search input to display the correct value from URL parameters
  }, [reports, loading, searchQuery, handleSearch])

  // Set filters in the top bar
  useEffect(() => {
    setFilters(<MemoizedReportFilters />)

    // Cleanup when component unmounts
    return () => {
      setFilters(null)
    }
  }, [setFilters, MemoizedReportFilters])

  // Reset API fetching state after loading completes
  useEffect(() => {
        // When loading changes from true to false, it means API call completed
    if (!loading && isApiFetching) {
            setIsApiFetching(false);
      // No longer restoring focus to search input
    }
  }, [loading, isApiFetching])

  // Parse URL parameters for search query
  useEffect(() => {
    // Parse URL parameters
    const params = new URLSearchParams(location.search);

    // Get search query - check both 'query' and 'search' parameters
    const queryParam = params.get('query');
    const searchParam = params.get('search');
    const presetIdParam = params.get('presetId');
    const sharedPresetIdParam = params.get('sharedPresetId');

    // If sharedPresetId is provided, create a search query for it
    if (sharedPresetIdParam) {
      const sharedPresetSearchQuery = `sharedPreset:${sharedPresetIdParam}`;
            setSearchQuery(sharedPresetSearchQuery);
      // Apply filters when URL changes
      applyFilters(sharedPresetSearchQuery);
      return;
    }

    // If presetId is provided, create a search query for it
    if (presetIdParam) {
      const presetSearchQuery = `preset:${presetIdParam}`;
            setSearchQuery(presetSearchQuery);
      // Apply filters when URL changes
      applyFilters(presetSearchQuery);
      return;
    }

    // Otherwise use the regular search parameters
    const effectiveSearchParam = queryParam || searchParam;

    if (effectiveSearchParam) {
            setSearchQuery(effectiveSearchParam);
      // Apply filters when URL changes
      applyFilters(effectiveSearchParam);
    } else {
            setSearchQuery('');
      // Apply filters with empty query
      applyFilters('');
    }
  }, [location.search]);

  // Fetch reports on mount and when repository changes
  useEffect(() => {
    fetchReports()
  }, [selectedRepoId])

  // Function to filter reports by ID (used by the custom event)
  const filterReportById = (reportId: string) => {
    if (!reports || !reports.length) {
      return;
    }


    // Find the report with the matching ID
    const report = reports.find(r => r.id === reportId);

    if (report) {
            // Use the ID filter which we've added support for
      const newQuery = `id:${reportId}`;

      // Update the search query state
      setSearchQuery(newQuery);

      // Update URL params
      const params = new URLSearchParams(location.search);
      params.set('query', newQuery);
      const newUrl = `${location.pathname}?${params.toString()}`;
      window.history.replaceState(null, '', newUrl);

      // Apply the filter
      applyFilters(newQuery);

      // Focus and select the search input for easy editing
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
          searchInputRef.current.select();
        }
      }, 100);
    } else {
            // If no matching report is found, use the ID syntax
      const newQuery = `id:${reportId}`;
      setSearchQuery(newQuery);

      // Update URL params
      const params = new URLSearchParams(location.search);
      params.set('query', newQuery);
      const newUrl = `${location.pathname}?${params.toString()}`;
      window.history.replaceState(null, '', newUrl);

      applyFilters(newQuery);

      // Focus and select the search input for easy editing
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
          searchInputRef.current.select();
        }
      }, 100);
    }
  };

  // Function to apply filters based on search query
  const applyFilters = (query: string) => {
    if (!reports || !reports.length) {
      setFilteredReports([])
      return
    }

    let filtered = [...reports]

    if (query) {
      // Parse the search query to handle specific filters
      const filenameMatch = query.match(/filename:([^\s]+)/)
      const presetMatch = query.match(/preset:([^\s]+)/)
      const sharedPresetMatch = query.match(/sharedPreset:([^\s]+)/)
      const typeMatch = query.match(/type:([^\s]+)/)
      const dateMatch = query.match(/date:([^\s]+)/)
      const reportIdMatch = query.match(/id:([^\s]+)/)

      // Apply filename filter
      if (filenameMatch && filenameMatch[1]) {
        const filenameQuery = filenameMatch[1].toLowerCase()
        filtered = filtered.filter(report =>
          report.filename.toLowerCase().includes(filenameQuery)
        )
      }

      // Apply shared preset filter
      if (sharedPresetMatch && sharedPresetMatch[1]) {
        const sharedPresetQuery = sharedPresetMatch[1].toLowerCase()

        filtered = filtered.filter(report => {
          // Check if the shared preset ID matches exactly
          if (report.sharedPresetId && report.sharedPresetId.toLowerCase() === sharedPresetQuery) {
                        return true;
          }
          return false;
        })

              }

      // Apply preset filter
      else if (presetMatch && presetMatch[1]) {
        const presetQuery = presetMatch[1].toLowerCase()

        filtered = filtered.filter(report => {
          // Check if the preset ID matches exactly
          if (report.presetId && report.presetId.toLowerCase() === presetQuery) {
                        return true;
          }
          // Or if the preset name includes the query
          const nameMatch = report.presetName && report.presetName.toLowerCase().includes(presetQuery);
          if (nameMatch) {
                      }
          return nameMatch;
        })

              }

      // Apply type filter
      if (typeMatch && typeMatch[1]) {
        const typeQuery = typeMatch[1].toLowerCase()
        filtered = filtered.filter(report =>
          report.type.toLowerCase() === typeQuery
        )
      }

      // Apply ID filter
      if (reportIdMatch && reportIdMatch[1]) {
        const idQuery = reportIdMatch[1].toLowerCase()

        filtered = filtered.filter(report => {
          // Check if the report ID matches exactly
          if (report.id.toLowerCase() === idQuery) {
                        return true;
          }
          return false;
        })

              }

      // Apply date filter
      if (dateMatch && dateMatch[1]) {
        const dateQuery = dateMatch[1].toLowerCase()
        const now = new Date()
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)

        const thisWeekStart = new Date(today)
        thisWeekStart.setDate(today.getDate() - today.getDay())

        const lastWeekStart = new Date(thisWeekStart)
        lastWeekStart.setDate(thisWeekStart.getDate() - 7)
        const lastWeekEnd = new Date(thisWeekStart)
        lastWeekEnd.setDate(lastWeekEnd.getDate() - 1)

        const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)

        filtered = filtered.filter(report => {
          const reportDate = new Date(report.createdAt)

          switch (dateQuery) {
            case 'today':
              return reportDate >= today
            case 'yesterday':
              return reportDate >= yesterday && reportDate < today
            case 'thisweek':
              return reportDate >= thisWeekStart
            case 'lastweek':
              return reportDate >= lastWeekStart && reportDate <= lastWeekEnd
            case 'thismonth':
              return reportDate >= thisMonthStart
            case 'lastmonth':
              return reportDate >= lastMonthStart && reportDate <= lastMonthEnd
            default:
              return true
          }
        })
      }

      // If no specific filters were used, apply general search
      if (!filenameMatch && !presetMatch && !sharedPresetMatch && !typeMatch && !dateMatch && !reportIdMatch) {
        filtered = filtered.filter(report =>
          report.filename.toLowerCase().includes(query.toLowerCase()) ||
          (report.presetName && report.presetName.toLowerCase().includes(query.toLowerCase())) ||
          report.id.toLowerCase().includes(query.toLowerCase())
        )
      }
    }

    // Sort by created date (newest first)
    filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    setFilteredReports(filtered)
  }

  // Apply filters when reports change or when URL parameters change
  useEffect(() => {
    applyFilters(searchQuery)
  }, [reports])

  // Add event listener for custom report ID filtering event
  useEffect(() => {
    const handleFilterByReportId = (event: CustomEvent) => {
      const { reportId } = event.detail;
      if (reportId) {
                filterReportById(reportId);
      }
    };

    // Add event listener
    window.addEventListener('filter-report-by-id', handleFilterByReportId as EventListener);

    // Clean up
    return () => {
      window.removeEventListener('filter-report-by-id', handleFilterByReportId as EventListener);
    };
  }, [reports]); // Re-add listener when reports change

  // Fetch reports
  const fetchReports = async () => {
    // No longer tracking focus state

    setLoading(true)
    setError(null)
    setIsApiFetching(true)

    try {
      // Pass the repository ID to filter reports
      const reports = await apiClient.data.getReports(selectedRepoId)
            setReports(reports)
    } catch (err) {
      console.error(err)
      setError('Failed to fetch reports')
      toast({
        title: "Error",
        description: "Failed to fetch reports",
        variant: "destructive",
      })
    } finally {
      setLoading(false)

      // No longer restoring focus to search input after API call
          }
  }

  // Delete report
  const deleteReport = async () => {
    if (!deleteReportId) return

    // No longer tracking focus state

    setIsApiFetching(true)

    try {
      await apiClient.data.deleteReport(selectedRepoId, deleteReportId)

      // Update local state
      setReports(prevReports => prevReports ? prevReports.filter(report => report.id !== deleteReportId) : null)

      toast({
        title: "Success",
        description: "Report deleted successfully",
      })
    } catch (err) {
      console.error(err)
      toast({
        title: "Error",
        description: "Failed to delete report",
        variant: "destructive",
      })
    } finally {
      setShowConfirmDelete(false)
      setDeleteReportId(null)

      // No longer restoring focus to search input after API call
          }
  }

  // Batch delete reports
  const batchDeleteReports = async () => {
    if (selectedReports.length === 0) return

    setIsApiFetching(true)

    try {
      const result = await apiClient.data.batchDeleteReports(selectedRepoId, selectedReports)

      // Update local state
      setReports(prevReports => prevReports ? prevReports.filter(report => !selectedReports.includes(report.id)) : null)

      // Clear selection
      setSelectedReports([])

      // Show success message
      if (result.failedCount > 0) {
        toast({
          title: "Partial Success",
          description: `Successfully deleted ${result.successCount} of ${result.totalCount} reports.`,
          variant: "default",
        })
      } else {
        toast({
          title: "Success",
          description: `Successfully deleted ${result.successCount} reports.`,
        })
      }
    } catch (err) {
      console.error(err)
      toast({
        title: "Error",
        description: "Failed to delete reports",
        variant: "destructive",
      })
    } finally {
      setShowBatchDeleteConfirm(false)
    }
  }

  // Toggle selection of a report
  const toggleReportSelection = (reportId: string) => {
    setSelectedReports(prev => {
      if (prev.includes(reportId)) {
        return prev.filter(id => id !== reportId)
      } else {
        return [...prev, reportId]
      }
    })
  }

  // Toggle selection of all reports
  const toggleSelectAll = () => {
    if (selectedReports.length === filteredReports.length) {
      // If all are selected, deselect all
      setSelectedReports([])
    } else {
      // Otherwise, select all
      setSelectedReports(filteredReports.map(report => report.id))
    }
  }

  // Navigate to presets page
  const navigateToPresets = () => {
    navigate('/report-presets')
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Get report type badge
  const getReportTypeBadge = (type: string) => {
    switch (type) {
      case 'users':
        return <Badge className="bg-blue-500">Users</Badge>
      case 'groups':
        return <Badge className="bg-green-500">Groups</Badge>
      case 'both':
        return <Badge className="bg-purple-500">Users & Groups</Badge>
      default:
        return <Badge>Unknown</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Reports</h1>
          <p className="text-muted-foreground">View and download generated reports</p>
        </div>
        <div className="flex gap-2">
          {selectedReports.length > 0 && (
            <Button
              variant="destructive"
              onClick={() => setShowBatchDeleteConfirm(true)}
              title="Delete selected reports"
            >
              <Trash className="mr-2 h-4 w-4" /> Delete Selected ({selectedReports.length})
            </Button>
          )}
          <Button variant="outline" onClick={fetchReports} title="Refresh reports list">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4">
              <path d="M21 2v6h-6"></path>
              <path d="M3 12a9 9 0 0 1 15-6.7L21 8"></path>
              <path d="M3 22v-6h6"></path>
              <path d="M21 12a9 9 0 0 1-15 6.7L3 16"></path>
            </svg>
            Refresh
          </Button>
          <Button onClick={navigateToPresets}>
            <ArrowRight className="mr-2 h-4 w-4" /> Manage Presets
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-destructive/20 text-destructive p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : filteredReports.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <FileJson className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Reports Found</h3>
            <p className="text-muted-foreground mb-4">
              {!reports || reports.length === 0
                ? "You haven't generated any reports yet."
                : "No reports match your search criteria."}
            </p>
            <Button onClick={navigateToPresets}>
              Go to Report Presets
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={filteredReports.length > 0 && selectedReports.length === filteredReports.length}
                  onCheckedChange={toggleSelectAll}
                  aria-label="Select all reports"
                />
              </TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Source</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredReports.map((report, index) => (
              <TableRow key={`${report.id}_${index}`}>
                <TableCell>
                  <Checkbox
                    checked={selectedReports.includes(report.id)}
                    onCheckedChange={() => toggleReportSelection(report.id)}
                    aria-label={`Select report ${report.filename}`}
                  />
                </TableCell>
                <TableCell className="font-medium">{report.filename}</TableCell>
                <TableCell>{getReportTypeBadge(report.type)}</TableCell>
                <TableCell>
                  {report.presetName && report.presetId
                    ? <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="j-6 px-2 py-0 h-auto font-normal text-xs bg-blue-50 hover:bg-blue-100 border-blue-200"
                          onClick={() => navigate(`/report-presets?preset=${report.sharedPresetId || report.presetId?.split('_')[0] || report.presetId}&version=${report.presetVersion ||
                            (report.presetId && report.presetId.includes('_') ?
                              parseInt(report.presetId.split('_')[1].slice(-1)) || 1 : 1)}&view=true`)}
                          title="View this preset version in read-only mode"
                        >
                          <span className="flex items-center gap-1">
                            <Database className="h-3 w-3" />
                            {report.presetName} (v{report.presetVersion ||
                              (report.presetId && report.presetId.includes('_') ?
                                parseInt(report.presetId.split('_')[1].slice(-1)) || 1 : 1)})
                          </span>
                        </Button>
                      </div>
                    : "Manual Export"}
                </TableCell>
                <TableCell>{formatFileSize(report.size)}</TableCell>
                <TableCell>{formatDate(report.createdAt)}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <a href={report.downloadUrl} download>
                        <Download className="h-4 w-4 mr-2" /> Download
                      </a>
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => {
                            setDeleteReportId(report.id)
                            setShowConfirmDelete(true)
                          }}
                        >
                          <Trash className="h-4 w-4 mr-2" /> Delete
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will permanently delete the report file.
                            This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel onClick={() => setDeleteReportId(null)}>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={deleteReport}>Delete</AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {/* Confirm Delete Dialog */}
      <AlertDialog open={showConfirmDelete} onOpenChange={setShowConfirmDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this report file.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteReportId(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={deleteReport} disabled={isApiFetching}>
              {isApiFetching ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Batch Delete Confirmation Dialog */}
      <AlertDialog open={showBatchDeleteConfirm} onOpenChange={setShowBatchDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Multiple Reports</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to delete {selectedReports.length} report{selectedReports.length !== 1 ? 's' : ''}.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={batchDeleteReports} disabled={isApiFetching}>
              {isApiFetching ? 'Deleting...' : 'Delete All Selected'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

export default Reports
