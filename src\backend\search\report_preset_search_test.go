package search

import (
	"testing"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
)

func TestReportPresetWithSearchQuery(t *testing.T) {
	// Create test groups
	groups := []models.Group{
		{
			Groupname: "group1",
			Lob:       "Marketing",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group2",
			Lob:       "Engineering",
			Type:      "distribution",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group3",
			Lob:       "Finance",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
	}

	// Create a preset with search query
	preset := models.ReportPreset{
		ID:          "test-preset-id",
		Name:        "Security Groups",
		ReportType:  "groups",
		SearchQuery: "type:security",
		Query:       models.QueryParams{}, // Empty query params since we're using search query
	}

	// Create a simple implementation of search filtering for testing
	filterBySearchQuery := func(groups []models.Group, searchQuery string) []models.Group {
		// This is a simplified version of what the search service would do
		// In a real implementation, this would use the search engine

		var result []models.Group

		// Simple parsing of the search query
		if searchQuery == "type:security" {
			// Filter for security groups
			for _, group := range groups {
				if group.Type == "security" {
					result = append(result, group)
				}
			}
		}

		return result
	}

	// Filter groups using the search query
	filteredGroups := filterBySearchQuery(groups, preset.SearchQuery)

	// Verify that only security groups are included
	assert.Len(t, filteredGroups, 2)
	for _, group := range filteredGroups {
		assert.Equal(t, "security", group.Type)
	}
	assert.Contains(t, []string{"group1", "group3"}, filteredGroups[0].Groupname)
	assert.Contains(t, []string{"group1", "group3"}, filteredGroups[1].Groupname)
}
