package repository

import (
	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"
)

// Repository interface defines methods that any repository implementation must support
type Repository interface {
	// Initialize the client connection to the repository service
	InitClient() error

	// Start and stop polling for repository changes
	StartPolling() error
	StopPolling()

	// Register a callback to be notified of repository changes
	RegisterChangeCallback(callback func())

	// Manually sync the repository
	SyncRepository() error

	// Get the current status of the repository
	GetRepositoryStatus() models.RepositoryStatus

	// Local repository management
	EnsureLocalRepoExists() error
	GetLocalRepoPath() string

	// Configuration updates
	UpdateConfig(config models.RepositoryConfig) error

	// Get repository configuration
	GetConfig() models.RepositoryConfig

	// Get repository ID
	GetID() string
}

// NewRepository creates a new repository instance based on the repository type
func NewRepository(config models.RepositoryConfig, localRepoDir string, syncLogger *services.SyncLogger) Repository {
	switch config.Type {
	case models.GitLab:
		// Convert to GitLab config for backward compatibility
		glConfig := models.GitLabConfig(config)
		return NewGitLabRepository(glConfig, localRepoDir, syncLogger)
	case models.Bitbucket:
		return NewBitbucketRepository(config, localRepoDir, syncLogger)
	default:
		// Default to GitLab for backward compatibility
		glConfig := models.GitLabConfig(config)
		return NewGitLabRepository(glConfig, localRepoDir, syncLogger)
	}
}
