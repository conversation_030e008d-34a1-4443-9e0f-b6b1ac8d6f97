import { createRoot } from 'react-dom/client'
import { <PERSON><PERSON>erRout<PERSON> } from 'react-router-dom'
import App from './App'
import './index.css'

// Get the root element from the document
const rootElement = document.getElementById('root');

// Handle the case where the root element doesn't exist
if (!rootElement) {
  throw new Error(
    "Cannot find the 'root' element. Did you forget to add it to your index.html? Or maybe the id attribute is misspelled?"
  );
}

// Create a React root
const root = createRoot(rootElement);

// Render the app
root.render(
  <BrowserRouter>
    <App />
  </BrowserRouter>
);
