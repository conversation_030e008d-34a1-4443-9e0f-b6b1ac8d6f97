import { useState, useEffect } from 'react'
import { RefreshCw, CheckCircle, AlertCircle, Terminal } from 'lucide-react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { apiClient } from '@/api/client'
import { useToast } from '@/components/ui/use-toast'

interface SyncDetailsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  repoId: string
  repoName: string
}

export interface SyncLogEntry {
  timestamp: string;
  message: string;
  level: 'info' | 'warning' | 'error' | 'success';
  repoId?: string;
}

const SyncDetailsModal = ({ open, onOpenChange, repoId, repoName }: SyncDetailsModalProps) => {
  const [loading, setLoading] = useState(false)
  const [syncInProgress, setSyncInProgress] = useState(false)
  const [logs, setLogs] = useState<SyncLogEntry[]>([])
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [lastRefresh, setLastRefresh] = useState(new Date())
  const [refreshProgress, setRefreshProgress] = useState(0)
  const { toast } = useToast()

  // Simplified progress tracking - discrete steps without animation
  useEffect(() => {
    if (!autoRefresh) {
      setRefreshProgress(0);
      return;
    }

    // Use fixed steps with setInterval instead of animation frames
    // 20 steps over 2 seconds = 100ms intervals
    const stepSize = 5; // 5% increment per step
    const intervalTime = 100; // 100ms per step

    // Reset at the beginning of each cycle
    setRefreshProgress(0);

    const interval = setInterval(() => {
      setRefreshProgress(prev => {
        const next = prev + stepSize;
        return next <= 100 ? next : 100;
      });
    }, intervalTime);

    return () => clearInterval(interval);
  }, [autoRefresh, lastRefresh])

  // Fetch initial logs when modal opens
  useEffect(() => {
    if (open && repoId) {
      fetchSyncLogs()
    }
  }, [open, repoId])

  // Auto-refresh logs on interval when enabled
  useEffect(() => {
    if (!open || !autoRefresh) return

    const interval = setInterval(() => {
      fetchSyncLogs(true)
    }, 2000)

    return () => clearInterval(interval)
  }, [open, autoRefresh, repoId])

  // Fetch logs periodically if sync is in progress
  useEffect(() => {
    if (!open || !syncInProgress) return

    const interval = setInterval(() => {
      fetchSyncLogs(true)
    }, 2000)

    return () => clearInterval(interval)
  }, [open, syncInProgress])

  const fetchSyncLogs = async (isAutoRefresh = false) => {
    if (!repoId) return

    if (!isAutoRefresh) {
      setLoading(true)
    }

    try {
      // Get repository status to check if sync is in progress
      const response = await apiClient.repositories.getRepositoryStatus(repoId)

      // Check if sync is in progress
      if (response) {
        if (response.status) {
          // Format: { status: {...} }
          setSyncInProgress(response.status.syncInProgress)
        } else if ('syncInProgress' in response) {
          // Format: directly the status object
          setSyncInProgress(Boolean(response.syncInProgress))
        } else {
          console.error("Invalid repository status response format:", response)
          setSyncInProgress(false)
        }
      } else {
        console.error("Empty repository status response")
        setSyncInProgress(false)
      }

      // Extract the repository status from the response
      let repoStatus: any = null;
      if (response) {
        if (response.status) {
          repoStatus = response.status;
        } else if ('lastSync' in response || 'syncInProgress' in response) {
          repoStatus = response;
        }
      }


      // Get sync logs
      try {
        const logsResponse = await apiClient.repositories.getSyncLogs(repoId)

        // Convert API response to our log format
        if (logsResponse && logsResponse.logs && Array.isArray(logsResponse.logs)) {
          const formattedLogs: SyncLogEntry[] = logsResponse.logs.map(log => ({
            timestamp: log.timestamp,
            message: log.message,
            level: (log.level as 'info' | 'warning' | 'error' | 'success') ||
                   mapStatusToLevel(log.status || 'info')
          }))

          setLogs(formattedLogs)
          setLastRefresh(new Date())
        }
      } catch (error) {
        console.error('Error fetching sync logs:', error);

        // If we can't get logs but we have status, create at least one status entry
        if (repoStatus) {
          const statusLogs: SyncLogEntry[] = []

          if (repoStatus.lastSync) {
            statusLogs.push({
              timestamp: repoStatus.lastSync,
              message: `Repository sync process ${Boolean(repoStatus.syncInProgress) ? 'started' : 'completed'}`,
              level: Boolean(repoStatus.hasChanges) ? 'info' : 'success'
            })
          }

          if (repoStatus.lastCommit) {
            statusLogs.push({
              timestamp: repoStatus.lastSync || new Date().toISOString(),
              message: `Last commit: ${repoStatus.lastCommit}`,
              level: 'info'
            })
          }

          if (statusLogs.length > 0) {
                        setLogs(statusLogs)
            setLastRefresh(new Date())
          }
        }
      }
    } catch (error) {
      toast({
        title: "Failed to fetch logs",
        description: String(error),
        variant: "destructive"
      })
    } finally {
      if (!isAutoRefresh) {
        setLoading(false)
      }
    }
  }

  // Helper function to map API status to log level
  const mapStatusToLevel = (status: string): 'info' | 'warning' | 'error' | 'success' => {
    switch (status?.toLowerCase()) {
      case 'success':
        return 'success'
      case 'error':
        return 'error'
      case 'warning':
        return 'warning'
      default:
        return 'info'
    }
  }

  const startSync = async () => {
    if (!repoId || syncInProgress) return

    try {
      await apiClient.repositories.syncRepository(repoId)
      toast({
        title: "Sync started",
        description: "Repository synchronization has been initiated"
      })

      // Fetch status immediately after starting sync
      fetchSyncLogs(true)

      // No need to set syncInProgress here, as it will be set by fetchSyncLogs
    } catch (error) {
      toast({
        title: "Failed to start sync",
        description: String(error),
        variant: "destructive"
      })
    }
  }

  const getLogTextColor = (level: string) => {
    switch (level) {
      case 'success':
        return 'text-green-500'
      case 'error':
        return 'text-red-500'
      case 'warning':
        return 'text-amber-500'
      default:
        return 'text-gray-500'
    }
  }

  const getStatusIcon = (level: string) => {
    switch (level) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-amber-500" />
      default:
        return <Terminal className="h-4 w-4 text-blue-500" />
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[90vw] max-w-[90vw] h-[90vh] max-h-[90vh] flex flex-col p-6">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center justify-between text-xl">
            <div className="flex items-center gap-2">
              <Terminal className="h-6 w-6" />
              <span>Repository Sync Details: {repoName}</span>
            </div>
            <button
              onClick={() => onOpenChange(false)}
              className="rounded-full p-1 hover:bg-gray-200 dark:hover:bg-gray-800"
            >
            </button>
          </DialogTitle>
        </DialogHeader>

        <div className="mb-6 flex justify-between items-center">
          <div className="text-sm text-gray-500 flex items-center gap-2">
            {syncInProgress ? (
              <div className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
                <span>Sync in progress...</span>
              </div>
            ) : (
              <span>Sync details</span>
            )}
          </div>
          <div className="flex items-center gap-4">
            <div className="relative">
              <Button
                variant={autoRefresh ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  if (autoRefresh) {
                    // If auto-refresh is on, turn it off
                    setAutoRefresh(false);
                  } else {
                    // If auto-refresh is off, turn it on and do an immediate refresh
                    setAutoRefresh(true);
                    fetchSyncLogs();
                  }
                }}
                className={`relative overflow-hidden ${autoRefresh ? 'bg-green-500 hover:bg-green-600' : ''}`}
              >
                <span className="relative z-10">
                  <RefreshCw className={`h-4 w-4 mr-2 inline ${autoRefresh ? 'animate-spin' : ''}`} />
                  {autoRefresh ? 'Auto-Refresh: On' : 'Auto-Refresh: Off'}
                </span>

                {/* Progress bar */}
                {autoRefresh && (
                  <div className="h-1 w-full absolute bottom-0 left-0 right-0 z-0">
                    <div
                      className="h-full bg-green-600"
                      style={{ width: `${refreshProgress}%` }}
                    />
                  </div>
                )}
              </Button>
            </div>
            <Button
              size="sm"
              onClick={startSync}
              disabled={syncInProgress || loading}
            >
              {syncInProgress && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
              {syncInProgress ? 'Syncing...' : 'Start Sync'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                toast({
                  title: "Connection Information",
                  description: (
                    <div className="text-xs space-y-1 mt-2">
                      <p>Repository ID: {repoId}</p>
                      <p>Name: {repoName}</p>
                      <p>API Endpoint: /api/repositories/sync-logs/{repoId}</p>
                      <p>Last refresh: {lastRefresh.toLocaleTimeString()}</p>
                    </div>
                  ),
                });
              }}
            >
              Debug
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-auto py-2 min-h-[600px] border rounded">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : logs.length > 0 ? (
            <div className="space-y-2 px-4">
              {[...logs]
                .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                .map((log, index) => (
                <div key={index} className="py-3 border-b last:border-b-0">
                  <div className="flex justify-between items-start gap-2">
                    <div className="flex items-start gap-2">
                      <span className="shrink-0 mt-0.5">
                        {getStatusIcon(log.level)}
                      </span>
                      <div className={`flex-1 ${getLogTextColor(log.level)}`}>
                        <p className="whitespace-pre-wrap break-all text-sm">{log.message}</p>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground whitespace-nowrap">
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1 pl-6">
                    {new Date(log.timestamp).toLocaleDateString()} • {log.level}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
              <p>No logs available</p>
              <p className="text-xs mt-1">Check the browser console for API response details</p>
              <Button
                variant="link"
                size="sm"
                className="mt-2"
                onClick={() => {
                  fetchSyncLogs();
                }}
              >
                Retry Log Fetch
              </Button>
            </div>
          )}
        </div>

        <DialogFooter className="pt-6">
          <div className="flex-1 text-xs text-muted-foreground">
            Last refresh: {lastRefresh.toLocaleString()}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default SyncDetailsModal
