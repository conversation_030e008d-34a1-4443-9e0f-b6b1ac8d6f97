import { useState, useEffect } from 'react';
import { RefreshCw, Database, Users, FolderGit, FileText, ArrowRight, Calendar, Clock, FileCheck } from 'lucide-react';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { apiClient, ReportPreset, ReportExecution } from '@/api/client';
import { useRepository } from '@/context/RepositoryContext';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';


interface RepositoryStatsCardProps {
  repoId?: string; // Optional: If provided, will override the context repository
}

interface RepositoryStats {
  groups: number;
  users: number;
  presets: number;
  reports: number;
  totalSchedules: number;
  scheduledForToday: number;
  reportsGeneratedToday: number;
  lastUpdated: string;
}

const RepositoryStatsCard = ({ repoId }: RepositoryStatsCardProps = {}) => {
  const [stats, setStats] = useState<RepositoryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const { selectedRepoId, isLoading: contextLoading } = useRepository();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Use provided repoId or fall back to selected repo from context
  const currentRepoId = repoId || selectedRepoId;

  // Function to check if a preset is scheduled for today
  const isScheduledForToday = (preset: ReportPreset): boolean => {
    // Check if preset and its schedule exist and are enabled
    if (!preset || !preset.schedule || !preset.schedule.enabled) {
      return false;
    }

    const now = new Date();
    const today = now.getDay(); // 0-6, Sunday-Saturday
    const currentDate = now.getDate(); // 1-31

    // Get frequency with a fallback to prevent errors
    const frequency = preset.schedule.frequency || '';

    switch (frequency) {
      case 'daily':
        return true;
      case 'weekly':
        // Check if dayOfWeek exists and matches today
        return preset.schedule.dayOfWeek === today;
      case 'monthly':
        // Check if dayOfMonth exists and matches current date
        return preset.schedule.dayOfMonth === currentDate;
      case 'interval':
        // For interval schedules, check if there's a next run time for today
        if (preset.schedule.nextRun) {
          try {
            const nextRun = new Date(preset.schedule.nextRun);
            return nextRun.toDateString() === now.toDateString();
          } catch (e) {
            console.error('Error parsing nextRun date:', e);
            return false;
          }
        }
        return false;
      default:
        return false;
    }
  };

  // Function to check if a report execution happened today
  const isExecutedToday = (execution: ReportExecution): boolean => {
    // Check if execution and executedAt exist
    if (!execution || !execution.executedAt) {
      return false;
    }

    try {
      const executionDate = new Date(execution.executedAt);
      const today = new Date();

      return (
        executionDate.getDate() === today.getDate() &&
        executionDate.getMonth() === today.getMonth() &&
        executionDate.getFullYear() === today.getFullYear()
      );
    } catch (e) {
      console.error('Error parsing execution date:', e);
      return false;
    }
  };

  // Function to fetch repository statistics
  const fetchStats = async () => {
    if (!currentRepoId) {
      setStats(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      // Fetch groups count
      const groupsResponse = await apiClient.data.getGroups(currentRepoId);
      const groupsCount = groupsResponse?.pagination?.totalItems || 0;

      // Fetch users count
      const usersResponse = await apiClient.data.getUsers(currentRepoId);
      const usersCount = usersResponse?.pagination?.totalItems || 0;

      // Fetch report presets
      const presets = await apiClient.data.getReportPresets(currentRepoId) || [];
      const presetsCount = presets?.length || 0;

      // Fetch reports
      const reports = await apiClient.data.getReports(currentRepoId) || [];
      const reportsCount = reports?.length || 0;

      // Count total schedules (presets with scheduling enabled)
      const scheduledPresets = presets?.filter(preset =>
        preset?.isActive && preset?.schedule && preset?.schedule?.enabled
      ) || [];
      const totalSchedules = scheduledPresets?.length || 0;

      // Count presets scheduled for today
      const scheduledForToday = scheduledPresets?.filter(preset =>
        isScheduledForToday(preset)
      )?.length || 0;

      // Get all executions for all presets to count reports generated today
      let allExecutions: ReportExecution[] = [];

      // For each preset with a shared ID, fetch its executions
      const uniqueSharedIds = new Set((presets || []).map(preset => preset?.sharedId).filter(Boolean));

      for (const sharedId of uniqueSharedIds) {
        if (!sharedId) continue;

        try {
          // Use the first preset with this shared ID to fetch executions
          const executions = await apiClient.getReportExecutionsByPreset(sharedId, currentRepoId) || [];
          // Make sure executions is an array before spreading
          if (Array.isArray(executions)) {
            allExecutions = [...allExecutions, ...executions];
          } else {
            console.warn(`Executions for preset ${sharedId} is not an array:`, executions);
          }
        } catch (error) {
          console.error(`Error fetching executions for preset ${sharedId}:`, error);
        }
      }

      // Count executions that happened today and were successful
      const reportsGeneratedToday = allExecutions.filter(execution =>
        execution && execution.status === 'success' && isExecutedToday(execution)
      ).length || 0;

      // Set the stats
      setStats({
        groups: groupsCount,
        users: usersCount,
        presets: presetsCount,
        reports: reportsCount,
        totalSchedules,
        scheduledForToday,
        reportsGeneratedToday,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      toast({
        title: "Failed to load repository statistics",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
      console.error("Error fetching repository statistics:", error);

      // Set default stats for development/testing
      if (process.env.NODE_ENV === 'development') {
        setStats({
          groups: 0,
          users: 0,
          presets: 0,
          reports: 0,
          totalSchedules: 0,
          scheduledForToday: 0,
          reportsGeneratedToday: 0,
          lastUpdated: new Date().toISOString()
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch stats when the repository changes or on manual refresh
  useEffect(() => {
    if (!contextLoading && currentRepoId) {
      fetchStats();
    }
  }, [currentRepoId, contextLoading]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (e) {
      return 'Unknown';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Database className="h-6 w-6 text-blue-500" />
            <span>Repository Statistics</span>
          </div>
          <Button
            size="icon"
            variant="ghost"
            onClick={fetchStats}
            disabled={loading}
            title="Refresh Statistics"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
        <div className="text-sm text-muted-foreground mt-1.5">
          Overview of repository content
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        ) : stats ? (
          <div className="space-y-6">
            {/* Repository Content Statistics */}
            <div>
              <h3 className="text-sm font-medium mb-3">Repository Content</h3>
              <div className="grid grid-cols-2 gap-4">
                <div
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
                  onClick={() => navigate('/groups')}
                >
                  <div className="flex items-center">
                    <FolderGit className="h-5 w-5 text-green-500 mr-2" />
                    <span>Groups</span>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-lg font-semibold mr-1">
                      {stats.groups}
                    </Badge>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity text-gray-500" />
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
                  onClick={() => navigate('/users')}
                >
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-blue-500 mr-2" />
                    <span>Users</span>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-lg font-semibold mr-1">
                      {stats.users}
                    </Badge>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity text-gray-500" />
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
                  onClick={() => navigate('/report-presets')}
                >
                  <div className="flex items-center">
                    <Database className="h-5 w-5 text-purple-500 mr-2" />
                    <span>Presets</span>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-lg font-semibold mr-1">
                      {stats.presets}
                    </Badge>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity text-gray-500" />
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
                  onClick={() => navigate('/reports')}
                >
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 text-orange-500 mr-2" />
                    <span>Reports</span>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-lg font-semibold mr-1">
                      {stats.reports}
                    </Badge>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity text-gray-500" />
                  </div>
                </div>
              </div>
            </div>

            {/* Schedule Statistics */}
            <div>
              <h3 className="text-sm font-medium mb-3">Schedule Statistics</h3>
              <div className="grid grid-cols-1 gap-4">
                <div
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
                  onClick={() => navigate('/report-presets')}
                >
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-purple-500 mr-2" />
                    <span>Total Schedules</span>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-lg font-semibold mr-1">
                      {stats.totalSchedules}
                    </Badge>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity text-gray-500" />
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
                  onClick={() => navigate('/report-presets')}
                >
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-blue-500 mr-2" />
                    <span>Scheduled For Today</span>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-lg font-semibold mr-1">
                      {stats.scheduledForToday}
                    </Badge>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity text-gray-500" />
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
                  onClick={() => navigate('/reports')}
                >
                  <div className="flex items-center">
                    <FileCheck className="h-5 w-5 text-green-500 mr-2" />
                    <span>Generated Today</span>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-lg font-semibold mr-1">
                      {stats.reportsGeneratedToday}
                    </Badge>
                    <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity text-gray-500" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            <p>No statistics available</p>
          </div>
        )}
      </CardContent>
      {stats && (
        <CardFooter className="text-xs text-muted-foreground">
          Last updated: {formatDate(stats.lastUpdated)}
        </CardFooter>
      )}
    </Card>
  );
};

export default RepositoryStatsCard;
