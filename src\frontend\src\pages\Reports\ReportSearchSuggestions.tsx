import React, { useState, useEffect } from 'react';
import SearchInputWithSuggestions from '@/components/SearchInputWithSuggestions';
import { Report } from '@/api/client';

interface ReportSearchSuggestionsProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  onSearch: (value: string) => void;
  placeholder?: string;
  className?: string;
  searchInputRef: React.RefObject<HTMLInputElement>;
  reports?: Report[];
}

const ReportSearchSuggestions: React.FC<ReportSearchSuggestionsProps> = ({
  inputValue,
  setInputValue,
  onSearch,
  placeholder = 'Search reports...',
  className = '',
  searchInputRef,
  reports = []
}: ReportSearchSuggestionsProps) => {
  // State for suggestions
  const [reportTypes, setReportTypes] = useState<string[]>([]);
  const [presetNames, setPresetNames] = useState<string[]>([]);
  const [sharedPresetIds, setSharedPresetIds] = useState<string[]>([]);
  const [filenames, setFilenames] = useState<string[]>([]);

  // Process reports to extract suggestion data
  useEffect(() => {
    if (!reports || reports.length === 0) return;

    // Extract unique report types
    const types = Array.from(new Set(reports.map(report => report.type)));

    // Extract unique preset names
    const presets = Array.from(new Set(
      reports
        .filter(report => report.presetName)
        .map(report => report.presetName as string)
    ));

    // Extract unique shared preset IDs
    const sharedIds = Array.from(new Set(
      reports
        .filter(report => report.sharedPresetId)
        .map(report => report.sharedPresetId as string)
    ));

    // Extract filenames (without extension)
    const names = Array.from(new Set(
      reports.map(report => {
        const name = report.filename.split('.')[0];
        return name;
      })
    ));

    setReportTypes(types);
    setPresetNames(presets);
    setSharedPresetIds(sharedIds);
    setFilenames(names);
  }, [reports]);

  // Create suggestions array for the SearchInputWithSuggestions component
  const suggestions = [
    // Filter type suggestions for reports
    { type: 'filename', value: '', display: 'filename:' },
    { type: 'preset', value: '', display: 'preset:' },
    { type: 'sharedPreset', value: '', display: 'sharedPreset:' },
    { type: 'type', value: '', display: 'type:' },
    { type: 'date', value: '', display: 'date:' },
    { type: 'id', value: '', display: 'id:' },

    // Type values
    ...reportTypes.map(type => ({
      type: 'type',
      value: type,
      display: type
    })),

    // Preset name values
    ...presetNames.map(name => ({
      type: 'preset',
      value: name,
      display: name
    })),

    // Filename values
    ...filenames.map(name => ({
      type: 'filename',
      value: name,
      display: name
    })),

    // Shared preset ID values
    ...sharedPresetIds.map(id => ({
      type: 'sharedPreset',
      value: id,
      display: id
    })),

    // Common date formats
    { type: 'date', value: 'today', display: 'today' },
    { type: 'date', value: 'yesterday', display: 'yesterday' },
    { type: 'date', value: 'thisweek', display: 'thisweek' },
    { type: 'date', value: 'lastweek', display: 'lastweek' },
    { type: 'date', value: 'thismonth', display: 'thismonth' },
    { type: 'date', value: 'lastmonth', display: 'lastmonth' },
  ];

  // Log the input value for debugging

  return (
    <SearchInputWithSuggestions
      ref={searchInputRef}
      placeholder={placeholder}
      initialValue={inputValue}
      value={inputValue} // Add explicit value prop for controlled component
      onChange={setInputValue}
      onSearch={onSearch}
      className={className}
      suggestions={suggestions}
      autoFocus={false}
    />
  );
};

export default ReportSearchSuggestions;
