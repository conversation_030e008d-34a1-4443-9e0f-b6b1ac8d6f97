package services

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"adgitops-ui/src/backend/models"
)

func TestFilterGroups(t *testing.T) {
	// Create a data processor
	dp := &DataProcessor{}

	// Create test groups
	groups := []models.Group{
		{
			Groupname: "group1",
			Lob:       "Marketing",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group2",
			Lob:       "Engineering",
			Type:      "distribution",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group3",
			Lob:       "Finance",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
	}

	// Test LOB filter
	filteredGroups := dp.FilterGroups(groups, models.QueryParams{
		LOB: "Marketing",
	})
	assert.Len(t, filteredGroups, 1)
	assert.Equal(t, "group1", filteredGroups[0].Groupname)

	// Test type filter
	filteredGroups = dp.FilterGroups(groups, models.QueryParams{
		Types: []string{"security"},
	})
	assert.Len(t, filteredGroups, 2)
	assert.Contains(t, []string{"group1", "group3"}, filteredGroups[0].Groupname)
	assert.Contains(t, []string{"group1", "group3"}, filteredGroups[1].Groupname)

	// Test group ID filter
	filteredGroups = dp.FilterGroups(groups, models.QueryParams{
		GroupIDs: []string{"group2", "group3"},
	})
	assert.Len(t, filteredGroups, 2)
	assert.Contains(t, []string{"group2", "group3"}, filteredGroups[0].Groupname)
	assert.Contains(t, []string{"group2", "group3"}, filteredGroups[1].Groupname)

	// Test combined filters
	filteredGroups = dp.FilterGroups(groups, models.QueryParams{
		Types:    []string{"security"},
		GroupIDs: []string{"group1", "group2"},
	})
	assert.Len(t, filteredGroups, 1)
	assert.Equal(t, "group1", filteredGroups[0].Groupname)
}
