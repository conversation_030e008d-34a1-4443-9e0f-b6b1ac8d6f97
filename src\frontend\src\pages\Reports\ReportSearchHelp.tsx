import { HelpCircle } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const ReportSearchHelp = () => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          type="button"
          className="inline-flex items-center text-gray-500 hover:text-gray-700"
          aria-label="Search help"
        >
          <HelpCircle className="h-4 w-4" />
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-[80vw] max-w-[1200px]" side="bottom" align="center" sideOffset={5} avoidCollisions>
        <div className="p-2">
          <h3 className="text-2xs font-bold mb-0.5 border-b pb-0.5 text-blue-600">Report Search Guide</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <div>
              <div className="bg-blue-50 rounded-lg p-2 border border-blue-200">
                <h4 className="font-medium text-blue-700 mb-0.5 text-7xs">Basic Search Syntax</h4>
                <ul className="space-y-1">
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">text</span>
                    <span className="text-8xs">Search in report name and preset name</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">field:value</span>
                    <span className="text-8xs">Search for specific field values (e.g., type:users)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">date:period</span>
                    <span className="text-8xs">Filter by time period (e.g., date:today, date:thisweek)</span>
                  </li>
                </ul>
              </div>
            </div>

            <div>
              <div className="bg-blue-50 rounded-lg p-2 border border-blue-200">
                <h4 className="font-medium text-blue-700 mb-0.5 text-7xs">Searchable Fields</h4>
                <ul className="space-y-1">
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">filename</span>
                    <span className="text-8xs">Report filename</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">preset</span>
                    <span className="text-8xs">Preset ID or name used to generate the report</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">sharedPreset</span>
                    <span className="text-8xs">Shared preset ID (finds reports from all versions)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">type</span>
                    <span className="text-8xs">Report type (users, groups, both)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded font-mono text-8xs mr-1 w-[100px] text-center">id</span>
                    <span className="text-8xs">Report ID (exact match)</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="mt-2 bg-yellow-50 rounded-lg p-1.5 border border-yellow-200">
            <h4 className="font-medium text-yellow-700 mb-0.5 text-7xs">Examples</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-1.5">
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">finance</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find reports with "finance" in name or preset</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">type:users</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find user reports</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">preset:monthly</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find reports from presets with "monthly" in name</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">filename:export</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find reports with "export" in filename</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">date:today</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find reports created today</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">date:thisweek</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find reports created this week</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">type:groups date:thismonth</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find group reports created this month</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">preset:security type:both</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find combined reports from security presets</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">sharedPreset:abc123</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find all reports from all versions of a preset</p>
              </div>
              <div>
                <div className="bg-white p-0.5 rounded border border-yellow-300 mb-0.5 h-5 flex items-center justify-center">
                  <code className="text-9xs leading-tight">id:report_123456</code>
                </div>
                <p className="text-9xs text-gray-600 leading-tight">Find a specific report by its ID</p>
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ReportSearchHelp;
