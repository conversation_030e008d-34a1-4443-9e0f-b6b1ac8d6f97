package search

import (
	"adgitops-ui/src/backend/models"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseQuery(t *testing.T) {
	parser := NewParser()

	tests := []struct {
		name        string
		query       string
		expectError bool
	}{
		{"Empty query", "", false},
		{"Simple keyword", "admin", false},
		{"Exact match", "groupname:admin", false},
		{"Contains match", "groupname:~admin", false},
		{"Prefix match", "groupname:admin*", false},
		{"Suffix match", "groupname:*admin", false},
		{"Contains wildcard", "groupname:*admin*", false},
		{"Suffix match", "groupname:*min", false},
		{"Quoted string", "\"admin\"", false},
		{"AND operator", "type:security AND lob:fm", false},
		{"OR operator", "type:security OR type:admin", false},
		{"NOT operator", "NOT lob:fm", false},
		{"Complex query", "groupname:~admin AND (type:security OR lob:fm) NOT description:\"test group\"", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node, err := parser.ParseQuery(tt.query)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.query != "" {
					assert.NotNil(t, node)
				}
			}
		})
	}
}

func TestSearchGroups(t *testing.T) {
	engine := NewEngine()
	ctx := context.Background()

	// Create test groups
	groups := []models.Group{
		{
			Groupname:   "admin_group",
			Type:        "security",
			Description: "Admin security group",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "john", Type: models.UserMemberType},
				{Name: "jane", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "finance_team",
			Type:        "business",
			Description: "Finance department team",
			Lob:         "finance",
			Members: models.GroupMembers{
				{Name: "bob", Type: models.UserMemberType},
				{Name: "alice", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "dev_team",
			Type:        "technical",
			Description: "Development team",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "dave", Type: models.UserMemberType},
				{Name: "carol", Type: models.UserMemberType},
			},
		},
	}

	tests := []struct {
		name          string
		query         string
		expectedCount int
	}{
		{"Empty query", "", 3},
		{"Simple keyword", "admin", 1},
		{"Exact match", "groupname:admin_group", 1},
		{"Exact match (uppercase field)", "GROUPNAME:admin_group", 1},
		{"Contains match", "groupname:~team", 2},
		{"Contains match (mixed case field)", "GrOuPnAmE:~team", 2},
		{"Prefix match", "type:sec*", 1},
		{"Suffix match", "type:*cal", 1},
		{"Contains wildcard", "type:*nic*", 1},
		{"Member search", "members:john", 1},
		{"LOB search", "lob:fm", 1},
		{"Type search", "type:technical", 1},
		{"AND operator", "type:security AND lob:fm", 1},
		{"AND operator (lowercase)", "type:security and lob:fm", 1},
		{"OR operator", "lob:fm OR lob:it", 2},
		{"OR operator (lowercase)", "lob:fm or lob:it", 2},
		{"NOT operator", "NOT lob:fm", 2},
		{"NOT operator (lowercase)", "not lob:fm", 2},
		{"Wildcard with AND", "groupname:*team* AND lob:finance", 1},
		{"Wildcard with OR", "groupname:*team* OR lob:it", 2},
		{"Wildcard with NOT", "groupname:*team* NOT lob:it", 1},
		{"No matches", "groupname:nonexistent", 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := engine.SearchGroups(ctx, tt.query, groups)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedCount, len(results))
		})
	}
}

func TestSearchUsers(t *testing.T) {
	engine := NewEngine()
	ctx := context.Background()

	// Create test users
	users := []models.User{
		{
			Name:   "john",
			Groups: []string{"admin_group", "dev_team"},
			LOBs:   []string{"fm", "it"},
		},
		{
			Name:   "alice",
			Groups: []string{"finance_team"},
			LOBs:   []string{"finance"},
		},
		{
			Name:   "dave",
			Groups: []string{"dev_team"},
			LOBs:   []string{"it"},
		},
	}

	tests := []struct {
		name          string
		query         string
		expectedCount int
	}{
		{"Empty query", "", 3},
		{"Simple keyword", "john", 1},
		{"Exact match", "name:john", 1},
		{"Contains match", "name:~li", 1},
		{"Prefix match", "name:jo*", 1},
		{"Suffix match", "name:*ce", 1},
		{"Contains wildcard", "name:*a*", 2},
		{"Group search", "groups:admin_group", 1},
		{"LOB search", "lob:finance", 1},
		{"AND operator", "groups:dev_team AND lob:it", 2},
		{"OR operator", "name:john OR name:alice", 2},
		{"NOT operator", "NOT lob:finance", 2},
		{"No matches", "name:nonexistent", 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := engine.SearchUsers(ctx, tt.query, users)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedCount, len(results))
		})
	}
}

func TestBackwardCompatibility(t *testing.T) {
	service := NewService()

	// Test ParseQueryToMap
	query := "groupname:admin AND type:security"
	result, err := service.ParseQueryToMap(query)
	assert.NoError(t, err)
	assert.Contains(t, result, "groupname")
	assert.Contains(t, result, "type")

	// Test with a group
	group := models.Group{
		Groupname:   "admin_group",
		Type:        "security",
		Description: "Admin security group",
		Lob:         "fm",
	}

	// Create search criteria map
	searchCriteria := map[string][]string{
		"groupname": {"=admin_group"},
		"type":      {"=security"},
	}

	// Test MatchesAdvancedSearch
	assert.True(t, service.MatchesAdvancedSearch(group, searchCriteria))

	// Test with a user
	user := models.User{
		Name:   "john",
		Groups: []string{"admin_group"},
		LOBs:   []string{"fm"},
	}

	// Create search criteria map
	searchCriteria = map[string][]string{
		"name":   {"=john"},
		"groups": {"=admin_group"},
	}

	// Test MatchesAdvancedUserSearch
	assert.True(t, service.MatchesAdvancedUserSearch(user, searchCriteria))
}
