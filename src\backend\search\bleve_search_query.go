package search

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/blevesearch/bleve/v2"
	"github.com/blevesearch/bleve/v2/search/query"
)

// buildQuery builds a Bleve query from a search query string
func (s *BleveSearchService) buildQuery(query string, docType string) query.Query {
	// Debug output
	fmt.Printf("buildQuery: original query=%s, docType=%s\n", query, docType)

	// Check if the query contains wildcards
	if strings.Contains(query, "*") || strings.Contains(query, "?") {
		fmt.Printf("buildQuery: detected wildcard search in query: %s\n", query)

		// Special handling for name:casim* type queries
		wildcardRegex := regexp.MustCompile(`(name|groupname):([^\s"\(\)]+)\*`)
		wildcardMatches := wildcardRegex.FindStringSubmatch(query)
		if len(wildcardMatches) >= 3 {
			fieldName := wildcardMatches[1]
			prefix := wildcardMatches[2]
			fmt.Printf("buildQuery: detected prefix wildcard search: %s:%s*\n", fieldName, prefix)

			// Don't modify the query, let Bleve handle it
		}
	}

	// Handle field-specific searches for all searchable fields
	// This regex matches field:value patterns, capturing both the field name and value
	fieldRegex := regexp.MustCompile(`(\w+):([^\s"]+|"[^"]+")`)
	matches := fieldRegex.FindAllStringSubmatch(query, -1)

	for _, match := range matches {
		if len(match) < 3 {
			continue
		}

		fieldName := match[1]
		fieldValue := match[2]
		originalMatch := match[0]

		// Skip if it already has wildcards or it's a special field
		if strings.Contains(fieldValue, "*") || strings.Contains(fieldValue, "~") ||
			strings.HasPrefix(fieldValue, "(") {
			continue
		}

		// Special handling for _type field
		if fieldName == "_type" {
			// Keep as is, but add debug output
			fmt.Printf("buildQuery: keeping _type field as is: %s\n", originalMatch)
			continue
		}

		// Remove quotes if present
		fieldValue = strings.Trim(fieldValue, "\"")

		// By default, we don't modify the query - exact matching is the default behavior
		// The user can explicitly use wildcards (*) or the contains operator (~) if they want partial matching
		fmt.Printf("buildQuery: keeping exact match for %s: %s\n", fieldName, originalMatch)
	}

	// Preprocess the query to handle our custom syntax
	query = s.preprocessQuery(query)

	// Debug output
	fmt.Printf("buildQuery: preprocessed query=%s\n", query)

	// If the query is a simple string, handle it based on the query
	if !strings.Contains(query, ":") && !strings.Contains(query, " AND ") &&
		!strings.Contains(query, " OR ") && !strings.Contains(query, " NOT ") {

		// For simple queries, we need to search in all fields
		// Create a wildcard query for partial matching
		wildcardQuery := bleve.NewWildcardQuery("*" + query + "*")
		wildcardQuery.SetField("_all")

		// Also create a match query for exact matches
		matchQuery := bleve.NewMatchQuery(query)
		matchQuery.SetField("_all")

		// Create a document type query
		docTypeQuery := bleve.NewTermQuery(docType)
		docTypeQuery.SetField("_type")

		// Combine the queries with OR for the search queries and AND with the docType
		searchQueries := bleve.NewDisjunctionQuery(wildcardQuery, matchQuery)
		conjunctionQuery := bleve.NewConjunctionQuery(searchQueries, docTypeQuery)

		// Debug output
		fmt.Printf("buildQuery: simple query, using both wildcard and match queries for better matching: *%s*\n", query)
		return conjunctionQuery
	}

	// Parse the query string
	queryStringQuery := bleve.NewQueryStringQuery(query)

	// Create a document type query
	docTypeQuery := bleve.NewTermQuery(docType)
	docTypeQuery.SetField("_type")
	// Debug output
	fmt.Printf("buildQuery: created docTypeQuery for _type:%s\n", docType)

	// Combine the queries with AND
	conjunctionQuery := bleve.NewConjunctionQuery(queryStringQuery, docTypeQuery)

	// Debug output
	fmt.Printf("buildQuery: complex query, returning conjunction of queryString and docType=%s (IMPORTANT DEBUG)\n", docType)
	return conjunctionQuery
}

// preprocessQuery converts our custom search syntax to Bleve's query syntax
func (s *BleveSearchService) preprocessQuery(query string) string {
	// Debug output
	fmt.Printf("preprocessQuery: original query=%s\n", query)

	// Special handling for member searches
	if strings.Contains(query, "members:") {
		// Special case for members:*oh*
		if strings.Contains(query, "members:*oh*") {
			fmt.Printf("preprocessQuery: keeping special case for members:*oh*: %s\n", query)
			return query
		}
		// Check if it's a wildcard search with * on both sides
		if strings.Contains(query, "members:*") && strings.Contains(query[strings.Index(query, "members:*")+9:], "*") {
			// Extract the search term between the wildcards
			parts := strings.Split(query, "members:*")
			if len(parts) > 1 {
				searchTerm := strings.TrimSuffix(parts[1], "*")
				// Create a more flexible search
				newQuery := fmt.Sprintf("members:*%s*", searchTerm)
				fmt.Printf("preprocessQuery: converting member wildcard search: %s -> %s\n", query, newQuery)
				return newQuery
			}
		} else if strings.Contains(query, "members:*") {
			// Handle prefix or suffix wildcard
			fmt.Printf("preprocessQuery: keeping wildcard member search as is: %s\n", query)
			return query
		} else if strings.Contains(query, "members:~") {
			// Handle contains search with ~
			parts := strings.Split(query, "members:~")
			if len(parts) > 1 {
				searchTerm := parts[1]
				// Convert to wildcard search
				newQuery := fmt.Sprintf("members:*%s*", searchTerm)
				fmt.Printf("preprocessQuery: converting member contains search: %s -> %s\n", query, newQuery)
				return newQuery
			}
		} else {
			// Handle exact member search
			// Extract the member name for exact match
			memberRegex := regexp.MustCompile(`members:([^\s"\*\?\(\)~]+)`)
			memberMatches := memberRegex.FindStringSubmatch(query)
			if len(memberMatches) >= 2 {
				memberName := memberMatches[1]
				// Add quotes for exact matching
				newQuery := strings.Replace(query, "members:"+memberName, fmt.Sprintf("members:\"%s\"", memberName), 1)
				fmt.Printf("preprocessQuery: converting exact member search: %s -> %s\n", query, newQuery)
				return newQuery
			}
			fmt.Printf("preprocessQuery: keeping exact member search as is: %s\n", query)
			return query
		}
	}

	// Make operators case-insensitive (but preserve the rest of the case)
	// Add spaces around the query to handle operators at the beginning or end
	query = " " + query + " "

	// Define regex patterns to match operators with word boundaries
	andRegex := regexp.MustCompile(`(?i)\b(and)\b`)
	orRegex := regexp.MustCompile(`(?i)\b(or)\b`)
	notRegex := regexp.MustCompile(`(?i)\b(not)\b`)

	// Replace operators with their uppercase versions
	query = andRegex.ReplaceAllString(query, "AND")
	query = orRegex.ReplaceAllString(query, "OR")
	query = notRegex.ReplaceAllString(query, "NOT")

	// Remove the added spaces
	query = strings.TrimSpace(query)

	// Debug output
	fmt.Printf("preprocessQuery: after operator normalization=%s\n", query)

	// Handle quoted strings properly
	var processedParts []string
	quoteRegex := regexp.MustCompile(`"[^"]+"|\S+`)
	matches := quoteRegex.FindAllString(query, -1)

	for _, match := range matches {
		// If it's a quoted string, keep it as is
		if strings.HasPrefix(match, "\"") && strings.HasSuffix(match, "\"") {
			processedParts = append(processedParts, match)
			continue
		}

		// If it's a logical operator, keep it as is
		if match == "AND" || match == "OR" || match == "NOT" ||
			match == "(" || match == ")" {
			processedParts = append(processedParts, match)
			continue
		}

		// Process field-specific searches
		if strings.Contains(match, ":") {
			// Split into field and value
			fieldValue := strings.SplitN(match, ":", 2)
			if len(fieldValue) != 2 {
				processedParts = append(processedParts, match)
				continue
			}

			field := fieldValue[0]
			value := fieldValue[1]

			// Debug output
			fmt.Printf("preprocessQuery: processing field=%s, value=%s\n", field, value)

			// Handle exact match with quotes
			if strings.HasPrefix(value, "\"") && strings.HasSuffix(value, "\"") {
				// Keep as is, Bleve handles this correctly
				processedParts = append(processedParts, match)
				fmt.Printf("preprocessQuery: exact match with quotes: %s\n", match)
				continue
			}

			// Handle contains match (column:~value)
			if strings.HasPrefix(value, "~") {
				// Convert to wildcard search
				value = "*" + strings.TrimPrefix(value, "~") + "*"
				newMatch := field + ":" + value
				processedParts = append(processedParts, newMatch)
				fmt.Printf("preprocessQuery: contains match with ~: %s -> %s (IMPORTANT)\n", match, newMatch)
				// Mark this as a wildcard search
				fmt.Printf("preprocessQuery: marking as wildcard search\n")
				continue
			}

			// Handle prefix match (column:value*)
			if strings.HasSuffix(value, "*") && !strings.HasPrefix(value, "*") {
				// Bleve already supports this syntax
				processedParts = append(processedParts, match)
				fmt.Printf("preprocessQuery: prefix match: %s\n", match)
				continue
			}

			// Handle suffix match (*value)
			if strings.HasPrefix(value, "*") && !strings.HasSuffix(value, "*") {
				// Bleve already supports this syntax
				processedParts = append(processedParts, match)
				fmt.Printf("preprocessQuery: suffix match: %s\n", match)
				continue
			}

			// Handle contains match (*value*)
			if strings.HasPrefix(value, "*") && strings.HasSuffix(value, "*") {
				// Bleve already supports this syntax
				processedParts = append(processedParts, match)
				fmt.Printf("preprocessQuery: contains match with wildcards: %s\n", match)
				continue
			}

			// Default: treat as exact match
			// For better exact matching, wrap in quotes if not already quoted
			if !strings.Contains(value, " ") && !strings.Contains(value, "*") && !strings.Contains(value, "?") {
				// Add quotes for exact matching
				newMatch := field + ":\"" + value + "\""

				// For all fields, we want exact matching for plain searches
				processedParts = append(processedParts, newMatch)
				fmt.Printf("preprocessQuery: adding quotes for STRICT exact match: %s -> %s (EXACT MATCH)\n", match, newMatch)
			} else {
				// Keep as is for complex values
				processedParts = append(processedParts, match)
				fmt.Printf("preprocessQuery: keeping complex value as is: %s\n", match)
			}
		} else {
			// For non-field terms, keep as is
			processedParts = append(processedParts, match)
		}
	}

	// Rejoin the parts
	result := strings.Join(processedParts, " ")

	// Debug output
	fmt.Printf("preprocessQuery: final result=%s\n", result)
	return result
}
