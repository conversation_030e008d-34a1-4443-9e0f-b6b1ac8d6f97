import { useState, useEffect, useRef } from 'react'
import { Calendar, HelpCircle, RefreshCw } from 'lucide-react'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ScheduleConfig } from '@/api/client'
import { Separator } from '@/components/ui/separator'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { calculateEstimatedNextRun, formatScheduleDate } from '@/utils/scheduleUtils'
import { Button } from '@/components/ui/button'
import { CountdownTimer } from '@/components/ui/countdown-timer'

interface ScheduleConfigProps {
  schedule: ScheduleConfig | undefined
  onChange: (schedule: ScheduleConfig) => void
  readOnly?: boolean
}

export default function ScheduleConfigComponent({ schedule, onChange, readOnly = false }: ScheduleConfigProps) {
  // Default schedule configuration
  const defaultSchedule = {
    enabled: false,
    frequency: 'daily' as 'interval' | 'daily' | 'weekly' | 'monthly',
    hour: 0,
    minute: 0,
    dayOfWeek: 1, // Monday
    dayOfMonth: 1,
    intervalHours: 1, // Default interval is 1 hour
    intervalMinutes: 0, // Default interval is 0 minutes
  } as const

  // Use a ref to track if we're updating from internal state changes
  const isInternalUpdate = useRef(false);
  const isInitialMount = useRef(true);

  // Initialize state with provided schedule or default
  const [enabled, setEnabled] = useState<boolean>(schedule?.enabled ?? defaultSchedule.enabled)
  const [frequency, setFrequency] = useState<'interval' | 'daily' | 'weekly' | 'monthly'>(
    (schedule?.frequency as any) ?? defaultSchedule.frequency
  )
  const [dayOfWeek, setDayOfWeek] = useState<number>(
    schedule?.dayOfWeek !== undefined ? schedule.dayOfWeek : defaultSchedule.dayOfWeek
  )
  const [dayOfMonth, setDayOfMonth] = useState<number>(
    schedule?.dayOfMonth !== undefined ? schedule.dayOfMonth : defaultSchedule.dayOfMonth
  )
  const [hour, setHour] = useState<number>(schedule?.hour ?? defaultSchedule.hour)
  const [minute, setMinute] = useState<number>(schedule?.minute ?? defaultSchedule.minute)
  const [intervalHours, setIntervalHours] = useState<number>(
    schedule?.intervalHours !== undefined ? schedule.intervalHours : defaultSchedule.intervalHours
  )
  const [intervalMinutes, setIntervalMinutes] = useState<number>(
    schedule?.intervalMinutes !== undefined ? schedule.intervalMinutes : defaultSchedule.intervalMinutes
  )
  const [nextRun, setNextRun] = useState<string | undefined>(schedule?.nextRun)
  const [estimatedNextRun, setEstimatedNextRun] = useState<Date | null>(null)

  // Update state when schedule prop changes
  useEffect(() => {
    if (schedule && !isInternalUpdate.current) {
      setEnabled(schedule.enabled ?? defaultSchedule.enabled);
      setFrequency((schedule.frequency as any) ?? defaultSchedule.frequency);

      // Handle dayOfWeek with proper type checking
      if (typeof schedule.dayOfWeek === 'number') {
        setDayOfWeek(schedule.dayOfWeek);
      } else {
        setDayOfWeek(defaultSchedule.dayOfWeek);
      }

      // Handle dayOfMonth with proper type checking
      if (typeof schedule.dayOfMonth === 'number') {
        setDayOfMonth(schedule.dayOfMonth);
      } else {
        setDayOfMonth(defaultSchedule.dayOfMonth);
      }

      setHour(schedule.hour ?? defaultSchedule.hour);
      setMinute(schedule.minute ?? defaultSchedule.minute);

      // Handle intervalHours with proper type checking
      if (typeof (schedule as any).intervalHours === 'number') {
        setIntervalHours((schedule as any).intervalHours);
      } else {
        setIntervalHours(defaultSchedule.intervalHours);
      }

      // Handle intervalMinutes with proper type checking
      if (typeof (schedule as any).intervalMinutes === 'number') {
        setIntervalMinutes((schedule as any).intervalMinutes);
      } else {
        setIntervalMinutes(defaultSchedule.intervalMinutes);
      }

      setNextRun(schedule.nextRun);

      // Calculate initial estimated next run time
      setTimeout(() => {
        // If there's a saved next run time, use it for the initial estimate
        if (schedule.nextRun) {
          setEstimatedNextRun(new Date(schedule.nextRun));
        } else {
          updateEstimatedNextRun();
        }
      }, 0);
    }
    // Reset the flag after the effect runs
    isInternalUpdate.current = false;
  }, [schedule, defaultSchedule])

  // Update parent component when any schedule field changes
  useEffect(() => {
    // Skip the first render to avoid unnecessary updates
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Always update the parent component with the current state
    onChange({
      enabled,
      frequency: frequency as any, // Cast to any to avoid type errors
      dayOfWeek,
      dayOfMonth,
      hour,
      minute,
      nextRun,
      intervalHours,
      intervalMinutes,
    } as any);

  }, [enabled, frequency, dayOfWeek, dayOfMonth, hour, minute, intervalHours, intervalMinutes, nextRun, onChange])

  // Update estimated next run time whenever schedule settings change
  useEffect(() => {
    if (!isInitialMount.current) {
      updateEstimatedNextRun()
    }
  }, [enabled, frequency, dayOfWeek, dayOfMonth, hour, minute, intervalHours, intervalMinutes])

  // Set up a timer to periodically update the estimated next run time
  useEffect(() => {
    if (enabled) {
      // Update immediately
      updateEstimatedNextRun()

      // Set up interval to update every second
      const interval = setInterval(() => {
        updateEstimatedNextRun()
      }, 1000)

      // Clean up interval on unmount or when schedule is disabled
      return () => clearInterval(interval)
    }
  }, [enabled])

  // Generate options for hours (0-23)
  const hourOptions = Array.from({ length: 24 }, (_, i) => ({
    value: i,
    label: i.toString().padStart(2, '0'),
  }))

  // Generate options for minutes (0-59)
  const minuteOptions = Array.from({ length: 60 }, (_, i) => ({
    value: i,
    label: i.toString().padStart(2, '0'),
  }))

  // Generate options for days of week
  const dayOfWeekOptions = [
    { value: 0, label: 'Sunday' },
    { value: 1, label: 'Monday' },
    { value: 2, label: 'Tuesday' },
    { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' },
    { value: 5, label: 'Friday' },
    { value: 6, label: 'Saturday' },
  ]

  // Generate options for days of month (1-31)
  const dayOfMonthOptions = Array.from({ length: 31 }, (_, i) => ({
    value: i + 1,
    label: (i + 1).toString(),
  }))

  // Format next run date for display
  // This function is not currently used but may be needed in the future
  // const formatNextRun = (dateString?: string) => {
  //   if (!dateString) return 'Not scheduled'
  //   const date = new Date(dateString)
  //   return date.toLocaleString()
  // }

  // Update the estimated next run time based on current schedule settings
  const updateEstimatedNextRun = () => {
    if (!enabled) {
      setEstimatedNextRun(null)
      return
    }

    // Ensure we don't have a zero interval
    let validIntervalHours = intervalHours;
    let validIntervalMinutes = intervalMinutes;

    if (frequency === 'interval' && intervalHours === 0 && intervalMinutes === 0) {
      validIntervalMinutes = 30; // Default to 30 minutes if both are 0
    }

    const currentSchedule: ScheduleConfig = {
      enabled,
      frequency,
      dayOfWeek,
      dayOfMonth,
      hour,
      minute,
      intervalHours: validIntervalHours,
      intervalMinutes: validIntervalMinutes
    }

    // Calculate the next run time
    const nextRunDate = calculateEstimatedNextRun(currentSchedule)

    // Only update state if the date has changed (to avoid unnecessary re-renders)
    if (!estimatedNextRun ||
        nextRunDate.getTime() !== estimatedNextRun.getTime()) {
      setEstimatedNextRun(nextRunDate)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="schedule-enabled" className="text-base">Automatic Scheduling</Label>
          <p className="text-sm text-muted-foreground">
            Enable automatic generation of this report on a schedule
          </p>
        </div>
        <Switch
          id="schedule-enabled"
          checked={enabled}
          onCheckedChange={(value) => {
            isInternalUpdate.current = true;
            setEnabled(value);
          }}
          disabled={readOnly}
        />
      </div>

      {enabled && (
        <>
          <Separator className="my-4" />

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-1">
                <Label htmlFor="frequency">Frequency</Label>
                <Popover>
                  <PopoverTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 cursor-pointer" />
                  </PopoverTrigger>
                  <PopoverContent side="top" className="w-80">
                    <div className="p-2">
                      <h4 className="font-medium text-sm mb-1">Schedule Frequency</h4>
                      <ul className="text-xs space-y-1">
                        <li><strong>Interval:</strong> Run at regular intervals (every X hours and Y minutes)</li>
                        <li><strong>Daily:</strong> Run once a day at the specified time</li>
                        <li><strong>Weekly:</strong> Run once a week on the specified day and time</li>
                        <li><strong>Monthly:</strong> Run once a month on the specified day and time</li>
                      </ul>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              <Select
                value={frequency}
                onValueChange={(value: any) => {
                  isInternalUpdate.current = true;
                  setFrequency(value);
                }}
                disabled={readOnly}
              >
                <SelectTrigger id="frequency">
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="interval">Interval</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {frequency === 'interval' && (
              <div className="space-y-4">
                <div className="flex items-center gap-1 mb-2">
                  <Label>Interval Duration</Label>
                  <Popover>
                    <PopoverTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400 cursor-pointer" />
                    </PopoverTrigger>
                    <PopoverContent side="top" className="w-80">
                      <div className="p-2">
                        <h4 className="font-medium text-sm mb-1">Interval Duration</h4>
                        <p className="text-xs">Set how frequently the report should be generated. Combine hours and minutes to create the total interval.</p>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Display hours and minutes in the same line */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="interval-hours" className="text-sm mb-2 block">Hours</Label>
                    <Select
                      value={intervalHours.toString()}
                      onValueChange={(value) => {
                        isInternalUpdate.current = true;
                        const newHours = parseInt(value);
                        setIntervalHours(newHours);

                        // If hours is set to 0, ensure minutes is not also 0
                        if (newHours === 0 && intervalMinutes === 0) {
                          setIntervalMinutes(30); // Default to 30 minutes if hours is 0
                        }
                      }}
                      disabled={readOnly}
                    >
                      <SelectTrigger id="interval-hours">
                        <SelectValue placeholder="Select hours" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 24 }, (_, i) => (
                          <SelectItem key={i} value={i.toString()}>{i} hour{i !== 1 ? 's' : ''}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="interval-minutes" className="text-sm mb-2 block">Minutes</Label>
                    <Select
                      value={intervalMinutes.toString()}
                      onValueChange={(value) => {
                        isInternalUpdate.current = true;
                        const newMinutes = parseInt(value);

                        // If hours is 0, don't allow minutes to be 0
                        if (intervalHours === 0 && newMinutes === 0) {
                          // Show a warning or set a default value
                          setIntervalMinutes(30); // Default to 30 minutes
                        } else {
                          setIntervalMinutes(newMinutes);
                        }
                      }}
                      disabled={readOnly}
                    >
                      <SelectTrigger id="interval-minutes">
                        <SelectValue placeholder="Select minutes" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* Add more granular minute options */}
                        <SelectItem value="0" disabled={intervalHours === 0}>0 minutes{intervalHours === 0 && " (not allowed when hours is 0)"}</SelectItem>
                        <SelectItem value="2">2 minutes</SelectItem>
                        <SelectItem value="5">5 minutes</SelectItem>
                        <SelectItem value="10">10 minutes</SelectItem>
                        <SelectItem value="15">15 minutes</SelectItem>
                        <SelectItem value="20">20 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="45">45 minutes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {frequency === 'weekly' && (
              <div className="space-y-2">
                <div className="flex items-center gap-1">
                  <Label htmlFor="day-of-week">Day of Week</Label>
                  <Popover>
                    <PopoverTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400 cursor-pointer" />
                    </PopoverTrigger>
                    <PopoverContent side="top" className="w-80">
                      <div className="p-2">
                        <h4 className="font-medium text-sm mb-1">Day of Week</h4>
                        <p className="text-xs">Select which day of the week the report should be generated.</p>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
                <Select
                  value={dayOfWeek.toString()}
                  onValueChange={(value) => {
                    isInternalUpdate.current = true;
                    setDayOfWeek(parseInt(value));
                  }}
                  disabled={readOnly}
                >
                  <SelectTrigger id="day-of-week">
                    <SelectValue placeholder="Select day of week" />
                  </SelectTrigger>
                  <SelectContent>
                    {dayOfWeekOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {frequency === 'monthly' && (
              <div className="space-y-2">
                <div className="flex items-center gap-1">
                  <Label htmlFor="day-of-month">Day of Month</Label>
                  <Popover>
                    <PopoverTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400 cursor-pointer" />
                    </PopoverTrigger>
                    <PopoverContent side="top" className="w-80">
                      <div className="p-2">
                        <h4 className="font-medium text-sm mb-1">Day of Month</h4>
                        <p className="text-xs">Select which day of the month the report should be generated. If the selected day doesn't exist in a particular month (e.g., 31st in February), the report will run on the last day of that month.</p>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
                <Select
                  value={dayOfMonth.toString()}
                  onValueChange={(value) => {
                    isInternalUpdate.current = true;
                    setDayOfMonth(parseInt(value));
                  }}
                  disabled={readOnly}
                >
                  <SelectTrigger id="day-of-month">
                    <SelectValue placeholder="Select day of month" />
                  </SelectTrigger>
                  <SelectContent>
                    {dayOfMonthOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Only show hour and minute fields for non-interval schedules */}
            {frequency !== 'interval' && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-1">
                    <Label htmlFor="hour">Hour</Label>
                    <Popover>
                      <PopoverTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400 cursor-pointer" />
                      </PopoverTrigger>
                      <PopoverContent side="top" className="w-80">
                        <div className="p-2">
                          <h4 className="font-medium text-sm mb-1">Hour</h4>
                          <p className="text-xs">Select the hour when the report should be generated (0-23, where 0 is midnight and 23 is 11 PM).</p>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <Select
                    value={hour.toString()}
                    onValueChange={(value) => {
                      isInternalUpdate.current = true;
                      setHour(parseInt(value));
                    }}
                    disabled={readOnly}
                  >
                    <SelectTrigger id="hour">
                      <SelectValue placeholder="Select hour" />
                    </SelectTrigger>
                    <SelectContent>
                      {hourOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-1">
                    <Label htmlFor="minute">Minute</Label>
                    <Popover>
                      <PopoverTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400 cursor-pointer" />
                      </PopoverTrigger>
                      <PopoverContent side="top" className="w-80">
                        <div className="p-2">
                          <h4 className="font-medium text-sm mb-1">Minute</h4>
                          <p className="text-xs">Select the minute when the report should be generated (0-59).</p>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <Select
                    value={minute.toString()}
                    onValueChange={(value) => {
                      isInternalUpdate.current = true;
                      setMinute(parseInt(value));
                    }}
                    disabled={readOnly}
                  >
                    <SelectTrigger id="minute">
                      <SelectValue placeholder="Select minute" />
                    </SelectTrigger>
                    <SelectContent>
                      {minuteOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Display estimated next run time based on current settings */}
            {enabled && estimatedNextRun && (
              <div className="flex flex-col text-sm mt-2 p-2 bg-blue-50 rounded border border-blue-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-blue-500" />
                    <span>
                      <span className="font-medium">Next run:</span> {formatScheduleDate(estimatedNextRun)}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={updateEstimatedNextRun}
                    title="Recalculate next run time"
                  >
                    <RefreshCw className="h-3 w-3" />
                  </Button>
                </div>

                {/* Add countdown timer */}
                <div className="mt-1 ml-5 text-xs text-green-600 font-medium">
                  <CountdownTimer
                    targetDate={estimatedNextRun}
                    refreshInterval={1000} // Update every second
                  />
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}
