package search

import (
	"fmt"
	"strings"

	"github.com/bzick/tokenizer"
)

// Parse<PERSON> is responsible for parsing search queries
type Parser struct {
	tokenizer *tokenizer.Tokenizer
}

// NewParser creates a new search parser
func NewParser() *Parser {
	parser := &Parser{
		tokenizer: tokenizer.New(),
	}

	// Configure tokenizer
	parser.tokenizer.
		DefineTokens(TokenAnd, []string{"AND", "and"}).
		DefineTokens(TokenOr, []string{"OR", "or"}).
		DefineTokens(TokenNot, []string{"NOT", "not"}).
		DefineTokens(TokenOpenParen, []string{"("}).
		DefineTokens(TokenCloseParen, []string{")"}).
		DefineTokens(TokenField, []string{":"}).
		DefineTokens(TokenContainsMatch, []string{"~"}).
		DefineStringToken(TokenQuotedString, `"`, `"`).SetEscapeSymbol('\\')

	// Define quoted string token
	parser.tokenizer.DefineStringToken(TokenQuotedString, `"`, `"`).SetEscapeSymbol('\\')

	// Allow wildcards and special characters in keywords
	parser.tokenizer.AllowKeywordSymbols([]rune{'*', '_', '-', '.', '@'}, tokenizer.Numbers)

	return parser
}

// ParseQuery parses a search query string into a structured query
func (p *Parser) ParseQuery(query string) (*SearchNode, error) {
	// Check for invalid syntax with tilde (~) in wrong position
	if strings.Contains(query, "~") && !strings.Contains(query, ":~") {
		return nil, fmt.Errorf("invalid syntax: tilde (~) must be used after a field name and colon, e.g., 'name:~value'")
	}

	// Check for unbalanced parentheses
	openCount := strings.Count(query, "(")
	closeCount := strings.Count(query, ")")
	if openCount != closeCount {
		if openCount > closeCount {
			return nil, fmt.Errorf("invalid syntax: missing closing parenthesis - found %d opening '(' but only %d closing ')'", openCount, closeCount)
		} else {
			return nil, fmt.Errorf("invalid syntax: unexpected closing parenthesis - found %d closing ')' but only %d opening '('", closeCount, openCount)
		}
	}

	if strings.TrimSpace(query) == "" {
		// Return a special node that matches all records
		return NewTermNode("_all", "=", "true", false), nil
	}

	// Handle quoted strings directly if the entire query is a quoted string
	if strings.HasPrefix(query, "\"") && strings.HasSuffix(query, "\"") && len(query) > 2 {
		value := query[1 : len(query)-1] // Remove quotes
		return NewTermNode("_any", "=", value, true), nil
	}

	// Handle NOT at the beginning of the query
	if strings.HasPrefix(strings.ToLower(query), "not ") {
		// Create a special case for NOT at the beginning
		// Parse the rest of the query (after NOT)
		restQuery := query[4:] // Skip "NOT "
		stream := p.tokenizer.ParseString(restQuery)
		defer stream.Close()

		// Parse the expression after NOT
		right, err := p.parseExpression(stream)
		if err != nil {
			return nil, err
		}

		// Create a NOT node with the right node
		return NewNotNode(right), nil
	}

	// Check if this is a simple query with a wildcard (no logical operators)
	if strings.Contains(query, "*") &&
		!strings.Contains(strings.ToLower(query), " and ") &&
		!strings.Contains(strings.ToLower(query), " or ") &&
		!strings.Contains(strings.ToLower(query), " not ") {
		// Handle field:*value* pattern (contains)
		if strings.Contains(query, ":") && strings.Count(query, ":") == 1 {
			parts := strings.SplitN(query, ":", 2)
			field := strings.ToLower(parts[0])
			value := parts[1]

			// Check for different wildcard patterns
			if strings.HasPrefix(value, "*") && strings.HasSuffix(value, "*") && len(value) > 2 {
				// *value* pattern (contains)
				value = value[1 : len(value)-1]
				return NewTermNode(field, "~", value, false), nil
			} else if strings.HasPrefix(value, "*") && len(value) > 1 {
				// *value pattern (suffix)
				value = value[1:]
				return NewTermNode(field, "$", value, false), nil
			} else if strings.HasSuffix(value, "*") && len(value) > 1 {
				// value* pattern (prefix)
				value = value[:len(value)-1]
				return NewTermNode(field, "^", value, false), nil
			}
		}
	}

	// Handle all queries generically

	// Parse the query into tokens
	stream := p.tokenizer.ParseString(query)
	defer stream.Close()

	// Build the AST
	node, err := p.parseExpression(stream)
	if err != nil {
		return nil, err
	}

	return node, nil
}

// parseExpression parses an expression (term OR term OR term...)
func (p *Parser) parseExpression(stream *tokenizer.Stream) (*SearchNode, error) {
	// Parse the first term
	left, err := p.parseTerm(stream)
	if err != nil {
		return nil, err
	}

	// If there are no more tokens, return the term
	if !stream.IsValid() {
		return left, nil
	}

	// Check for operators
	for stream.IsValid() {
		token := stream.CurrentToken()

		if token.Key() == TokenAnd {
			// AND operator
			stream.GoNext()
			right, err := p.parseTerm(stream)
			if err != nil {
				return nil, err
			}
			left = NewAndNode(left, right)
		} else if token.Key() == TokenOr {
			// OR operator
			stream.GoNext()
			right, err := p.parseTerm(stream)
			if err != nil {
				return nil, err
			}
			left = NewOrNode(left, right)
		} else if token.Key() == TokenNot {
			// NOT operator
			stream.GoNext()
			right, err := p.parseTerm(stream)
			if err != nil {
				return nil, err
			}
			// Create a NOT node with the right node
			notNode := NewNotNode(right)
			// If left is nil, just return the NOT node
			if left == nil {
				left = notNode
			} else {
				left = NewAndNode(left, notNode)
			}
		} else if token.Key() == TokenCloseParen {
			// End of expression
			break
		} else {
			// If there's no operator, assume AND (implicit)
			if token.Key() == tokenizer.TokenKeyword {
				right, err := p.parseTerm(stream)
				if err != nil {
					return nil, err
				}
				left = NewAndNode(left, right)
			} else {
				// End of expression
				break
			}
		}
	}

	return left, nil
}

// parseTerm parses a term (field:value, "quoted string", etc.)
func (p *Parser) parseTerm(stream *tokenizer.Stream) (*SearchNode, error) {
	if !stream.IsValid() {
		return nil, fmt.Errorf("unexpected end of query")
	}

	originalValue := stream.CurrentToken().ValueString()

	// Special handling for wildcards in the original query
	if strings.Contains(originalValue, "*") {
		// Handle field:*value* pattern (contains)
		if strings.Contains(originalValue, ":") && strings.Count(originalValue, ":") == 1 {
			parts := strings.SplitN(originalValue, ":", 2)
			field := strings.ToLower(parts[0])
			value := parts[1]

			// Check for different wildcard patterns
			if strings.HasPrefix(value, "*") && strings.HasSuffix(value, "*") && len(value) > 2 {
				// *value* pattern (contains)
				value = value[1 : len(value)-1]
				stream.GoNext()
				return NewTermNode(field, "~", value, false), nil
			} else if strings.HasPrefix(value, "*") && len(value) > 1 {
				// *value pattern (suffix)
				value = value[1:]
				stream.GoNext()
				return NewTermNode(field, "$", value, false), nil
			} else if strings.HasSuffix(value, "*") && len(value) > 1 {
				// value* pattern (prefix)
				value = value[:len(value)-1]
				stream.GoNext()
				return NewTermNode(field, "^", value, false), nil
			}
		}
	}

	token := stream.CurrentToken()

	// Check for NOT operator
	if token.Key() == TokenNot {
		stream.GoNext()
		if !stream.IsValid() {
			return nil, fmt.Errorf("expected expression after NOT")
		}
		right, err := p.parseTerm(stream)
		if err != nil {
			return nil, err
		}
		return NewNotNode(right), nil
	}

	// Check for open parenthesis
	if token.Key() == TokenOpenParen {
		stream.GoNext()
		if !stream.IsValid() {
			return nil, fmt.Errorf("expected expression after (")
		}
		node, err := p.parseExpression(stream)
		if err != nil {
			return nil, err
		}
		if !stream.IsValid() || stream.CurrentToken().Key() != TokenCloseParen {
			return nil, fmt.Errorf("expected closing parenthesis")
		}
		stream.GoNext()
		return node, nil
	}

	// Check for quoted string
	if token.Key() == TokenQuotedString {
		value := string(token.ValueUnescaped())
		stream.GoNext()
		return NewTermNode("_any", "=", value, true), nil
	}

	// Check for field:value syntax
	if token.Key() == tokenizer.TokenKeyword {
		field := strings.ToLower(token.ValueString())
		stream.GoNext()

		// Check if next token is a field separator
		if stream.IsValid() && stream.CurrentToken().Key() == TokenField {
			stream.GoNext()

			// Check for contains match (~)
			operator := "="
			if stream.IsValid() && stream.CurrentToken().Key() == TokenContainsMatch {
				operator = "~"
				stream.GoNext()
			}

			// Get the value
			if !stream.IsValid() {
				return nil, fmt.Errorf("expected value after field specifier")
			}

			value := stream.CurrentToken().ValueString()
			isQuoted := false

			// Check for quoted value
			if stream.CurrentToken().Key() == TokenQuotedString {
				value = string(stream.CurrentToken().ValueUnescaped())
				isQuoted = true
			}

			// Check for wildcards
			if !isQuoted {
				// Check for *value* pattern (contains match)
				if strings.HasPrefix(value, "*") && strings.HasSuffix(value, "*") && len(value) > 2 {
					value = value[1 : len(value)-1] // Remove * from both ends
					operator = "~"
				} else if strings.HasPrefix(value, "*") && len(value) > 1 {
					// *value pattern (suffix match)
					value = value[1:] // Remove leading *
					operator = "$"    // Suffix match
				} else if strings.HasSuffix(value, "*") && len(value) > 1 {
					// value* pattern (prefix match)
					value = value[:len(value)-1] // Remove trailing *
					operator = "^"
				}
			}

			// Trim quotes if present
			if strings.HasPrefix(value, "\"") && strings.HasSuffix(value, "\"") && len(value) > 2 {
				value = value[1 : len(value)-1]
				isQuoted = true
			}

			stream.GoNext()
			return NewTermNode(field, operator, value, isQuoted), nil
		}

		// Simple keyword - use contains match by default for better user experience
		return NewTermNode("_any", "~", field, false), nil
	}

	// Unknown token
	return nil, fmt.Errorf("unexpected token: %s", token.ValueString())
}

// ParseQueryToMap parses a search query into a map format for backward compatibility
func (p *Parser) ParseQueryToMap(query string) (map[string][]string, error) {
	result := make(map[string][]string)
	result["_any"] = []string{} // For non-column-specific terms

	if query == "" {
		return result, nil
	}

	// Parse the query into an AST
	node, err := p.ParseQuery(query)
	if err != nil {
		return nil, err
	}

	// Convert the AST to a map
	p.nodeToMap(node, result)

	return result, nil
}

// nodeToMap converts a search node to a map
func (p *Parser) nodeToMap(node *SearchNode, result map[string][]string) {
	if node == nil {
		return
	}

	switch node.Type {
	case NodeTerm:
		// Add the term to the map
		if node.Field == "_any" {
			if node.IsQuoted {
				// Exact match
				if result["_exact"] == nil {
					result["_exact"] = []string{}
				}
				result["_exact"] = append(result["_exact"], strings.ToLower(node.Value))
			} else {
				// General search
				result["_any"] = append(result["_any"], strings.ToLower(node.Value))
			}
		} else {
			// Field-specific search
			if result[node.Field] == nil {
				result[node.Field] = []string{}
			}

			switch node.Operator {
			case "=":
				result[node.Field] = append(result[node.Field], "="+strings.ToLower(node.Value))
			case "~":
				result[node.Field] = append(result[node.Field], "~"+strings.ToLower(node.Value))
			case "^":
				result[node.Field] = append(result[node.Field], "^"+strings.ToLower(node.Value))
			case "$":
				result[node.Field] = append(result[node.Field], "$"+strings.ToLower(node.Value))
			}
		}
	case NodeAnd:
		// Process both sides of the AND
		p.nodeToMap(node.Left, result)
		p.nodeToMap(node.Right, result)
	case NodeOr:
		// For OR, we need to create a special entry
		// This is a limitation of the map format, but we'll handle it in the search engine
		p.nodeToMap(node.Left, result)
		p.nodeToMap(node.Right, result)
	case NodeNot:
		// For NOT, we need to create a special entry
		// This is a limitation of the map format, but we'll handle it in the search engine
		p.nodeToMap(node.Right, result)
	}
}
