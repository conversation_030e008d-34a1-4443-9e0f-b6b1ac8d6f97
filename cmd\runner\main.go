package main

import (
	"bufio"
	"flag"
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"syscall"
	"time"
)

// Version information - will be set during build
var (
	version = "dev"
	commit  = "none"
	date    = "unknown"
)

// Default values
const (
	defaultBackendPort    = "8080"
	defaultFrontendPort   = "5173"
	defaultDockerRegistry = "localhost"
)

// Command line flags
type devFlags struct {
	help bool
}

type buildFlags struct {
	help       bool
	snapshot   bool
	noSnapshot bool
	skipDocker bool
	registry   string
	clean      bool
	noClean    bool
}

type startFlags struct {
	help        bool
	port        string
	repoDir     string
	exportDir   string
	pollSeconds int
}

func main() {
	// Get the project root directory
	projectRoot, err := os.Getwd()
	if err != nil {
		log.Fatalf("Error getting current directory: %v", err)
	}

	// Check if a command was provided
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	// Process commands
	command := os.Args[1]
	switch command {
	case "dev":
		runDev(projectRoot, os.Args[2:])
	case "build":
		runBuild(projectRoot, os.Args[2:])
	case "start":
		runStart(projectRoot, os.Args[2:])
	case "--help", "-h":
		printUsage()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Println("Usage: runner [dev|build|start] [options]")
	fmt.Println()
	fmt.Println("Commands:")
	fmt.Println("  dev     Start the application in development mode")
	fmt.Println("  build   Build the application with the UI embedded in the binary and create Docker image")
	fmt.Println("  start   Start the built application")
	fmt.Println()
	fmt.Println("For more information on options, run: runner [command] --help")
}

func runDev(projectRoot string, args []string) {
	// Parse dev command flags
	devFlagSet := flag.NewFlagSet("dev", flag.ExitOnError)
	devOpts := devFlags{}
	devFlagSet.BoolVar(&devOpts.help, "help", false, "Show help for dev command")

	if err := devFlagSet.Parse(args); err != nil {
		log.Fatalf("Error parsing dev command flags: %v", err)
	}

	// Show help if requested
	if devOpts.help {
		fmt.Println("Usage: runner dev")
		fmt.Println()
		fmt.Println("Start the application in development mode.")
		fmt.Println("This will start both the backend server and the frontend development server.")
		return
	}

	fmt.Println("Starting ADGitOps Extract in development mode...")

	// Check if servers are already running
	backendRunning := isPortInUse(defaultBackendPort)
	frontendRunning := isPortInUse(defaultFrontendPort)

	if backendRunning || frontendRunning {
		// At least one server is running
		serversStatus := ""
		if backendRunning && frontendRunning {
			serversStatus = "Both servers are already running."
		} else if backendRunning {
			serversStatus = "Backend server is already running."
		} else {
			serversStatus = "Frontend server is already running."
		}

		fmt.Println(serversStatus)
		fmt.Printf("- Backend: http://localhost:%s (Running: %t)\n", defaultBackendPort, backendRunning)
		fmt.Printf("- Frontend: http://localhost:%s (Running: %t)\n", defaultFrontendPort, frontendRunning)

		// Prompt for restart
		fmt.Print("\nDo you want to restart the servers? [y/N] ")

		// Create a channel for timeout
		timeout := make(chan bool, 1)
		response := make(chan string, 1)

		// Start a goroutine to read user input
		go func() {
			reader := bufio.NewReader(os.Stdin)
			input, _ := reader.ReadString('\n')
			input = strings.TrimSpace(strings.ToLower(input))
			response <- input
		}()

		// Start a goroutine for timeout
		go func() {
			time.Sleep(10 * time.Second)
			timeout <- true
		}()

		// Wait for either user input or timeout
		select {
		case input := <-response:
			if input == "y" || input == "yes" {
				fmt.Println("Restarting servers...")

				// Kill existing processes
				if backendRunning {
					killServerOnPort(defaultBackendPort)
					// Wait for the port to be released
					waitForPortRelease(defaultBackendPort)
					backendRunning = false
				}

				if frontendRunning {
					killServerOnPort(defaultFrontendPort)
					// Wait for the port to be released
					waitForPortRelease(defaultFrontendPort)
					frontendRunning = false
				}
			} else {
				fmt.Println("Keeping existing servers running.")
				return
			}
		case <-timeout:
			fmt.Println("\nTimeout reached. Keeping existing servers running.")
			return
		}
	}

	// Create a channel to handle signals
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	// Create a channel to track running processes
	done := make(chan bool, 1)

	// Start backend if not already running
	var backendCmd *exec.Cmd
	if !backendRunning {
		fmt.Println("Starting backend server...")
		backendCmd = startBackendDev(projectRoot)

		// Verify that the backend started successfully
		if !verifyServerStarted(defaultBackendPort) {
			fmt.Println("Error: Backend server failed to start. Check for errors above.")
			if backendCmd != nil && backendCmd.Process != nil {
				killProcessAndChildren(backendCmd.Process.Pid)
			}
			return
		}
		fmt.Println("Backend server started successfully.")
	}

	// Start frontend if not already running
	var frontendCmd *exec.Cmd
	if !frontendRunning {
		fmt.Println("Starting frontend server...")
		frontendCmd = startFrontendDev(projectRoot)

		// Verify that the frontend started successfully
		if !verifyServerStarted(defaultFrontendPort) {
			fmt.Println("Error: Frontend server failed to start. Check for errors above.")
			if frontendCmd != nil && frontendCmd.Process != nil {
				killProcessAndChildren(frontendCmd.Process.Pid)
			}
			if backendCmd != nil && backendCmd.Process != nil {
				fmt.Println("Stopping backend server due to frontend startup failure...")
				killProcessAndChildren(backendCmd.Process.Pid)
			}
			return
		}
		fmt.Println("Frontend server started successfully.")
	}

	// Handle cleanup on exit
	go func() {
		<-sigs
		fmt.Println("\nShutting down development servers...")

		if backendCmd != nil && backendCmd.Process != nil {
			fmt.Println("Stopping backend server...")
			killProcessAndChildren(backendCmd.Process.Pid)
		}

		if frontendCmd != nil && frontendCmd.Process != nil {
			fmt.Println("Stopping frontend server...")
			killProcessAndChildren(frontendCmd.Process.Pid)
		}

		done <- true
	}()

	fmt.Println("\nDevelopment environment status:")
	fmt.Printf("- Backend: http://localhost:%s\n", defaultBackendPort)
	fmt.Printf("- Frontend: http://localhost:%s\n", defaultFrontendPort)
	fmt.Println("\nPress Ctrl+C to stop the servers.")

	// Wait for signal
	<-done
	fmt.Println("Development servers stopped.")
}

func runBuild(projectRoot string, args []string) {
	// Parse build command flags
	buildFlagSet := flag.NewFlagSet("build", flag.ExitOnError)
	buildOpts := buildFlags{
		snapshot: true, // Default to snapshot build
		clean:    true, // Default to clean build
	}

	buildFlagSet.BoolVar(&buildOpts.help, "help", false, "Show help for build command")
	buildFlagSet.BoolVar(&buildOpts.snapshot, "snapshot", true, "Create a snapshot build")
	buildFlagSet.BoolVar(&buildOpts.noSnapshot, "no-snapshot", false, "Create a release build (requires a Git tag)")
	buildFlagSet.BoolVar(&buildOpts.skipDocker, "skip-docker", false, "Skip Docker image creation")
	buildFlagSet.StringVar(&buildOpts.registry, "registry", defaultDockerRegistry, "Specify the Docker registry")
	buildFlagSet.BoolVar(&buildOpts.clean, "clean", true, "Clean before building")
	buildFlagSet.BoolVar(&buildOpts.noClean, "no-clean", false, "Skip cleaning before building")

	// These flags are accepted but not used (for compatibility)
	buildFlagSet.Bool("skip-validate", false, "Skip validation steps (accepted but not used)")
	buildFlagSet.Bool("skip-publish", false, "Skip publishing artifacts (accepted but not used)")

	if err := buildFlagSet.Parse(args); err != nil {
		log.Fatalf("Error parsing build command flags: %v", err)
	}

	// Show help if requested
	if buildOpts.help {
		fmt.Println("Usage: runner build [options]")
		fmt.Println()
		fmt.Println("Build the application with GoReleaser and create Docker image.")
		fmt.Println()
		fmt.Println("Options:")
		fmt.Println("  --snapshot       Create a snapshot build (default)")
		fmt.Println("  --no-snapshot    Create a release build (requires a Git tag)")
		fmt.Println("  --skip-validate  Skip validation steps (accepted but not used)")
		fmt.Println("  --skip-publish   Skip publishing artifacts (accepted but not used)")
		fmt.Println("  --skip-docker    Skip Docker image creation")
		fmt.Println("  --registry REG   Specify the Docker registry (default: localhost)")
		fmt.Println("  --clean          Clean before building (default)")
		fmt.Println("  --no-clean       Skip cleaning before building")
		return
	}

	// Override snapshot flag if no-snapshot is set
	if buildOpts.noSnapshot {
		buildOpts.snapshot = false
	}

	// Override clean flag if no-clean is set
	if buildOpts.noClean {
		buildOpts.clean = false
	}

	// Set Docker registry environment variable
	registry := buildOpts.registry
	if registry == "" {
		registry = defaultDockerRegistry
	}
	os.Setenv("DOCKER_REGISTRY", registry)
	fmt.Printf("Using Docker registry: %s\n", registry)

	// Build frontend first
	fmt.Println("=== Building Frontend ===")
	if err := buildFrontend(projectRoot); err != nil {
		log.Fatalf("Frontend build failed: %v", err)
	}

	// Check if GoReleaser is installed
	if !isCommandAvailable("goreleaser") {
		log.Fatalf("Error: GoReleaser is not installed or not in the PATH\nPlease install GoReleaser: https://goreleaser.com/install/")
	}

	fmt.Println("=== Building with GoReleaser ===")

	// Construct GoReleaser command
	args = []string{"release"}
	if buildOpts.snapshot {
		args = append(args, "--snapshot")
	}
	if buildOpts.skipDocker {
		args = append(args, "--skip-docker")
	}
	if buildOpts.clean {
		args = append(args, "--clean")
	}

	fmt.Printf("Running: goreleaser %s\n", strings.Join(args, " "))

	// Execute GoReleaser command
	cmd := exec.Command("goreleaser", args...)
	cmd.Dir = projectRoot
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Env = append(os.Environ(), fmt.Sprintf("DOCKER_REGISTRY=%s", registry))

	if err := cmd.Run(); err != nil {
		log.Fatalf("GoReleaser build failed: %v", err)
	}

	fmt.Println("Build process completed successfully!")
	fmt.Println("Binaries location: dist/")
}

func runStart(projectRoot string, args []string) {
	// Parse start command flags
	startFlagSet := flag.NewFlagSet("start", flag.ExitOnError)
	startOpts := startFlags{
		port:        defaultBackendPort,
		exportDir:   "export",
		pollSeconds: 300,
	}

	startFlagSet.BoolVar(&startOpts.help, "help", false, "Show help for start command")
	startFlagSet.StringVar(&startOpts.port, "port", defaultBackendPort, "Specify the server port")
	startFlagSet.StringVar(&startOpts.repoDir, "repo-dir", "", "Path to local GitLab repository clone")
	startFlagSet.StringVar(&startOpts.exportDir, "export-dir", "export", "Directory for exported data")
	startFlagSet.IntVar(&startOpts.pollSeconds, "poll-seconds", 300, "Polling frequency in seconds")

	if err := startFlagSet.Parse(args); err != nil {
		log.Fatalf("Error parsing start command flags: %v", err)
	}

	// Show help if requested
	if startOpts.help {
		fmt.Println("Usage: runner start [options]")
		fmt.Println()
		fmt.Println("Start the built application.")
		fmt.Println()
		fmt.Println("Options:")
		fmt.Println("  --port PORT            Specify the server port (default: 8080)")
		fmt.Println("  --repo-dir DIR         Path to repositories directory (default: repos)")
		fmt.Println("  --export-dir DIR       Directory for data files (default: data)")
		fmt.Println("  --poll-seconds SEC     Polling frequency in seconds (default: 300, not directly used)")
		return
	}

	// Find the binary
	binaryPath := findBinary(projectRoot)
	if binaryPath == "" {
		log.Fatalf("Error: Application not built yet. Run 'runner build' first.")
	}

	// Ensure the static directory exists in the same directory as the binary
	binaryDir := filepath.Dir(binaryPath)
	staticDir := filepath.Join(binaryDir, "static")

	// Check if the static directory exists
	if _, err := os.Stat(staticDir); os.IsNotExist(err) {
		fmt.Printf("Static directory not found at %s, creating it...\n", staticDir)

		// Create the static directory
		if err := os.MkdirAll(staticDir, 0755); err != nil {
			log.Fatalf("Error creating static directory: %v", err)
		}

		// Copy static files from the backend directory if they exist
		srcStaticDir := filepath.Join(projectRoot, "src", "backend", "static")
		if _, err := os.Stat(srcStaticDir); err == nil {
			fmt.Printf("Copying static files from %s to %s...\n", srcStaticDir, staticDir)
			if err := copyDir(srcStaticDir, staticDir); err != nil {
				log.Fatalf("Error copying static files: %v", err)
			}
		} else {
			fmt.Printf("Warning: Source static directory not found at %s\n", srcStaticDir)
		}
	}

	fmt.Printf("Using binary: %s\n", binaryPath)
	fmt.Println("Starting ADGitOps Extract...")

	// Prepare command arguments
	cmdArgs := []string{}

	// Add port if specified
	if startOpts.port != "" {
		cmdArgs = append(cmdArgs, "-port", startOpts.port)
	}

	// Add repos-dir if specified (renamed from repo-dir)
	if startOpts.repoDir != "" {
		cmdArgs = append(cmdArgs, "-repos-dir", startOpts.repoDir)
	}

	// Add data-dir if specified (renamed from export-dir)
	if startOpts.exportDir != "" {
		cmdArgs = append(cmdArgs, "-data-dir", startOpts.exportDir)
	}

	// Poll seconds is not directly supported by the binary, so we'll skip it

	// Execute the binary
	cmd := exec.Command(binaryPath, cmdArgs...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		log.Fatalf("Error running application: %v", err)
	}
}

// Helper functions

// buildFrontend builds the frontend and copies the output to the backend static directory
func buildFrontend(projectRoot string) error {
	// Set paths
	frontendDir := filepath.Join(projectRoot, "src", "frontend")
	backendStaticDir := filepath.Join(projectRoot, "src", "backend", "static")

	// Change to frontend directory
	fmt.Println("Changing to frontend directory...")
	if err := os.Chdir(frontendDir); err != nil {
		return fmt.Errorf("error changing to frontend directory: %v", err)
	}

	// Install dependencies
	fmt.Println("Installing frontend dependencies...")
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", "pnpm", "install", "--frozen-lockfile")
	} else {
		cmd = exec.Command("pnpm", "install", "--frozen-lockfile")
	}
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("error installing frontend dependencies: %v", err)
	}

	// Build frontend
	fmt.Println("Building frontend...")
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", "pnpm", "build")
	} else {
		cmd = exec.Command("pnpm", "build")
	}
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("error building frontend: %v", err)
	}

	// Create backend static directory if it doesn't exist
	fmt.Println("Creating backend static directory...")
	if err := os.MkdirAll(backendStaticDir, 0755); err != nil {
		return fmt.Errorf("error creating backend static directory: %v", err)
	}

	// Copy frontend build to backend static directory
	fmt.Println("Copying frontend build to backend static directory...")
	frontendBuildDir := filepath.Join(frontendDir, "dist")

	// Get all files in the dist directory
	files, err := os.ReadDir(frontendBuildDir)
	if err != nil {
		return fmt.Errorf("error reading frontend build directory: %v", err)
	}

	// Copy each file/directory
	for _, file := range files {
		srcPath := filepath.Join(frontendBuildDir, file.Name())
		dstPath := filepath.Join(backendStaticDir, file.Name())

		if file.IsDir() {
			// Copy directory recursively
			if err := copyDir(srcPath, dstPath); err != nil {
				return fmt.Errorf("error copying directory %s: %v", file.Name(), err)
			}
		} else {
			// Copy file
			if err := copyFile(srcPath, dstPath); err != nil {
				return fmt.Errorf("error copying file %s: %v", file.Name(), err)
			}
		}
	}

	// Change back to project root
	if err := os.Chdir(projectRoot); err != nil {
		return fmt.Errorf("error changing back to project root: %v", err)
	}

	fmt.Println("Frontend build completed successfully!")
	return nil
}

// copyFile copies a single file from src to dst
func copyFile(src, dst string) error {
	// Read the source file
	data, err := os.ReadFile(src)
	if err != nil {
		return err
	}

	// Write to the destination file
	return os.WriteFile(dst, data, 0644)
}

// copyDir recursively copies a directory tree from src to dst
func copyDir(src, dst string) error {
	// Create the destination directory
	if err := os.MkdirAll(dst, 0755); err != nil {
		return err
	}

	// Get all files in the source directory
	entries, err := os.ReadDir(src)
	if err != nil {
		return err
	}

	// Copy each file/directory
	for _, entry := range entries {
		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		if entry.IsDir() {
			// Recursively copy subdirectory
			if err := copyDir(srcPath, dstPath); err != nil {
				return err
			}
		} else {
			// Copy file
			if err := copyFile(srcPath, dstPath); err != nil {
				return err
			}
		}
	}

	return nil
}

// isPortInUse checks if a port is already in use using Go's native networking
func isPortInUse(port string) bool {
	// Convert port string to integer
	portNum, err := strconv.Atoi(port)
	if err != nil {
		log.Printf("Invalid port number: %s", port)
		return false
	}

	// For frontend port (5173), try to connect to it directly
	if port == defaultFrontendPort {
		// Try to connect to the port
		conn, err := net.DialTimeout("tcp", fmt.Sprintf("localhost:%d", portNum), 100*time.Millisecond)
		if err == nil {
			// If we can connect, the port is in use
			conn.Close()
			return true
		}
		return false
	}

	// For other ports, try to listen on the port to see if it's available
	// First try TCP
	tcpAddr, err := net.ResolveTCPAddr("tcp", fmt.Sprintf(":%d", portNum))
	if err != nil {
		log.Printf("Error resolving TCP address: %v", err)
		return false
	}

	tcpListener, err := net.ListenTCP("tcp", tcpAddr)
	if err != nil {
		// If we can't listen, the port is in use
		return true
	}

	// Close the listener since we're just checking
	tcpListener.Close()

	// Also check UDP for completeness
	udpAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf(":%d", portNum))
	if err != nil {
		log.Printf("Error resolving UDP address: %v", err)
		return false
	}

	udpConn, err := net.ListenUDP("udp", udpAddr)
	if err != nil {
		// If we can't listen, the port is in use
		return true
	}

	// Close the connection since we're just checking
	udpConn.Close()

	// If we can listen on both TCP and UDP, the port is free
	return false
}

// startBackendDev starts the backend server in development mode
func startBackendDev(projectRoot string) *exec.Cmd {
	var cmd *exec.Cmd

	// Use "go run src/backend/main.go" as specified in the user's instructions
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("cmd", "/c", "go", "run", "src/backend/main.go")
	default:
		cmd = exec.Command("go", "run", "src/backend/main.go")
	}

	cmd.Dir = projectRoot
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Start(); err != nil {
		log.Fatalf("Error starting backend server: %v", err)
	}

	return cmd
}

// startFrontendDev starts the frontend server in development mode
func startFrontendDev(projectRoot string) *exec.Cmd {
	frontendDir := filepath.Join(projectRoot, "src", "frontend")

	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("cmd", "/c", "pnpm", "dev")
	default:
		cmd = exec.Command("pnpm", "dev")
	}

	cmd.Dir = frontendDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Start(); err != nil {
		log.Fatalf("Error starting frontend server: %v", err)
	}

	return cmd
}

// killProcessAndChildren kills a process and its children
func killProcessAndChildren(pid int) {
	switch runtime.GOOS {
	case "windows":
		exec.Command("taskkill", "/F", "/T", "/PID", fmt.Sprintf("%d", pid)).Run()
	default:
		// On Unix-like systems, we can use the process group
		// This will be handled differently on Unix systems
		cmd := exec.Command("sh", "-c", fmt.Sprintf("kill -9 %d", pid))
		cmd.Run()
	}
}

// isCommandAvailable checks if a command is available in the PATH
func isCommandAvailable(command string) bool {
	_, err := exec.LookPath(command)
	return err == nil
}

// findBinary finds the built binary
func findBinary(projectRoot string) string {
	// Define possible binary locations based on OS and architecture
	var binaryLocations []string
	binaryName := "adgitops-ui"
	if runtime.GOOS == "windows" {
		binaryName += ".exe"
	}

	// Check GoReleaser output directories first
	distDir := filepath.Join(projectRoot, "dist")

	// Check OS and architecture specific directories
	osArchDir := fmt.Sprintf("%s_%s", runtime.GOOS, runtime.GOARCH)

	// Add more specific patterns for GoReleaser output
	if runtime.GOOS == "windows" {
		// Windows with architecture variant
		binaryLocations = append(binaryLocations, filepath.Join(distDir, fmt.Sprintf("adgitops-uis_v1", runtime.GOOS, runtime.GOARCH), binaryName))
	}

	// Add the original patterns
	binaryLocations = append(binaryLocations, filepath.Join(distDir, fmt.Sprintf("%s_%s", binaryName, osArchDir), binaryName))
	binaryLocations = append(binaryLocations, filepath.Join(distDir, osArchDir, binaryName))

	// Check common locations
	binaryLocations = append(binaryLocations, filepath.Join(distDir, binaryName))

	// Also check in the root directory
	binaryLocations = append(binaryLocations, filepath.Join(projectRoot, binaryName))

	// Print all locations being checked (for debugging)
	fmt.Println("Checking for binary in the following locations:")
	for _, location := range binaryLocations {
		fmt.Printf("- %s\n", location)
		if _, err := os.Stat(location); err == nil {
			fmt.Printf("Found binary at: %s\n", location)
			return location
		}
	}

	return ""
}

// killServerOnPort kills a process running on a specific port
func killServerOnPort(port string) {
	switch runtime.GOOS {
	case "windows":
		// Find the PID of the process using the port
		findCmd := exec.Command("cmd", "/c", fmt.Sprintf("netstat -ano | findstr :%s | findstr LISTENING", port))
		output, err := findCmd.CombinedOutput()
		if err != nil {
			log.Printf("No process found on port %s", port)
			return
		}

		// Parse the output to get the PID
		lines := strings.Split(string(output), "\n")
		if len(lines) == 0 {
			log.Printf("No process found on port %s", port)
			return
		}

		// The PID is the last column in the netstat output
		parts := strings.Fields(lines[0])
		if len(parts) < 5 {
			log.Printf("Could not parse netstat output: %s", lines[0])
			return
		}

		pid := parts[len(parts)-1]
		fmt.Printf("Killing process with PID %s on port %s\n", pid, port)

		// Kill the process
		killCmd := exec.Command("taskkill", "/F", "/PID", pid)
		killCmd.Run()
	default:
		// For Unix-like systems
		findCmd := exec.Command("sh", "-c", fmt.Sprintf("lsof -i :%s -t", port))
		output, err := findCmd.CombinedOutput()
		if err != nil {
			log.Printf("No process found on port %s", port)
			return
		}

		// Parse the output to get the PID
		pid := strings.TrimSpace(string(output))
		if pid == "" {
			log.Printf("No process found on port %s", port)
			return
		}

		fmt.Printf("Killing process with PID %s on port %s\n", pid, port)

		// Kill the process
		killCmd := exec.Command("sh", "-c", fmt.Sprintf("kill -9 %s", pid))
		killCmd.Run()
	}
}

// waitForPortRelease waits for a port to be released
func waitForPortRelease(port string) {
	maxAttempts := 10
	for i := 0; i < maxAttempts; i++ {
		if !isPortInUse(port) {
			return
		}
		fmt.Printf("Waiting for port %s to be released... (%d/%d)\n", port, i+1, maxAttempts)
		time.Sleep(500 * time.Millisecond)
	}
	log.Printf("Warning: Port %s may still be in use after waiting", port)
}

// verifyServerStarted checks if a server has started successfully
func verifyServerStarted(port string) bool {
	maxAttempts := 40 // Increase max attempts to give more time for startup
	for i := 0; i < maxAttempts; i++ {
		// First check if the port is in use
		if isPortInUse(port) {
			// For frontend, we need to give it more time to initialize
			if port == defaultFrontendPort {
				// Give it a few more seconds to fully initialize
				time.Sleep(2 * time.Second)
			}
			return true
		}

		// If we're still waiting, print a message every 5 attempts
		if i > 0 && i%5 == 0 {
			fmt.Printf("Still waiting for server on port %s to start... (%d/%d)\n", port, i, maxAttempts)
		}

		time.Sleep(500 * time.Millisecond)
	}
	return false
}
