// React is imported for JSX support
import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import ExecutionHistory from './ExecutionHistory'
import { apiClient, ReportPreset } from '@/api/client'
import { useRepository } from '@/context/RepositoryContext'
import { Calendar } from 'lucide-react'

interface ExecutionHistoryDialogProps {
  presetId: string
  open: boolean
  onOpenChange: (open: boolean) => void
  presetName: string
  sharedPresetId?: string
}

export default function ExecutionHistoryDialog({
  presetId,
  open,
  onOpenChange,
  presetName,
  sharedPresetId
}: ExecutionHistoryDialogProps) {
  const { selectedRepoId } = useRepository()
  const [preset, setPreset] = useState<ReportPreset | null>(null)
  // Loading state for API calls
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setLoading] = useState(false)

  // Generate a unique key when the dialog opens or the preset changes
  // This ensures the ExecutionHistory component is remounted with fresh data
  const dialogKey = `${presetId}-${open ? 'open' : 'closed'}`;

  // Fetch preset details when dialog opens
  useEffect(() => {
    if (open && presetId && selectedRepoId) {
      setLoading(true)
      apiClient.data.getReportPreset(selectedRepoId, presetId)
        .then(data => {
          setPreset(data)
        })
        .catch(err => {
          console.error('Failed to fetch preset details:', err)
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      setPreset(null)
    }
  }, [open, presetId, selectedRepoId])

  // Format next run date for display
  const formatNextRun = (dateString?: string) => {
    if (!dateString) return 'Not scheduled'
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  // If the dialog is not open, render a minimal version
  if (!open) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Execution History</DialogTitle>
            <DialogDescription>
              Showing execution history for preset: {presetName}
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Execution History</DialogTitle>
          <DialogDescription className="space-y-1">
            <div>Showing execution history for preset: {presetName}</div>

            {preset?.schedule?.enabled && preset?.schedule?.nextRun && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Calendar className="h-4 w-4 mr-1" />
                <span>Next scheduled run: {formatNextRun(preset.schedule.nextRun)}</span>
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          {/* Use the key to force remounting when the dialog opens or preset changes */}
          {sharedPresetId ? (
            <ExecutionHistory
              key={dialogKey}
              sharedPresetId={sharedPresetId}
            />
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No execution history available for this preset.
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
