package services

import (
	"encoding/json"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"adgitops-ui/src/backend/models"
)

// Create a test environment for reports testing
func setupTestReportEnvironment(t *testing.T) (*DataProcessor, string, func()) {
	// Create a temporary test directory
	tempDir, err := os.MkdirTemp("", "reports_test")
	assert.NoError(t, err)

	// Create exports subdirectory
	exportsDir := filepath.Join(tempDir, "exports")
	err = os.MkdirAll(exportsDir, 0755)
	assert.NoError(t, err)

	// Initialize the data processor
	dp := NewDataProcessor(exportsDir)

	// Return cleanup function
	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return dp, tempDir, cleanup
}

// Test creating and retrieving report presets
func TestReportPresets(t *testing.T) {
	dp, _, cleanup := setupTestReportEnvironment(t)
	defer cleanup()

	// Create a test preset
	preset := models.ReportPreset{
		Name:        "Test Preset",
		Description: "Test preset description",
		ReportType:  "users",
		Query: models.QueryParams{
			LOB:   "Marketing",
			Types: []string{"security"},
		},
	}

	// Save preset
	saved, err := dp.SavePreset(preset)
	assert.NoError(t, err)
	assert.NotEmpty(t, saved.ID)
	assert.NotEmpty(t, saved.CreatedAt)
	assert.Equal(t, preset.Name, saved.Name)
	assert.True(t, saved.IsActive, "New preset should be active by default")

	// Get all presets
	presets, err := dp.GetPresets()
	assert.NoError(t, err)
	assert.Len(t, presets, 1)
	assert.Equal(t, saved.ID, presets[0].ID)

	// Get preset by ID
	retrieved, err := dp.GetPreset(saved.ID)
	assert.NoError(t, err)
	assert.Equal(t, saved.ID, retrieved.ID)
	assert.Equal(t, saved.Name, retrieved.Name)

	// Update preset
	retrieved.Name = "Updated Name"
	updated, err := dp.SavePreset(retrieved)
	assert.NoError(t, err)
	assert.Equal(t, "Updated Name", updated.Name)
	assert.NotEmpty(t, updated.UpdatedAt)

	// Verify the preset exists with the updated name
	retrieved, err = dp.GetPreset(updated.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Updated Name", retrieved.Name)

	// Create a test report for the preset
	// This will prevent the preset from being deleted
	report := models.Report{
		ID:         "test_report",
		PresetID:   updated.ID,
		PresetName: updated.Name,
		Type:       "groups",
		Filename:   "test_report.json",
		CreatedAt:  time.Now().Format(time.RFC3339),
	}

	// Save the report metadata
	err = dp.saveReportMetadata(report)
	assert.NoError(t, err)

	// Verify the report exists
	reports, err := dp.GetReports()
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(reports), 1)

	// Delete preset (which should just deactivate it since it has reports)
	// Pass nil for schedulerService since we don't need to delete execution history in this test
	err = dp.DeletePreset(updated.ID, nil)
	assert.NoError(t, err)

	// Verify the preset is now inactive but still exists
	presets, err = dp.GetPresets()
	assert.NoError(t, err)

	// Find the preset in the list
	var foundPreset bool
	for _, p := range presets {
		if p.ID == updated.ID {
			foundPreset = true
			assert.False(t, p.IsActive, "Preset should be inactive after deletion")
			break
		}
	}
	assert.True(t, foundPreset, "Preset should still exist after deactivation")
}

// Test generating reports
func TestGenerateReport(t *testing.T) {
	dp, tempDir, cleanup := setupTestReportEnvironment(t)
	defer cleanup()

	// Create test data directory
	dataDir := filepath.Join(tempDir, "data")
	err := os.MkdirAll(dataDir, 0755)
	assert.NoError(t, err)

	// Create test groups
	groups := []models.Group{
		{
			Groupname: "group1",
			Lob:       "Marketing",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group2",
			Lob:       "Engineering",
			Type:      "distribution",
			Members:   models.GroupMembers{},
		},
	}

	// Create test users - not used in this test but kept for reference
	_ = []models.User{
		{Name: "User One", Groups: []string{"group1"}},
		{Name: "User Two", Groups: []string{"group1"}},
		{Name: "User Three", Groups: []string{"group2"}},
		{Name: "User Four", Groups: []string{"group2"}},
	}

	// Test generating groups report with LOB filter
	query := models.QueryParams{
		LOB: "Marketing",
	}
	report, err := dp.GenerateReport(groups, "groups", query, "", "")
	assert.NoError(t, err)
	assert.NotEmpty(t, report.ID)
	assert.Equal(t, "groups", report.Type)
	assert.NotEmpty(t, report.Filename)

	// Verify the report file exists
	reportPath := filepath.Join(dp.reportsFolder, report.Filename)
	_, err = os.Stat(reportPath)
	assert.NoError(t, err)

	// Read the report content
	content, err := os.ReadFile(reportPath)
	assert.NoError(t, err)

	// Deserialize the content
	var reportGroups []models.Group
	err = json.Unmarshal(content, &reportGroups)
	assert.NoError(t, err)

	// Verify only Marketing LOB groups are included
	assert.Len(t, reportGroups, 1)
	assert.Equal(t, "Marketing", reportGroups[0].Lob)

	// Test generating users report
	report, err = dp.GenerateReport(groups, "users", models.QueryParams{}, "", "")
	assert.NoError(t, err)
	assert.Equal(t, "users", report.Type)

	// Test generating combined report
	report, err = dp.GenerateReport(groups, "both", models.QueryParams{}, "", "")
	assert.NoError(t, err)
	assert.Equal(t, "both", report.Type)

	// Test report listing
	reports, err := dp.GetReports()
	assert.NoError(t, err)
	// We should have at least 3 reports: groups, users, and both
	assert.GreaterOrEqual(t, len(reports), 3)
}

// Test report generation from preset
func TestGenerateReportFromPreset(t *testing.T) {
	dp, _, cleanup := setupTestReportEnvironment(t)
	defer cleanup()

	// Create test groups
	groups := []models.Group{
		{
			Groupname: "group1",
			Lob:       "Marketing",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group2",
			Lob:       "Engineering",
			Type:      "distribution",
			Members:   models.GroupMembers{},
		},
	}

	// Create a preset
	preset := models.ReportPreset{
		Name:       "Marketing Groups",
		ReportType: "groups",
		Query: models.QueryParams{
			LOB: "Marketing",
		},
	}
	saved, err := dp.SavePreset(preset)
	assert.NoError(t, err)

	// Generate report from preset
	report, err := dp.GenerateReportFromPreset(groups, saved.ID)
	assert.NoError(t, err)
	assert.Equal(t, saved.ID, report.PresetID)
	assert.Equal(t, saved.Name, report.PresetName)
	assert.Equal(t, "groups", report.Type)

	// Verify the report content
	reportPath := filepath.Join(dp.reportsFolder, report.Filename)
	content, err := os.ReadFile(reportPath)
	assert.NoError(t, err)

	var reportGroups []models.Group
	err = json.Unmarshal(content, &reportGroups)
	assert.NoError(t, err)

	// Verify only Marketing LOB groups are included
	assert.Len(t, reportGroups, 1)
	assert.Equal(t, "Marketing", reportGroups[0].Lob)
}

// Test report generation from preset with search query
func TestGenerateReportFromPresetWithSearchQuery(t *testing.T) {
	dp, _, cleanup := setupTestReportEnvironment(t)
	defer cleanup()

	// Create test groups
	groups := []models.Group{
		{
			Groupname: "group1",
			Lob:       "Marketing",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group2",
			Lob:       "Engineering",
			Type:      "distribution",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group3",
			Lob:       "Finance",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
	}

	// Create a preset with search query
	// Since we can't directly use the search query in this test,
	// we'll use the Query field with the Type filter instead
	preset := models.ReportPreset{
		ID:          uuid.New().String(),
		Name:        "Security Groups",
		ReportType:  "groups",
		SearchQuery: "type:security", // This won't be used in the test
		Query: models.QueryParams{
			Types: []string{"security"},
		},
		CreatedAt: "2023-01-01T00:00:00Z",
	}

	// Save the preset to the test environment
	_, err := dp.SavePreset(preset)
	assert.NoError(t, err)

	// Generate report from preset
	report, err := dp.GenerateReportFromPreset(groups, preset.ID)
	assert.NoError(t, err)
	assert.Equal(t, preset.ID, report.PresetID)
	assert.Equal(t, preset.Name, report.PresetName)
	assert.Equal(t, "groups", report.Type)

	// Verify the report content
	reportPath := filepath.Join(dp.reportsFolder, report.Filename)
	content, err := os.ReadFile(reportPath)
	assert.NoError(t, err)

	var reportGroups []models.Group
	err = json.Unmarshal(content, &reportGroups)
	assert.NoError(t, err)

	// Verify only security groups are included
	assert.Len(t, reportGroups, 2)
	for _, group := range reportGroups {
		assert.Equal(t, "security", group.Type)
	}

	// No need to restore FilterGroups function since we're not mocking it anymore
}

// Test toggling preset activation
func TestTogglePresetActivation(t *testing.T) {
	dp, tempDir, cleanup := setupTestReportEnvironment(t)
	defer cleanup()

	// Create a test preset
	preset := models.ReportPreset{
		Name:        "Toggle Test Preset",
		Description: "Test preset for toggling activation",
		ReportType:  "users",
		Query: models.QueryParams{
			LOB: "Marketing",
		},
	}

	// Save preset
	saved, err := dp.SavePreset(preset)
	assert.NoError(t, err)
	assert.True(t, saved.IsActive, "New preset should be active by default")

	// Toggle activation (active -> inactive)
	toggled, err := dp.TogglePresetActivation(saved.ID)
	assert.NoError(t, err)
	assert.False(t, toggled.IsActive, "Preset should be inactive after toggling")

	// Verify the change in the file
	presetFilePath := filepath.Join(tempDir, "exports", presetsFilename)
	fileContent, err := os.ReadFile(presetFilePath)
	assert.NoError(t, err)

	// Check if the file contains the correct isActive value
	assert.Contains(t, string(fileContent), `"isActive": false`)

	// Get all presets to verify the change is reflected in the list
	allPresets, err := dp.GetPresets()
	assert.NoError(t, err)
	var foundInList bool
	for _, p := range allPresets {
		if p.ID == saved.ID {
			foundInList = true
			assert.False(t, p.IsActive, "Preset should be inactive in the list")
			break
		}
	}
	assert.True(t, foundInList, "Preset should be found in the list")

	// Toggle again (inactive -> active)
	toggled, err = dp.TogglePresetActivation(saved.ID)
	assert.NoError(t, err)
	assert.True(t, toggled.IsActive, "Preset should be active after toggling again")

	// Verify the change in the file again
	fileContent, err = os.ReadFile(presetFilePath)
	assert.NoError(t, err)
	assert.Contains(t, string(fileContent), `"isActive": true`)

	// Get the preset directly to verify the change persisted
	retrieved, err := dp.GetPreset(saved.ID)
	assert.NoError(t, err)
	assert.True(t, retrieved.IsActive, "Preset should still be active when retrieved")

	// Get all presets again to verify the change is reflected in the list
	allPresets, err = dp.GetPresets()
	assert.NoError(t, err)
	foundInList = false
	for _, p := range allPresets {
		if p.ID == saved.ID {
			foundInList = true
			assert.True(t, p.IsActive, "Preset should be active in the list")
			break
		}
	}
	assert.True(t, foundInList, "Preset should be found in the list")
}

// Test filtering logic - moved to filter_groups_test.go

// Test batch delete reports
func TestBatchDeleteReports(t *testing.T) {
	dp, _, cleanup := setupTestReportEnvironment(t)
	defer cleanup()

	// Create test groups
	groups := []models.Group{
		{
			Groupname: "group1",
			Lob:       "Marketing",
			Type:      "security",
			Members:   models.GroupMembers{},
		},
		{
			Groupname: "group2",
			Lob:       "Engineering",
			Type:      "distribution",
			Members:   models.GroupMembers{},
		},
	}

	// Generate multiple reports
	report1, err := dp.GenerateReport(groups, "groups", models.QueryParams{LOB: "Marketing"}, "", "Report 1")
	assert.NoError(t, err)
	assert.NotEmpty(t, report1.ID)

	report2, err := dp.GenerateReport(groups, "users", models.QueryParams{}, "", "Report 2")
	assert.NoError(t, err)
	assert.NotEmpty(t, report2.ID)

	report3, err := dp.GenerateReport(groups, "both", models.QueryParams{}, "", "Report 3")
	assert.NoError(t, err)
	assert.NotEmpty(t, report3.ID)

	// Verify reports exist
	reports, err := dp.GetReports()
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(reports), 3)

	// Test batch delete with valid IDs
	result, err := dp.BatchDeleteReports([]string{report1.ID, report2.ID})
	assert.NoError(t, err)
	assert.Equal(t, 2, result.TotalCount)
	assert.Equal(t, 2, result.SuccessCount)
	assert.Equal(t, 0, result.FailedCount)
	assert.Empty(t, result.FailedReportIDs)

	// Verify reports were deleted
	reports, err = dp.GetReports()
	assert.NoError(t, err)

	// Only report3 should remain
	found := false
	for _, report := range reports {
		assert.NotEqual(t, report1.ID, report.ID, "Report 1 should be deleted")
		assert.NotEqual(t, report2.ID, report.ID, "Report 2 should be deleted")
		if report.ID == report3.ID {
			found = true
		}
	}
	assert.True(t, found, "Report 3 should still exist")

	// Test batch delete with non-existent ID
	nonExistentID := "non_existent_id"
	result, err = dp.BatchDeleteReports([]string{report3.ID, nonExistentID})
	assert.NoError(t, err)
	assert.Equal(t, 2, result.TotalCount)
	assert.Equal(t, 1, result.SuccessCount)
	assert.Equal(t, 0, result.FailedCount) // Non-existent IDs don't count as failures

	// Verify all reports are now deleted
	reports, err = dp.GetReports()
	assert.NoError(t, err)
	assert.Empty(t, reports, "All reports should be deleted")

	// Test batch delete with empty list
	_, err = dp.BatchDeleteReports([]string{})
	assert.Error(t, err, "Should return error for empty list")
}
