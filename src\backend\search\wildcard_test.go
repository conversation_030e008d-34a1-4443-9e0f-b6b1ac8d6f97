package search

import (
	"adgitops-ui/src/backend/models"
	"context"
	"testing"
)

func TestWildcardSearch(t *testing.T) {
	// Create a parser
	parser := NewParser()

	// Test cases for wildcard parsing
	testCases := []struct {
		name           string
		query          string
		expectedField  string
		expectedOp     string
		expectedValue  string
		expectedQuoted bool
	}{
		{"Contains wildcard", "groupname:*harbor*", "groupname", "~", "harbor", false},
		{"Prefix wildcard", "groupname:harbor*", "groupname", "^", "harbor", false},
		{"Suffix wildcard", "groupname:*harbor", "groupname", "$", "harbor", false},
		{"Contains wildcard with spaces", "groupname:*harbor users*", "groupname", "~", "harbor users", false},
		{"Simple contains", "groupname:~harbor", "groupname", "~", "harbor", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			node, err := parser.ParseQuery(tc.query)
			if err != nil {
				t.Fatalf("Failed to parse query: %v", err)
			}
			if node == nil {
				t.Fatalf("Expected node, got nil")
			}
			if node.Type != NodeTerm {
				t.Fatalf("Expected NodeTerm, got %v", node.Type)
			}
			if node.Field != tc.expectedField {
				t.Errorf("Expected field %s, got %s", tc.expectedField, node.Field)
			}
			if node.Operator != tc.expectedOp {
				t.Errorf("Expected operator %s, got %s", tc.expectedOp, node.Operator)
			}
			if node.Value != tc.expectedValue {
				t.Errorf("Expected value %s, got %s", tc.expectedValue, node.Value)
			}
			if node.IsQuoted != tc.expectedQuoted {
				t.Errorf("Expected isQuoted %v, got %v", tc.expectedQuoted, node.IsQuoted)
			}
		})
	}
}

func TestWildcardSearchEngine(t *testing.T) {
	// Create a search engine
	engine := NewEngine()

	// Create test groups
	groups := []models.Group{
		{
			Groupname:   "admin_group",
			Type:        "security",
			Lob:         "it",
			Description: "Admin group for IT",
			Members: []models.Member{
				{Name: "john", Type: "user"},
				{Name: "jane", Type: "user"},
			},
		},
		{
			Groupname:   "dev_team",
			Type:        "technical",
			Lob:         "finance",
			Description: "Development team for finance",
			Members: []models.Member{
				{Name: "alice", Type: "user"},
				{Name: "bob", Type: "user"},
			},
		},
		{
			Groupname:   "harbor_administrators",
			Type:        "org_group",
			Lob:         "cloud",
			Description: "Harbor registry administrators",
		},
		{
			Groupname:   "harbor_users",
			Type:        "org_group",
			Lob:         "fm",
			Description: "Harbor registry users",
			Members: []models.Member{
				{Name: "john", Type: "user"},
				{Name: "alice", Type: "user"},
			},
		},
	}

	// Test cases for wildcard search
	testCases := []struct {
		name     string
		query    string
		expected []string // Expected group names
	}{
		{"Contains wildcard", "groupname:*harbor*", []string{"harbor_administrators", "harbor_users"}},
		{"Prefix wildcard", "groupname:harbor*", []string{"harbor_administrators", "harbor_users"}},
		{"Suffix wildcard", "groupname:*group", []string{"admin_group"}},
		{"Contains wildcard in description", "description:*registry*", []string{"harbor_administrators", "harbor_users"}},
		{"Contains wildcard in members", "members:*oh*", []string{"admin_group", "harbor_users"}},
		{"Simple contains", "groupname:~harbor", []string{"harbor_administrators", "harbor_users"}},
	}

	ctx := context.Background()

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := engine.SearchGroups(ctx, tc.query, groups)
			if err != nil {
				t.Fatalf("Failed to search groups: %v", err)
			}

			// Check if the number of results matches
			if len(result) != len(tc.expected) {
				t.Errorf("Expected %d results, got %d for query: %s", len(tc.expected), len(result), tc.query)
				for _, r := range result {
					t.Logf("Got: %s", r.Groupname)
				}
				return
			}

			// Check if all expected groups are in the result
			resultNames := make(map[string]bool)
			for _, r := range result {
				resultNames[r.Groupname] = true
			}

			for _, expected := range tc.expected {
				if !resultNames[expected] {
					t.Errorf("Expected group %s not found in results for query: %s", expected, tc.query)
				}
			}
		})
	}
}
