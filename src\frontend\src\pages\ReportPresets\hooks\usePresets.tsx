import { useState, useEffect, useRef, useCallback } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { apiClient, Report } from '@/api/client'
import { useNavigate } from 'react-router-dom'
import { PresetFormState, ReportPreset } from '../types'

export function usePresets(selectedRepoId: string) {
  const { toast } = useToast()
  const navigate = useNavigate()

  // State
  const [presets, setPresets] = useState<ReportPreset[] | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  // We keep reportsData for backward compatibility
  const [reportsData] = useState<Report[]>([])
  const [deletePresetId, setDeletePresetId] = useState<string | null>(null)
  const [showConfirmDelete, setShowConfirmDelete] = useState<boolean>(false)

  // Refs
  const isFetchingPresetsRef = useRef<boolean>(false)
  const lastPresetsFetchTimeRef = useRef<number>(0)
  const isMountedRef = useRef<boolean>(true)

  // Fetch report presets
  const fetchPresets = useCallback(async (forceRefresh = false) => {
    // Skip if we're already fetching
    if (isFetchingPresetsRef.current) {
            return;
    }

    // Skip if we've fetched presets recently (within the last 2 seconds) unless force refresh is requested
    const now = Date.now();
    if (!forceRefresh && lastPresetsFetchTimeRef.current > 0 && now - lastPresetsFetchTimeRef.current < 2000) {
            return;
    }

        isFetchingPresetsRef.current = true;
    setLoading(true)
    setError(null)

    try {
      // Clear the cache for report presets to ensure we get fresh data
      apiClient.utils.cancelRequestsByPattern('/data/reports/presets')

      // Fetch the presets for the selected repository
      const presets = await apiClient.data.getReportPresets(selectedRepoId)
            setPresets(presets || [])

      // Update the last fetch time
      lastPresetsFetchTimeRef.current = Date.now();
    } catch (err) {
      console.error(err)
      setError('Failed to fetch report presets')
      toast({
        title: "Error",
        description: "Failed to fetch report presets",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
      isFetchingPresetsRef.current = false;
    }
  }, [selectedRepoId, toast])

  // This function has been moved to the ReportPresetsPage component

  // Create a new preset
  const createPreset = useCallback(async (formData: PresetFormState) => {
    try {
      const newPreset = {
        name: formData.name,
        description: formData.description,
        reportType: formData.reportType,
        searchQuery: formData.searchQuery,
        flattenMembership: formData.flattenMembership,
        schedule: formData.schedule,
        groupColumns: formData.groupColumns,
        userColumns: formData.userColumns,
        // Legacy fields
        lob: formData.lob,
        types: formData.types,
        groupIds: formData.groupIds,
        userIds: formData.userIds,
      }

      const response = await apiClient.data.createReportPreset(selectedRepoId, newPreset as any)

      toast({
        title: "Success",
        description: "Report preset created successfully",
      })

      // Refresh the presets list
      fetchPresets(true)

      return response
    } catch (err) {
      console.error('Failed to create preset:', err)
      toast({
        title: "Error",
        description: "Failed to create report preset",
        variant: "destructive",
      })
      throw err
    }
  }, [selectedRepoId, fetchPresets, toast])

  // Update an existing preset
  const updatePreset = useCallback(async (presetId: string, formData: PresetFormState) => {
    try {
      const updatedPreset = {
        name: formData.name,
        description: formData.description,
        reportType: formData.reportType,
        searchQuery: formData.searchQuery,
        flattenMembership: formData.flattenMembership,
        schedule: formData.schedule,
        groupColumns: formData.groupColumns,
        userColumns: formData.userColumns,
        // Legacy fields
        lob: formData.lob,
        types: formData.types,
        groupIds: formData.groupIds,
        userIds: formData.userIds,
      }

      const response = await apiClient.data.updateReportPreset(selectedRepoId, presetId, updatedPreset as any)

      toast({
        title: "Success",
        description: "Report preset updated successfully",
      })

      // Refresh the presets list
      fetchPresets(true)

      return response
    } catch (err) {
      console.error('Failed to update preset:', err)
      toast({
        title: "Error",
        description: "Failed to update report preset",
        variant: "destructive",
      })
      throw err
    }
  }, [selectedRepoId, fetchPresets, toast])

  // Delete a preset
  const deletePreset = useCallback(async () => {
    if (!deletePresetId) return;

    try {
      const response = await apiClient.data.deleteReportPreset(selectedRepoId, deletePresetId)

      // Check if the preset was deleted or just deactivated
      if (response.message.includes('deleted')) {
        toast({
          title: "Success",
          description: "Report preset deleted successfully",
        })
      } else {
        toast({
          title: "Success",
          description: "Report preset deactivated successfully",
        })
      }

      // Refresh the presets list
      fetchPresets(true)

      // Reset the delete state
      setDeletePresetId(null)
      setShowConfirmDelete(false)

      return response
    } catch (err) {
      console.error('Failed to delete preset:', err)
      toast({
        title: "Error",
        description: "Failed to delete report preset",
        variant: "destructive",
      })
      throw err
    }
  }, [selectedRepoId, deletePresetId, fetchPresets, toast])

  // Toggle preset activation
  const togglePresetActivation = useCallback(async (presetId: string) => {
    try {
      // Use the dedicated endpoint for toggling activation
      // This will toggle all versions of the preset without creating a new version
      const response = await apiClient.data.togglePresetActivation(selectedRepoId, presetId)

      toast({
        title: "Success",
        description: `Report preset ${response.isActive ? 'activated' : 'deactivated'} successfully`,
      })

      // Refresh the presets list
      fetchPresets(true)

      return response
    } catch (err) {
      console.error('Failed to toggle preset activation:', err)
      toast({
        title: "Error",
        description: "Failed to update report preset",
        variant: "destructive",
      })
      throw err
    }
  }, [selectedRepoId, fetchPresets, toast])

  // Generate a report from a preset
  const generateReport = useCallback(async (preset: ReportPreset) => {
    try {
      // The third parameter is optional, so we can omit it
      const response = await apiClient.data.generateReport(selectedRepoId, preset.id!, {})

      toast({
        title: "Success",
        description: "Report generated successfully",
      })

      // Navigate to the reports page
      navigate(`/reports?presetId=${preset.id}`)

      return response
    } catch (err) {
      console.error('Failed to generate report:', err)
      toast({
        title: "Error",
        description: "Failed to generate report",
        variant: "destructive",
      })
      throw err
    }
  }, [selectedRepoId, navigate, toast])

  // Fetch presets when repository changes
  // We use a ref to track if we've already fetched presets for this repository
  const hasInitializedRef = useRef<boolean>(false);
  const lastRepoIdRef = useRef<string>('');

  useEffect(() => {

    // Skip if the repository hasn't changed
    if (lastRepoIdRef.current === selectedRepoId && hasInitializedRef.current) {
            return;
    }

    // Update the last repository ID
    lastRepoIdRef.current = selectedRepoId;

    // Fetch presets for the new repository
        fetchPresets(true);

    // Mark as initialized
    hasInitializedRef.current = true;

    // Cleanup function
    return () => {
      isMountedRef.current = false;
    };
  }, [selectedRepoId, fetchPresets]);

  // We've moved the reports check to the ReportPresetsPage component
  // This effect is no longer needed

  return {
    presets,
    setPresets,
    loading,
    error,
    reportsData,
    deletePresetId,
    showConfirmDelete,
    setDeletePresetId,
    setShowConfirmDelete,
    fetchPresets,
    createPreset,
    updatePreset,
    deletePreset,
    togglePresetActivation,
    generateReport,
  }
}
