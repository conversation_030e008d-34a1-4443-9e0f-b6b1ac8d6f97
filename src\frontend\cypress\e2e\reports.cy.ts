describe('Reports Feature', () => {
  beforeEach(() => {
    // Visit the application
    cy.visit('/')
    
    // Intercept API calls
    cy.intercept('GET', '/api/data/repositories').as('getRepositories')
    cy.intercept('GET', '/api/data/reports/presets').as('getPresets')
    cy.intercept('GET', '/api/data/reports').as('getReports')
    cy.intercept('POST', '/api/data/reports/presets').as('createPreset')
    cy.intercept('POST', '/api/data/reports/generate/*').as('generateReport')
    cy.intercept('DELETE', '/api/data/reports/*').as('deleteReport')
    
    // Wait for repositories to load
    cy.wait('@getRepositories')
    
    // Select the first repository in the dropdown
    cy.get('[data-test="repository-selector"]').click()
    cy.get('[data-test="repository-option"]').first().click()
  })
  
  it('should navigate to the Report Presets page', () => {
    // Navigate to report presets page
    cy.get('a[href="/report-presets"]').click()
    
    // Verify page title
    cy.contains('h1', 'Report Presets')
    
    // Wait for presets to load
    cy.wait('@getPresets')
  })
  
  it('should create a new report preset', () => {
    // Navigate to report presets page
    cy.get('a[href="/report-presets"]').click()
    cy.wait('@getPresets')
    
    // Click on create new preset button
    cy.contains('button', 'Create New Preset').click()
    
    // Fill the form
    cy.get('#name').type('Test Preset')
    cy.get('#description').type('This is a test preset for E2E testing')
    
    // Select report type
    cy.get('[id*="reportType"]').click()
    cy.contains('Users Only').click()
    
    // Select LOB
    cy.get('[id*="lob"]').click()
    cy.contains('li', 'Marketing').click()
    
    // Submit the form
    cy.contains('button', 'Create Preset').click()
    
    // Wait for API response
    cy.wait('@createPreset')
    
    // Verify success message
    cy.contains('Success')
    
    // Verify the preset appears in the list
    cy.contains('Test Preset')
  })
  
  it('should generate a report from a preset', () => {
    // Navigate to report presets page
    cy.get('a[href="/report-presets"]').click()
    cy.wait('@getPresets')
    
    // Click on generate for the first preset
    cy.contains('.card', 'Test Preset')
      .contains('button', 'Generate')
      .click()
    
    // Wait for report generation
    cy.wait('@generateReport')
    
    // Verify success message
    cy.contains('Success')
    
    // Verify we're redirected to reports page
    cy.url().should('include', '/reports')
    
    // Wait for reports to load
    cy.wait('@getReports')
    
    // Verify the new report appears in the list
    cy.contains('Test Preset')
  })
  
  it('should save a report preset from the groups page', () => {
    // Navigate to groups page
    cy.get('a[href="/groups"]').click()
    
    // Intercept groups API call
    cy.intercept('GET', '/api/data/repositories/*/groups').as('getGroups')
    cy.wait('@getGroups')
    
    // Apply filter for Marketing LOB
    cy.get('[data-test="filter-button"]').click()
    cy.contains('Marketing').click()
    cy.get('[data-test="apply-filter"]').click()
    
    // Click on save as preset
    cy.contains('button', 'Save as Preset').click()
    
    // Fill the form
    cy.get('#preset-name').type('Filtered Groups Preset')
    cy.get('#preset-description').type('Marketing groups preset created from filter')
    
    // Submit the form
    cy.contains('button', 'Save Preset').click()
    
    // Wait for API response
    cy.wait('@createPreset')
    
    // Verify success message
    cy.contains('Success')
  })
  
  it('should manage reports on the reports page', () => {
    // Navigate to reports page
    cy.get('a[href="/reports"]').click()
    
    // Wait for reports to load
    cy.wait('@getReports')
    
    // Search for a report
    cy.get('input[type="search"]').type('Test')
    
    // Verify the filtered list
    cy.contains('Test Preset')
    
    // Download a report
    cy.contains('tr', 'Test Preset')
      .contains('a', 'Download')
      .should('have.attr', 'href')
      .and('include', '/api/data/exports/download/')
    
    // Delete a report
    cy.contains('tr', 'Test Preset')
      .contains('button', 'Delete')
      .click()
    
    // Confirm deletion
    cy.contains('button', 'Delete').click()
    
    // Wait for delete API call
    cy.wait('@deleteReport')
    
    // Verify success message
    cy.contains('Success')
    
    // Verify report is removed
    cy.contains('Test Preset').should('not.exist')
  })
  
  it('should show recent reports on the dashboard', () => {
    // Navigate to dashboard
    cy.get('a[href="/"]').click()
    
    // Intercept reports API call
    cy.intercept('GET', '/api/data/reports').as('getReportsForDashboard')
    
    // Wait for reports to load
    cy.wait('@getReportsForDashboard')
    
    // Verify recent reports section exists
    cy.contains('h3', 'Recent Reports')
    
    // Verify quick access buttons
    cy.contains('button', 'Report Presets').click()
    cy.url().should('include', '/report-presets')
  })
})
