import { Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { PresetCard } from './PresetCard'
import { PresetsListProps } from './types'

export function PresetsList({
  presets,
  loading,
  error,
  onEdit,
  onView,
  onGenerate,
  onOpenHistory
}: PresetsListProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-500 mb-4">{error}</div>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    )
  }

  if (!presets || presets.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-muted-foreground mb-4">No report presets found</div>
        <Button>
          <Plus className="h-4 w-4 mr-2" /> Create New Preset
        </Button>
      </div>
    )
  }

  // Group presets by parentId or id (for parent presets)
  const groupedPresets = presets.reduce((acc, preset) => {
    const groupId = preset.parentId || preset.id;
    if (!acc[groupId!]) {
      acc[groupId!] = [];
    }
    acc[groupId!].push(preset);
    return acc;
  }, {} as Record<string, typeof presets>);

  // Sort each group by version (descending)
  Object.keys(groupedPresets).forEach(groupId => {
    groupedPresets[groupId].sort((a, b) => {
      const versionA = a.version || 0;
      const versionB = b.version || 0;
      return versionB - versionA;
    });
  });

  // Get the latest version of each preset group
  const latestVersions = Object.values(groupedPresets).map(group => group[0]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {latestVersions.map((preset) => {
        const groupId = preset.parentId || preset.id;
        const versions = groupedPresets[groupId!];
        const hasMultipleVersions = versions.length > 1;

        return (
          <div key={preset.id} className="relative">
            <PresetCard
              preset={preset}
              onEdit={onEdit}
              onView={onView}
              onGenerate={onGenerate}
              onOpenHistory={onOpenHistory}
            />
            {hasMultipleVersions && versions.length > 1 && (
              <div className="absolute -bottom-1 left-0 right-0 flex justify-center">
                <div className="h-1 w-[90%] bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  )
}
