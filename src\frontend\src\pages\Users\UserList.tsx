import { User } from './UserTypes';
import UserListItem from './UserListItem';

interface UserListProps {
  loading: boolean;
  dataReady: boolean;
  filteredUsers: User[];
  expandedRows: Record<string, boolean>;
  toggleRowExpansion: (userName: string) => void;
  setSearchQuery: (query: string) => void;
}

const UserList = ({
  loading,
  dataReady,
  filteredUsers,
  expandedRows,
  toggleRowExpansion,
  setSearchQuery
}: UserListProps) => {
  if (loading && !dataReady) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 mx-auto border-4 border-primary border-t-transparent rounded-full"></div>
          <p className="mt-2 text-gray-500">Loading users...</p>
        </div>
      </div>
    );
  }

  if (!dataReady || !filteredUsers || filteredUsers.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg w-full max-w-md">
          <p className="text-gray-500">No users found</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {filteredUsers.map((user, index) => {
        const userKey = `user-${index}-${user.name || Math.random().toString(36).substring(2, 9)}`;
        
        return (
          <UserListItem
            key={userKey}
            user={user}
            userKey={userKey}
            expandedRows={expandedRows}
            toggleRowExpansion={toggleRowExpansion}
            setSearchQuery={setSearchQuery}
          />
        );
      })}
    </div>
  );
};

export default UserList;
