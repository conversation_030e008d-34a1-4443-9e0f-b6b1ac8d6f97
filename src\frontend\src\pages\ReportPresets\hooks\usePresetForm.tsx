import { useState, useEffect, useCallback } from 'react'
import { PresetFormState, ReportPreset } from '../types'

export function usePresetForm(preset: ReportPreset | null = null) {
  // Form state
  const [formName, setFormName] = useState<string>('')
  const [formDescription, setFormDescription] = useState<string>('')
  const [formReportType, setFormReportType] = useState<'users' | 'groups' | 'both'>('both')
  const [formSearchQuery, setFormSearchQuery] = useState<string>('')
  const [formFlattenMembership, setFormFlattenMembership] = useState<boolean>(false)
  const [formSchedule, setFormSchedule] = useState<any>({ enabled: false, frequency: 'daily', hour: 0, minute: 0 })

  // Column selection state
  const [formGroupColumns, setFormGroupColumns] = useState<string[]>(['groupname', 'type', 'lob', 'description', 'members'])
  const [formUserColumns, setFormUserColumns] = useState<string[]>(['username', 'groups', 'lobs'])

  // Legacy form state (kept for backward compatibility)
  const [formLob, setFormLob] = useState<string>('all_lobs')
  const [formTypes, setFormTypes] = useState<string>('')
  const [formGroupIds, setFormGroupIds] = useState<string>('')
  const [formUserIds, setFormUserIds] = useState<string>('')

  // Reset form to default values
  const resetForm = useCallback(() => {
    setFormName('')
    setFormDescription('')
    setFormReportType('both')
    setFormSearchQuery('')
    setFormFlattenMembership(false)
    setFormSchedule({ enabled: false, frequency: 'daily', hour: 0, minute: 0 })
    setFormGroupColumns(['groupname', 'type', 'lob', 'description', 'members'])
    setFormUserColumns(['username', 'groups', 'lobs'])
    setFormLob('all_lobs')
    setFormTypes('')
    setFormGroupIds('')
    setFormUserIds('')
  }, [])

  // Initialize form with preset values if provided
  useEffect(() => {
    if (preset) {
      setFormName(preset.name || '')
      setFormDescription(preset.description || '')
      setFormReportType(preset.reportType || 'both')
      setFormSearchQuery(preset.searchQuery || '')
      setFormFlattenMembership(preset.flattenMembership || false)
      setFormSchedule(preset.schedule || { enabled: false, frequency: 'daily', hour: 0, minute: 0 })
      setFormGroupColumns(preset.groupColumns || ['groupname', 'type', 'lob', 'description', 'members'])
      setFormUserColumns(preset.userColumns || ['username', 'groups', 'lobs'])
      setFormLob(preset.lob || 'all_lobs')
      setFormTypes(preset.types || '')
      setFormGroupIds(preset.groupIds || '')
      setFormUserIds(preset.userIds || '')
    } else {
      resetForm()
    }
  }, [preset, resetForm])

  // Get form data as an object
  const getFormData = useCallback((): PresetFormState => {
    return {
      name: formName,
      description: formDescription,
      reportType: formReportType,
      searchQuery: formSearchQuery,
      flattenMembership: formFlattenMembership,
      schedule: formSchedule,
      groupColumns: formGroupColumns,
      userColumns: formUserColumns,
      lob: formLob,
      types: formTypes,
      groupIds: formGroupIds,
      userIds: formUserIds,
    }
  }, [
    formName,
    formDescription,
    formReportType,
    formSearchQuery,
    formFlattenMembership,
    formSchedule,
    formGroupColumns,
    formUserColumns,
    formLob,
    formTypes,
    formGroupIds,
    formUserIds,
  ])

  return {
    formName,
    setFormName,
    formDescription,
    setFormDescription,
    formReportType,
    setFormReportType,
    formSearchQuery,
    setFormSearchQuery,
    formFlattenMembership,
    setFormFlattenMembership,
    formSchedule,
    setFormSchedule,
    formGroupColumns,
    setFormGroupColumns,
    formUserColumns,
    setFormUserColumns,
    formLob,
    setFormLob,
    formTypes,
    setFormTypes,
    formGroupIds,
    setFormGroupIds,
    formUserIds,
    setFormUserIds,
    resetForm,
    getFormData,
  }
}
