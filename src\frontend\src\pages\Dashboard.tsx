import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { useRepository } from '@/context/RepositoryContext'
import { apiClient, Report } from '@/api/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Database, FileText, Download, ArrowRight } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'
import RepositoryStatusCard from '@/components/dashboard/RepositoryStatus'
import IndexStatusCard from '@/components/dashboard/IndexStatus'
import RepositoryStatsCard from '@/components/dashboard/RepositoryStats'

const Dashboard = () => {
  const navigate = useNavigate()
  const { selectedRepoId } = useRepository()
  const [recentReports, setRecentReports] = useState<Report[]>([])
  const [loadingReports, setLoadingReports] = useState(true)

  // Define fetchRecentReports before using it in useEffect
  const fetchRecentReports = useCallback(async () => {
    setLoadingReports(true)

    try {
      // Only attempt to fetch reports if we have a selected repository
      if (!selectedRepoId) {
        setRecentReports([]);
        return;
      }

      // Pass the repository ID to filter reports
      const reports = await apiClient.data.getReports(selectedRepoId)

      // Check if reports is null or undefined before sorting
      if (!reports) {
        setRecentReports([]);
        return;
      }

      // Get the most recent 3 reports
      const sorted = reports.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ).slice(0, 3)

      setRecentReports(sorted)
    } catch (err) {
      console.error("Failed to fetch recent reports:", err)
      // Set empty array on error to prevent further issues
      setRecentReports([])
    } finally {
      setLoadingReports(false)
    }
  }, [selectedRepoId])

  // Fetch recent reports
  useEffect(() => {
    fetchRecentReports()
  }, [fetchRecentReports]) // Re-fetch when repository changes (via fetchRecentReports dependencies)

  // Format timestamp to readable date
  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Dashboard</h1>
      </div>

      {/* Repository Status, Stats, and Index Status Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <RepositoryStatusCard />
        <RepositoryStatsCard />
        <IndexStatusCard />
      </div>

      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-1">
        <Card className="col-span-1">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle>Recent Reports</CardTitle>
              <Button variant="ghost" size="sm" onClick={() => navigate('/reports')} className="gap-1">
                View All <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
            <CardDescription>Recently generated reports and exports</CardDescription>
          </CardHeader>
          <CardContent>
            {loadingReports ? (
              <div className="space-y-2">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : recentReports.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="mx-auto h-8 w-8 mb-2 opacity-50" />
                <p>No reports generated yet</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => navigate('/report-presets')}
                >
                  Create Your First Report
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                {recentReports.map(report => (
                  <div
                    key={report.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 text-gray-500 mr-3" />
                      <div>
                        <p className="font-medium">{report.filename}</p>
                        <p className="text-sm text-muted-foreground">
                          {report.presetName ? `From preset: ${report.presetName}` : 'Manual export'} • {formatFileSize(report.size)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        {formatDate(report.createdAt)}
                      </span>
                      <Button
                        variant="ghost"
                        size="icon"
                        asChild
                      >
                        <a href={report.downloadUrl} download>
                          <Download className="h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0">
            <div className="flex justify-between w-full">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate('/report-presets')}
                className="gap-1"
              >
                <Database className="h-4 w-4" />
                Manage Presets
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={() => navigate('/reports')}
                className="gap-1"
              >
                <FileText className="h-4 w-4" />
                View All Reports
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}

export default Dashboard
