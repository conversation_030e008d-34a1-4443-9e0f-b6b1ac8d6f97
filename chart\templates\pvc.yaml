{{- if .Values.persistence.enabled }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "adgitops-ui.fullname" . }}-configs
  labels:
    {{- include "adgitops-ui.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.configs.accessMode }}
  {{- if .Values.persistence.configs.storageClass }}
  storageClassName: {{ .Values.persistence.configs.storageClass }}
  {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistence.configs.size }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "adgitops-ui.fullname" . }}-data
  labels:
    {{- include "adgitops-ui.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.data.accessMode }}
  {{- if .Values.persistence.data.storageClass }}
  storageClassName: {{ .Values.persistence.data.storageClass }}
  {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistence.data.size }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "adgitops-ui.fullname" . }}-reports
  labels:
    {{- include "adgitops-ui.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.reports.accessMode }}
  {{- if .Values.persistence.reports.storageClass }}
  storageClassName: {{ .Values.persistence.reports.storageClass }}
  {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistence.reports.size }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "adgitops-ui.fullname" . }}-repos
  labels:
    {{- include "adgitops-ui.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.repos.accessMode }}
  {{- if .Values.persistence.repos.storageClass }}
  storageClassName: {{ .Values.persistence.repos.storageClass }}
  {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistence.repos.size }}
{{- end }}
