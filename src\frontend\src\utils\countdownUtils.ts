/**
 * Utility functions for countdown timers
 */

/**
 * Calculate the time remaining until a target date
 * @param targetDate The target date to count down to
 * @returns Object containing days, hours, minutes, seconds remaining, and whether the date is in the past
 */
export function getTimeRemaining(targetDate: string | Date): {
  total: number;
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  isPast: boolean;
} {
  const target = typeof targetDate === 'string' ? new Date(targetDate) : targetDate;
  const now = new Date();
  
  // Calculate total milliseconds remaining
  const total = target.getTime() - now.getTime();
  const isPast = total <= 0;
  
  // Convert to time units
  const seconds = Math.floor((Math.abs(total) / 1000) % 60);
  const minutes = Math.floor((Math.abs(total) / 1000 / 60) % 60);
  const hours = Math.floor((Math.abs(total) / (1000 * 60 * 60)) % 24);
  const days = Math.floor(Math.abs(total) / (1000 * 60 * 60 * 24));
  
  return {
    total,
    days,
    hours,
    minutes,
    seconds,
    isPast
  };
}

/**
 * Format a countdown in a human-readable way
 * @param timeRemaining The time remaining object from getTimeRemaining
 * @returns A formatted string representation of the countdown
 */
export function formatCountdown(timeRemaining: ReturnType<typeof getTimeRemaining>): string {
  const { days, hours, minutes, seconds, isPast } = timeRemaining;
  
  // For times in the past
  if (isPast) {
    if (days > 0) {
      return `${days}d ${hours}h ago`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m ago`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s ago`;
    } else {
      return `${seconds}s ago`;
    }
  }
  
  // For future times
  if (days > 0) {
    return `in ${days}d ${hours}h`;
  } else if (hours > 0) {
    return `in ${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `in ${minutes}m ${seconds}s`;
  } else {
    return `in ${seconds}s`;
  }
}

/**
 * Format a schedule frequency in a human-readable way
 * @param schedule The schedule configuration
 * @returns A formatted string representation of the schedule frequency
 */
export function formatScheduleFrequency(schedule: any): string {
  if (!schedule || !schedule.enabled) {
    return 'Not scheduled';
  }
  
  switch (schedule.frequency) {
    case 'interval':
      const hours = schedule.intervalHours || 0;
      const minutes = schedule.intervalMinutes || 0;
      
      if (hours > 0 && minutes > 0) {
        return `Every ${hours}h ${minutes}m`;
      } else if (hours > 0) {
        return `Every ${hours} hour${hours !== 1 ? 's' : ''}`;
      } else {
        return `Every ${minutes} minute${minutes !== 1 ? 's' : ''}`;
      }
      
    case 'daily':
      return `Daily at ${String(schedule.hour).padStart(2, '0')}:${String(schedule.minute).padStart(2, '0')}`;
      
    case 'weekly': {
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const day = days[schedule.dayOfWeek || 0];
      return `Weekly on ${day} at ${String(schedule.hour).padStart(2, '0')}:${String(schedule.minute).padStart(2, '0')}`;
    }
      
    case 'monthly':
      return `Monthly on day ${schedule.dayOfMonth || 1} at ${String(schedule.hour).padStart(2, '0')}:${String(schedule.minute).padStart(2, '0')}`;
      
    default:
      return 'Scheduled';
  }
}
