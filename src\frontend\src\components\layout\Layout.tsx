import { Outlet, Link, useLocation } from 'react-router-dom'
import { <PERSON><PERSON><PERSON>, Users, FolderGit, Settings, FileText, Database } from 'lucide-react'
import { cn } from '@/lib/utils'
import RepositorySelector from '@/components/repository/RepositorySelector'
import { useRepository } from '@/context/RepositoryContext'
import { Skeleton } from '@/components/ui/skeleton'
import { createContext, useContext, useState, ReactNode } from 'react'

// Create a context to manage page filters
export const FiltersContext = createContext({
  setFilters: (_filters: ReactNode) => {},
})

// Hook to use the filters context
export const useFilters = () => useContext(FiltersContext)

// Create a context for the current page info
export const PageContext = createContext({
  title: '',
  path: ''
})

// Hook to use the page context
export const usePageContext = () => useContext(PageContext)

const navItems = [
  { path: '/', label: 'Dashboard', icon: Bar<PERSON><PERSON> },
  { path: '/groups', label: 'Groups', icon: FolderGit },
  { path: '/users', label: 'Users', icon: Users },
  { path: '/report-presets', label: 'Report Presets', icon: Database },
  { path: '/reports', label: 'Reports', icon: FileText },
  { path: '/settings', label: 'Settings', icon: Settings },
]

const Layout = () => {
  const location = useLocation()
  const { isLoading } = useRepository()
  const [pageFilters, setPageFilters] = useState<ReactNode>(null)

  // Get the current page info
  const currentPage = navItems.find(item => item.path === location.pathname) || { label: 'Dashboard', path: '/' }
  const pageContextValue = {
    title: currentPage.label,
    path: currentPage.path
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r border-gray-200 shadow-sm">
        <div className="flex flex-col h-full">
          <div className="flex items-center h-16 px-6 border-b border-gray-200">
            <h1 className="text-xl font-bold text-blue-600">ADGitOps UI</h1>
          </div>
          <nav className="flex-1 p-4">
            <ul className="space-y-2">
              {navItems.map((item) => (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={cn(
                      "flex items-center px-4 py-2 rounded-md text-sm transition-colors",
                      location.pathname === item.path
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-blue-50 hover:text-blue-600"
                    )}
                  >
                    <item.icon className="mr-2 h-5 w-5" />
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
          <div className="p-4 text-xs text-gray-500 border-t border-gray-200">
            <div>ADGitOps Extract</div>
            <div>Version 0.1.0</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="h-16 border-b border-gray-200 bg-white shadow-sm flex items-center px-6">
          <div className="flex items-center flex-shrink-0 mr-4">
            <h2 className="text-lg font-medium text-gray-800">
              {currentPage.label}
            </h2>
          </div>

          {/* Page Filters Slot - Taking all available space */}
          <div className="flex-1 flex">
            {pageFilters}
          </div>

          {/* Repository selector */}
          {location.pathname !== '/settings' && (
            <div className="flex items-center flex-shrink-0 ml-4">
              <span className="mr-2 text-sm text-gray-500">Repository:</span>
              {isLoading ? (
                <Skeleton className="h-9 w-[200px]" />
              ) : (
                <RepositorySelector />
              )}
            </div>
          )}
        </header>

        <main className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-auto p-6">
            <FiltersContext.Provider value={{ setFilters: setPageFilters }}>
              <PageContext.Provider value={pageContextValue}>
                <Outlet />
              </PageContext.Provider>
            </FiltersContext.Provider>
          </div>
        </main>
      </div>
    </div>
  )
}

export default Layout
