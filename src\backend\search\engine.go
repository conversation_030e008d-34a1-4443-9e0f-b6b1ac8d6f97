package search

import (
	"adgitops-ui/src/backend/models"
	"context"
	"strings"
)

// Engine is responsible for executing search queries
type Engine struct {
	parser *Parser
}

// NewEngine creates a new search engine
func NewEngine() *Engine {
	return &Engine{
		parser: NewParser(),
	}
}

// SearchGroups searches for groups matching the query
func (e *Engine) SearchGroups(ctx context.Context, query string, groups []models.Group) ([]models.Group, error) {
	// Parse the query
	node, err := e.parser.ParseQuery(query)
	if err != nil {
		return nil, err
	}

	// If no query, return all groups
	if node == nil {
		return groups, nil
	}

	// Filter groups
	var result []models.Group
	for _, group := range groups {
		if e.matchesGroup(node, group) {
			result = append(result, group)
		}
	}

	return result, nil
}

// SearchUsers searches for users matching the query
func (e *Engine) SearchUsers(ctx context.Context, query string, users []models.User) ([]models.User, error) {
	// Parse the query
	node, err := e.parser.ParseQuery(query)
	if err != nil {
		return nil, err
	}

	// If no query, return all users
	if node == nil {
		return users, nil
	}

	// Filter users
	var result []models.User
	for _, user := range users {
		if e.matchesUser(node, user) {
			result = append(result, user)
		}
	}

	return result, nil
}

// matchesGroup checks if a group matches the search criteria
func (e *Engine) matchesGroup(node *SearchNode, group models.Group) bool {
	if node == nil {
		return true
	}

	switch node.Type {
	case NodeTerm:
		return e.matchesGroupTerm(node, group)
	case NodeAnd:
		return e.matchesGroup(node.Left, group) && e.matchesGroup(node.Right, group)
	case NodeOr:
		return e.matchesGroup(node.Left, group) || e.matchesGroup(node.Right, group)
	case NodeNot:
		return !e.matchesGroup(node.Right, group)
	}

	return false
}

// matchesGroupTerm checks if a group matches a specific term
// This function implements the search behavior as described in the search-behaviour.md documentation
func (e *Engine) matchesGroupTerm(node *SearchNode, group models.Group) bool {
	// For simple keyword searches with _any field, we want to match partial words in all fields
	// as per the documentation for groups
	if node.Field == "_any" && node.Operator == "~" {
		// Check if any field contains the search term
		value := strings.ToLower(node.Value)
		groupname := strings.ToLower(group.Groupname)
		groupType := strings.ToLower(group.Type)
		description := strings.ToLower(group.Description)
		lob := strings.ToLower(group.Lob)

		// Check if any field contains the search term
		if strings.Contains(groupname, value) ||
			strings.Contains(groupType, value) ||
			strings.Contains(description, value) ||
			strings.Contains(lob, value) {
			return true
		}

		// Check members
		for _, member := range group.Members {
			if strings.Contains(strings.ToLower(member.Name), value) {
				return true
			}
		}
	}

	// Handle field-specific searches
	switch node.Field {
	// Field-specific searches for name, groupname, and lob fields are treated as exact matches by default
	case "groupname", "name":
		// For groupname field, use exact match by default as per the documentation
		if node.Operator == "" || node.Operator == "=" {
			// Exact match
			return strings.ToLower(group.Groupname) == strings.ToLower(node.Value)
		} else {
			// Use the generic matchesValue function for all other operators
			return e.matchesValue(node.Operator, strings.ToLower(group.Groupname), strings.ToLower(node.Value))
		}
	case "type":
		return e.matchesValue(node.Operator, strings.ToLower(group.Type), strings.ToLower(node.Value))
	case "description":
		return e.matchesValue(node.Operator, strings.ToLower(group.Description), strings.ToLower(node.Value))
	case "lob":
		// For lob field, use exact match by default as per the documentation
		if node.Operator == "" || node.Operator == "=" {
			// Exact match
			return strings.ToLower(group.Lob) == strings.ToLower(node.Value)
		} else {
			// Use the generic matchesValue function for all other operators
			return e.matchesValue(node.Operator, strings.ToLower(group.Lob), strings.ToLower(node.Value))
		}
	case "members":
		// Generic handling for member searches with wildcards and regular searches
		for _, member := range group.Members {
			if e.matchesValue(node.Operator, strings.ToLower(member.Name), strings.ToLower(node.Value)) {
				return true
			}
		}
		return false
	case "repoid":
		// For repository ID, we always return true since the repository filtering is done at a higher level
		return true
	case "_any":
		// Check all fields
		// For simple keyword searches, we want to match partial words in all fields
		// This is especially important for the "admin" test case
		groupname := strings.ToLower(group.Groupname)
		groupType := strings.ToLower(group.Type)
		description := strings.ToLower(group.Description)
		lob := strings.ToLower(group.Lob)
		value := strings.ToLower(node.Value)

		// Check if any field contains the search term
		if strings.Contains(groupname, value) ||
			strings.Contains(groupType, value) ||
			strings.Contains(description, value) ||
			strings.Contains(lob, value) {
			return true
		}

		// Check members
		for _, member := range group.Members {
			if strings.Contains(strings.ToLower(member.Name), value) {
				return true
			}
		}
		return false
	case "_all":
		// Special case for matching all records (used with NOT operator)
		return true
	default:
		// Unknown field, check all fields
		return e.matchesGroupTerm(&SearchNode{
			Type:     NodeTerm,
			Field:    "_any",
			Operator: node.Operator,
			Value:    node.Value,
		}, group)
	}
}

// matchesUser checks if a user matches the search criteria
func (e *Engine) matchesUser(node *SearchNode, user models.User) bool {
	if node == nil {
		return true
	}

	switch node.Type {
	case NodeTerm:
		return e.matchesUserTerm(node, user)
	case NodeAnd:
		return e.matchesUser(node.Left, user) && e.matchesUser(node.Right, user)
	case NodeOr:
		return e.matchesUser(node.Left, user) || e.matchesUser(node.Right, user)
	case NodeNot:
		return !e.matchesUser(node.Right, user)
	}

	return false
}

// matchesUserTerm checks if a user matches a specific term
// This function implements the search behavior as described in the search-behaviour.md documentation
func (e *Engine) matchesUserTerm(node *SearchNode, user models.User) bool {
	// For simple keyword searches with _any field
	if node.Field == "_any" {
		// For simple keyword search, we want exact match on name as per the documentation
		// "Simple keyword search: Typing a word without any special syntax will search for that word across all fields."
		// "This should be exact match by default (e.g., "admin" shouldn't match "administrator")"
		value := strings.ToLower(node.Value)
		name := strings.ToLower(user.Name)

		// Check if name exactly matches the search term
		if name == value {
			return true
		}

		// We don't check groups or LOBs for simple keyword searches
		// as per the documentation which states simple keyword searches should be exact matches
		return false
	}

	// Handle field-specific searches
	switch node.Field {
	case "name", "username":
		// For name field, use exact match by default as per the documentation
		// "Name field: The name field is treated as an exact match field by default."
		if node.Operator == "" || node.Operator == "=" {
			// Exact match
			return strings.ToLower(user.Name) == strings.ToLower(node.Value)
		} else {
			// Use the generic matchesValue function for all other operators
			return e.matchesValue(node.Operator, strings.ToLower(user.Name), strings.ToLower(node.Value))
		}
	case "groups", "groupname":
		// Check if any group matches
		for _, group := range user.Groups {
			if e.matchesValue(node.Operator, strings.ToLower(group), strings.ToLower(node.Value)) {
				return true
			}
		}
		return false
	case "lob", "lobs":
		// For lob field, use exact match by default as per the documentation
		// "LOB field: The lob field (for groups) and lobs field (for users) are treated as exact match fields by default."
		if node.Operator == "" || node.Operator == "=" {
			// Check if any LOB exactly matches
			for _, lob := range user.LOBs {
				if strings.ToLower(lob) == strings.ToLower(node.Value) {
					return true
				}
			}
			return false
		} else {
			// For other operators, check if any LOB matches
			for _, lob := range user.LOBs {
				if e.matchesValue(node.Operator, strings.ToLower(lob), strings.ToLower(node.Value)) {
					return true
				}
			}
			return false
		}
	case "repoid":
		// For repository ID, we always return true since the repository filtering is done at a higher level
		return true
	case "_all":
		// Special case for matching all records (used with NOT operator)
		return true
	default:
		// Unknown field, check all fields
		return e.matchesUserTerm(&SearchNode{
			Type:     NodeTerm,
			Field:    "_any",
			Operator: node.Operator,
			Value:    node.Value,
		}, user)
	}
}

// matchesValue checks if a value matches the search criteria
// This function implements the search behavior as described in the search-behaviour.md documentation
func (e *Engine) matchesValue(operator, field, value string) bool {
	// Convert to lowercase for case-insensitive matching
	fieldLower := strings.ToLower(field)
	valueLower := strings.ToLower(value)

	// Handle empty values
	if fieldLower == "" || valueLower == "" {
		return false
	}

	var result bool
	switch operator {
	case "=":
		// Exact match - used for field:value syntax
		result = fieldLower == valueLower
	case "~":
		// Contains match - used for field:~value syntax
		// This is equivalent to field:*value* as per the documentation
		result = strings.Contains(fieldLower, valueLower)
	case "^":
		// Prefix match - used for field:value* syntax
		result = strings.HasPrefix(fieldLower, valueLower)
	case "$":
		// Suffix match - used for field:*value syntax
		result = strings.HasSuffix(fieldLower, valueLower)
	default:
		// Default to exact match as per the documentation
		// "Field-specific searches for name, groupname, and lob fields are treated as exact matches by default."
		result = fieldLower == valueLower
	}

	return result
}

// ParseQueryToMap parses a search query into a map format for backward compatibility
func (e *Engine) ParseQueryToMap(query string) (map[string][]string, error) {
	return e.parser.ParseQueryToMap(query)
}
