/**
 * Application configuration
 * This file provides configuration settings that can be imported throughout the application
 */

// API configuration
export const API_CONFIG = {
  BASE_URL: '/api',
  TIMEOUT: 30000, // 30 seconds
};

// Path configuration - handles OS differences
export const PATH_CONFIG = {
  // Use forward slashes for all paths, which work on both Windows and Unix
  SEPARATOR: '/',
  normalize: (path: string) => path.replace(/\\/g, '/'),
};

// Default application settings
export const APP_CONFIG = {
  APP_NAME: 'ADGitOps Extract',
  DEFAULT_POLL_FREQUENCY: 300, // 5 minutes
};

export default {
  API: API_CONFIG,
  PATH: PATH_CONFIG,
  APP: APP_CONFIG,
};
