import { HelpCircle } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const ReportPresetsHelp = () => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          type="button"
          className="inline-flex items-center text-gray-500 hover:text-gray-700"
          aria-label="Help with report presets"
        >
          <HelpCircle className="h-4 w-4" />
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-[80vw] max-w-[1200px]" side="bottom" align="center" sideOffset={5} avoidCollisions>
        <div className="p-2">
          <h3 className="text-2xs font-bold mb-0.5 border-b pb-0.5 text-blue-600">How Report Presets Work</h3>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="flex flex-col">
              <h3 className="font-medium text-sm mb-2">1. Create a Preset</h3>
              <p className="text-8xs text-muted-foreground mb-2">A preset is a saved configuration for generating reports.</p>
              <ul className="list-disc pl-5 space-y-1 text-8xs">
                <li>Click the <strong>"Create New Preset"</strong> button</li>
                <li>Give your preset a meaningful name</li>
                <li>Choose what to include: Users, Groups, or Both</li>
                <li>Use the search query to filter data (same syntax as Groups/Users pages)</li>
                <li>Select which columns to include in the report</li>
              </ul>
            </div>

            <div className="flex flex-col">
              <h3 className="font-medium text-sm mb-2">2. Generate a Report</h3>
              <p className="text-8xs text-muted-foreground mb-2">Use your preset to quickly generate reports.</p>
              <ul className="list-disc pl-5 space-y-1 text-8xs">
                <li>Select a repository from the dropdown</li>
                <li>Find the preset you want to use</li>
                <li>Click the <strong>"Generate"</strong> button</li>
                <li>The report will be created and you'll be taken to the Reports page</li>
              </ul>
            </div>

            <div className="flex flex-col">
              <h3 className="font-medium text-sm mb-2">3. Manage Your Presets</h3>
              <p className="text-8xs text-muted-foreground mb-2">Edit or delete presets as needed.</p>
              <ul className="list-disc pl-5 space-y-1 text-8xs">
                <li>Use the <strong>"Edit"</strong> button to modify a preset</li>
                <li>Use the <strong>"Delete"</strong> button to remove a preset</li>
                <li>Presets are saved for all users of the system</li>
                <li>Reports are listed on the separate Reports page</li>
              </ul>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ReportPresetsHelp;
