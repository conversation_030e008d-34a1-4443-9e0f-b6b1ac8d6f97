import React, { useState, useEffect } from 'react';
import SearchInputWithSuggestions from '@/components/SearchInputWithSuggestions';
import { Group } from './GroupTypes';

interface GroupSearchSuggestionsProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  onSearch: (value: string) => void;
  placeholder?: string;
  className?: string;
  searchInputRef: React.RefObject<HTMLInputElement>;
  groups: Group[];
  lobs: string[];
}

const GroupSearchSuggestions: React.FC<GroupSearchSuggestionsProps> = ({
  inputValue,
  setInputValue,
  onSearch,
  placeholder = 'Search groups...',
  className = '',
  searchInputRef,
  groups,
  lobs
}) => {
  // Extract unique group types from the groups
  const [groupTypes, setGroupTypes] = useState<string[]>([]);

  // Extract unique member names from all groups
  const [memberNames, setMemberNames] = useState<string[]>([]);

  // Extract group names for suggestions
  const [groupNames, setGroupNames] = useState<string[]>([]);

  // Process groups to extract suggestion data
  useEffect(() => {
    if (!groups || groups.length === 0) return;

    // Extract unique group types
    const types = new Set<string>();
    // Extract unique member names
    const members = new Set<string>();
    // Extract group names
    const names = new Set<string>();

    groups.forEach(group => {
      // Add group type if it exists
      if (group.Type) {
        types.add(group.Type.toLowerCase());
      }

      // Add group name
      if (group.Groupname) {
        names.add(group.Groupname);
      }

      // Process members
      if (group.Members) {
        if (Array.isArray(group.Members)) {
          group.Members.forEach(member => {
            if (typeof member === 'string') {
              members.add(member);
            } else if (typeof member === 'object' && member !== null && 'name' in member) {
              members.add(member.name as string);
            }
          });
        } else if (typeof group.Members === 'object' && group.Members !== null) {
          Object.keys(group.Members).forEach(memberName => {
            members.add(memberName);
          });
        }
      }
    });

    setGroupTypes(Array.from(types).sort());
    setMemberNames(Array.from(members).sort());
    setGroupNames(Array.from(names).sort());
  }, [groups]);

  // Create suggestions array for the SearchInputWithSuggestions component
  const suggestions = [
    // Filter type suggestions
    { type: 'groupname', value: '', display: 'groupname:' },
    { type: 'type', value: '', display: 'type:' },
    { type: 'lob', value: '', display: 'lob:' },
    { type: 'members', value: '', display: 'members:' },
    { type: 'description', value: '', display: 'description:' },

    // LOB value suggestions
    ...lobs.map(lob => ({
      type: 'lob',
      value: lob,
      display: lob
    })),

    // Group type value suggestions
    ...groupTypes.map(type => ({
      type: 'type',
      value: type,
      display: type
    })),

    // Group name value suggestions
    ...groupNames.map(name => ({
      type: 'groupname',
      value: name,
      display: name
    })),

    // Member name value suggestions
    ...memberNames.slice(0, 100).map(name => ({
      type: 'members',
      value: name,
      display: name
    }))
  ];

  return (
    <SearchInputWithSuggestions
      ref={searchInputRef}
      placeholder={placeholder}
      initialValue={inputValue}
      onChange={setInputValue}
      onSearch={onSearch}
      className={className}
      suggestions={suggestions}
      autoFocus={false}
    />
  );
};

export default GroupSearchSuggestions;
