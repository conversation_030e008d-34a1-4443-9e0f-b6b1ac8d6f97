/**
 * Utility functions for working with schedules
 */
import { ScheduleConfig } from '@/api/client';

/**
 * Calculate the estimated next run time based on the schedule configuration
 * This is a frontend implementation similar to the backend calculateNextRun function
 * @param schedule The schedule configuration
 * @returns The estimated next run time as a Date object
 */
export function calculateEstimatedNextRun(schedule: ScheduleConfig): Date {
  // If scheduling is not enabled, return current time
  if (!schedule.enabled) {
    return new Date();
  }

  const now = new Date();

  switch (schedule.frequency) {
    case 'interval': {
      // For interval, add the specified hours and minutes to the current time
      let intervalHours = schedule.intervalHours || 0;
      let intervalMinutes = schedule.intervalMinutes || 0;

      // Ensure we don't have a zero interval (both hours and minutes are 0)
      if (intervalHours === 0 && intervalMinutes === 0) {
        intervalMinutes = 30; // Default to 30 minutes if both are 0
      }

      const intervalMs = (intervalHours * 60 * 60 * 1000) + (intervalMinutes * 60 * 1000);

      // Calculate next run time by adding the interval to the current time
      return new Date(now.getTime() + intervalMs);
    }

    case 'daily': {
      // For daily, start with today at the scheduled time
      const next = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        schedule.hour,
        schedule.minute,
        0,
        0
      );

      // If the calculated time is in the past, move to tomorrow
      if (next.getTime() < now.getTime()) {
        next.setDate(next.getDate() + 1);
      }

      return next;
    }

    case 'weekly': {
      // For weekly, find the next occurrence of the scheduled day
      const targetDay = schedule.dayOfWeek || 0;
      const currentDay = now.getDay();

      // Calculate days until next occurrence
      let daysUntil = targetDay - currentDay;
      if (daysUntil <= 0) {
        daysUntil += 7; // Move to next week if target day is today or earlier in the week
      }

      // Create the next run date
      const next = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        schedule.hour,
        schedule.minute,
        0,
        0
      );
      next.setDate(next.getDate() + daysUntil);

      // If the calculated time is in the past (same day but earlier hour), move to next week
      if (next.getDate() === now.getDate() && next.getTime() < now.getTime()) {
        next.setDate(next.getDate() + 7);
      }

      return next;
    }

    case 'monthly': {
      // For monthly, set to the target day of the current month
      const targetDay = schedule.dayOfMonth || 1;
      const currentDay = now.getDate();

      // Start with the target day in the current month
      const next = new Date(
        now.getFullYear(),
        now.getMonth(),
        targetDay,
        schedule.hour,
        schedule.minute,
        0,
        0
      );

      // If the target day is already past in this month, move to next month
      if (targetDay < currentDay || (targetDay === currentDay && next.getTime() < now.getTime())) {
        next.setMonth(next.getMonth() + 1);
      }

      // Handle month length differences (e.g., February doesn't have 31 days)
      // If the day got adjusted due to month length, use the last day of the month
      if (next.getDate() !== targetDay) {
        // Go to the first day of the next month
        next.setMonth(next.getMonth() + 1);
        next.setDate(0); // Set to the last day of the previous month (i.e., the last day of the target month)
      }

      return next;
    }

    default:
      // Default: return current time if frequency is not recognized
      return now;
  }
}

/**
 * Format a date for display
 * @param date The date to format
 * @returns Formatted date string
 */
export function formatScheduleDate(date: Date): string {
  return date.toLocaleString();
}
