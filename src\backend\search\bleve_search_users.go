package search

import (
	"adgitops-ui/src/backend/models"
	"context"
	"fmt"
	"strings"
)

// SearchUsers searches for users matching the query
// This function implements the search behavior as described in the search-behaviour.md documentation
func (s *BleveSearchService) SearchUsers(ctx context.Context, query string, users []models.User) ([]models.User, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.initialized {
		return nil, fmt.Errorf("search service not initialized")
	}

	// If the query is empty or _all:true, return all users
	if strings.TrimSpace(query) == "" || strings.TrimSpace(query) == "_all:true" {
		return users, nil
	}

	// Debug output
	fmt.Printf("SearchUsers: query=%s\n", query)

	// Handle simple keyword search (no special syntax)
	if IsSimpleKeywordSearch(query) {
		// For simple keyword searches, we want exact match on name as per the documentation
		var results []models.User
		value := strings.ToLower(query)

		for _, user := range users {
			// Check if name matches the search term exactly
			name := strings.ToLower(user.Name)

			// For simple keyword search, we want exact match on name
			if name == value {
				results = append(results, user)
				continue
			}

			// We don't check groups or LOBs for simple keyword searches
			// as per the documentation which states simple keyword searches should be exact matches
		}

		// Debug output
		fmt.Printf("SearchUsers: simple keyword search returned %d results\n", len(results))
		for _, user := range results {
			fmt.Printf("SearchUsers: simple keyword result: %s\n", user.Name)
		}

		return results, nil
	}

	// Fix parentheses in the query if needed
	query = FixParentheses(query)

	// Create a new search engine
	engine := NewEngine()

	// Clean the query by removing type filters
	cleanQuery := CleanQuery(query, "user")

	// Use the generic search engine for all queries
	fmt.Printf("SearchUsers: using generic engine with query=%s\n", cleanQuery)

	// Use the engine to search
	filteredResults, err := engine.SearchUsers(ctx, cleanQuery, users)
	if err != nil {
		// Log the error for debugging
		fmt.Printf("SearchUsers: error searching users: %v\n", err)
		// Return the original error to preserve the error message
		return nil, err
	}

	// Debug output
	fmt.Printf("SearchUsers: engine returned %d results\n", len(filteredResults))
	for _, user := range filteredResults {
		fmt.Printf("SearchUsers: engine result: %s\n", user.Name)
	}

	return filteredResults, nil
}
