apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "adgitops-ui.fullname" . }}
  labels:
    {{- include "adgitops-ui.labels" . | nindent 4 }}
spec:
  # Force replicas to 1 as the application is not designed for horizontal scaling
  # See README.md for more details on why multiple replicas are not supported
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "adgitops-ui.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "adgitops-ui.selectorLabels" . | nindent 8 }}
    spec:
      containers:
        - name: {{ include "adgitops-ui.name" . }}
          image: "{{ index .Values.runtime.image.repository }}:{{ index .Values.runtime.image.tag }}"
          imagePullPolicy: {{ index .Values.runtime.pullPolicy }}
          args:
            - "--port"
            - "{{ .Values.app.port }}"
            - "--reports-dir"
            - "/app/reports"
            - "--repos-dir"
            - "/app/repos"
            - "--data-dir"
            - "/app/data"
            - "--static-dir"
            - "/app/static"
          ports:
            - name: http
              containerPort: {{ .Values.app.port }}
              protocol: TCP
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /health
              port: {{ .Values.service.port }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}

          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /health
              port: {{ .Values.service.port }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: configs-volume
              mountPath: /app/configs
            - name: data-volume
              mountPath: /app/data
            - name: reports-volume
              mountPath: /app/reports
            - name: repos-volume
              mountPath: /app/repos
      volumes:
        - name: configs-volume
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "adgitops-ui.fullname" . }}-configs
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: data-volume
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "adgitops-ui.fullname" . }}-data
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: reports-volume
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "adgitops-ui.fullname" . }}-reports
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: repos-volume
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "adgitops-ui.fullname" . }}-repos
          {{- else }}
          emptyDir: {}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
