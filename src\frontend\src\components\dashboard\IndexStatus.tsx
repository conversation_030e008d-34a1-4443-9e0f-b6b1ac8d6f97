import { useState, useEffect } from 'react';
import { RefreshCw, Database, CheckCircle, AlertCircle, Search, FileText, Users, FolderClosed, Info } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { apiClient } from '@/api/client';
import { useRepository } from '@/context/RepositoryContext';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface IndexStatusCardProps {
  repoId?: string; // Optional: If provided, will override the context repository
}

interface IndexStats {
  doc_count: number;
  index_size: number;
  update_time: string;
  index_path: string;
  initialized: boolean;
  doc_details?: DocumentDetails;
}

interface DocumentDetails {
  groups?: number;
  users?: number;
  files?: number;
  repositories?: string[];
}

const IndexStatusCard = ({ repoId }: IndexStatusCardProps = {}) => {
  const [stats, setStats] = useState<IndexStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [reindexing, setReindexing] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const { selectedRepoId, isLoading: contextLoading } = useRepository();
  const { toast } = useToast();

  // Use provided repoId or fall back to selected repo from context
  const currentRepoId = repoId || selectedRepoId;

  // Function to fetch index statistics
  const fetchStats = async () => {
    if (!currentRepoId) {
      setStats(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const response = await apiClient.search.getStats(currentRepoId);

      // Get document details from the response or estimate them
      const docDetails: DocumentDetails = {
        groups: response.group_count || Math.floor(response.doc_count / 2) || 0,
        users: response.user_count || Math.ceil(response.doc_count / 2) || 0,
        files: response.file_count || 0,
        repositories: response.repositories || [currentRepoId]
      };

      // Set default values if any properties are missing
      const statsData: IndexStats = {
        doc_count: response.doc_count || 0,
        index_size: response.index_size || 0,
        update_time: response.update_time || new Date().toISOString(),
        index_path: response.index_path || '',
        initialized: response.initialized !== undefined ? response.initialized : true,
        doc_details: docDetails
      };

      setStats(statsData);
    } catch (error) {
      toast({
        title: "Failed to load index statistics",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
      console.error("Error fetching index statistics:", error);

      // Set default stats for development/testing
      if (process.env.NODE_ENV === 'development') {
        setStats({
          doc_count: 42,
          index_size: 1024,
          update_time: new Date().toISOString(),
          index_path: '/path/to/index',
          initialized: true,
          doc_details: {
            groups: 6,
            users: 5,
            files: 31,
            repositories: [currentRepoId]
          }
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Trigger reindexing operation
  const handleReindex = async () => {
    if (!currentRepoId || reindexing) return;

    setReindexing(true);
    try {
      const response = await apiClient.search.reindex(currentRepoId);

      toast({
        title: "Reindexing Initiated",
        description: response.message || "Search index is being rebuilt",
      });

      // After reindexing completes, wait a moment and then refresh the stats
      setTimeout(() => {
        fetchStats();
        setReindexing(false);
      }, 2000);
    } catch (error) {
      console.error("Error reindexing:", error);

      toast({
        title: "Reindexing Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });

      setReindexing(false);
    }
  };

  // Effect to fetch stats when the repository changes or on manual refresh
  useEffect(() => {
    fetchStats();
  }, [currentRepoId]);

  // Render loading state if context is still loading or there's no repository selected
  if (contextLoading || (!repoId && !selectedRepoId)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </CardTitle>
          <div className="text-sm text-muted-foreground mt-1.5">
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
        <CardFooter>
          <Skeleton className="h-8 w-20" />
        </CardFooter>
      </Card>
    );
  }

  // Document Details Modal Component
  const DocumentDetailsModal = () => {
    if (!stats || !stats.doc_details) return null;

    const { doc_details } = stats;

    return (
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Search Index Details</DialogTitle>
            <DialogDescription>
              Detailed information about the indexed documents
            </DialogDescription>
          </DialogHeader>

          <div className="mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Document Type</TableHead>
                  <TableHead className="text-right">Count</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-blue-500" />
                    Users
                  </TableCell>
                  <TableCell className="text-right">
                    <Badge variant="outline">{doc_details.users}</Badge>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="flex items-center">
                    <FolderClosed className="h-4 w-4 mr-2 text-green-500" />
                    Groups
                  </TableCell>
                  <TableCell className="text-right">
                    <Badge variant="outline">{doc_details.groups}</Badge>
                  </TableCell>
                </TableRow>
                {doc_details.files !== undefined && doc_details.files > 0 && (
                  <TableRow>
                    <TableCell className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-amber-500" />
                      Files
                    </TableCell>
                    <TableCell className="text-right">
                      <Badge variant="outline">{doc_details.files}</Badge>
                    </TableCell>
                  </TableRow>
                )}
                <TableRow>
                  <TableCell className="flex items-center">
                    <Database className="h-4 w-4 mr-2 text-purple-500" />
                    Total Documents
                  </TableCell>
                  <TableCell className="text-right">
                    <Badge>{stats.doc_count}</Badge>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            {doc_details.repositories && doc_details.repositories.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Indexed Repositories:</h4>
                <div className="flex flex-wrap gap-2">
                  {doc_details.repositories.map((repo, index) => (
                    <Badge key={index} variant="secondary">{repo}</Badge>
                  ))}
                </div>
              </div>
            )}

            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Index Path:</h4>
              <p className="text-sm text-muted-foreground break-all">{stats.index_path}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Database className="h-6 w-6 text-purple-500" />
              <span>Search Index</span>
            </div>
            <Button
              size="icon"
              variant="ghost"
              onClick={fetchStats}
              disabled={loading || reindexing}
              title="Refresh Status"
            >
              <RefreshCw className={`h-4 w-4 ${(loading || reindexing) ? 'animate-spin' : ''}`} />
            </Button>
          </CardTitle>
          <div className="text-sm text-muted-foreground mt-1.5">
            Search index status and management
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ) : stats ? (
            <div className="grid gap-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Documents:</span>
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">
                    {stats.doc_count.toLocaleString()}
                  </span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => setShowDetailsModal(true)}
                    title="View Document Details"
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Last Updated:</span>
                <span className="text-sm font-medium">
                  {stats.update_time
                    ? (() => {
                        try {
                          // Try to parse the date and format it
                          return new Date(stats.update_time).toLocaleString();
                        } catch (e) {
                          console.error("Failed to parse update time:", e);
                          return stats.update_time;
                        }
                      })()
                    : 'Never'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Status:</span>
                <div className="flex items-center">
                  {stats.initialized ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm font-medium text-green-500">
                        Ready
                      </span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 text-amber-500 mr-1" />
                      <span className="text-sm font-medium text-amber-500">
                        Not Initialized
                      </span>
                    </>
                  )}
                </div>
              </div>
              {reindexing && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Reindex Status:</span>
                  <div className="flex items-center">
                    <RefreshCw className="h-4 w-4 text-blue-500 mr-1 animate-spin" />
                    <span className="text-sm font-medium text-blue-500">
                      Reindexing in Progress
                    </span>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-muted-foreground">No index statistics available</p>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleReindex}
            disabled={loading || reindexing || !currentRepoId}
            className="w-full"
          >
            {reindexing && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
            <Search className="h-4 w-4 mr-2" />
            Rebuild Search Index
          </Button>
        </CardFooter>
      </Card>

      {/* Render the document details modal */}
      <DocumentDetailsModal />
    </>
  );
};

export default IndexStatusCard;
