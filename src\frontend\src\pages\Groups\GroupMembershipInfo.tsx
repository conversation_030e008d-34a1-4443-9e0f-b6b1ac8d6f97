import React, { useState, useEffect } from 'react';
import { Users, UserCheck, Building2, ChevronDown, ChevronRight, Loader2, User as UserIcon } from 'lucide-react';
import { Group, ResolvedUser } from './GroupTypes';
import { apiClient } from '@/api/client';
import { useNavigate } from 'react-router-dom';

interface GroupMembershipInfoProps {
  group: Group;
  repoId: string;
  onGroupClick?: (groupName: string) => void;
}

const GroupMembershipInfo: React.FC<GroupMembershipInfoProps> = ({
  group,
  repoId,
  onGroupClick
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'direct' | 'resolved' | 'parents'>('direct');
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());
  const [enhancedGroup, setEnhancedGroup] = useState<Group | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uniqueUsers, setUniqueUsers] = useState<any[]>([]);
  const [uniqueUsersLoading, setUniqueUsersLoading] = useState(false);
  const [uniqueUsersError, setUniqueUsersError] = useState<string | null>(null);
  const [expandedUsers, setExpandedUsers] = useState<Set<string>>(new Set());

  // Create enhanced data from current group data
  useEffect(() => {
    const createEnhancedData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Create enhanced data from the existing Members array
        const directUsers: string[] = [];
        const directGroups: string[] = [];

        // Process members to categorize them
        if (group.Members && Array.isArray(group.Members)) {
          group.Members.forEach(member => {
            const memberName = typeof member === 'string' ? member : member.name;
            const memberType = typeof member === 'string' ? 'user' : member.type;

            if (memberType === 'group') {
              directGroups.push(memberName);
            } else {
              directUsers.push(memberName);
            }
          });
        }

        // Recursively resolve users from nested groups
        const resolvedUsers: ResolvedUser[] = [];
        const processedGroups = new Set<string>();
        let resolveErrors: string[] = [];

        // Add direct users
        directUsers.forEach(user => {
          resolvedUsers.push({
            name: user,
            path: [group.Groupname],
            direct: true
          });
        });

        // Recursively resolve users from nested groups
        const resolveGroupMembers = async (groupName: string, currentPath: string[]): Promise<void> => {
          if (processedGroups.has(groupName)) {
            return; // Avoid infinite loops
          }
          processedGroups.add(groupName);

          try {
            // Fetch the nested group data
            const response = await apiClient.search.searchGroups(`groupname:${groupName}`, 1, 1, repoId);
            if (response.groups && response.groups.length > 0) {
              const nestedGroup = response.groups[0];
              const newPath = [...currentPath, groupName];

              if (nestedGroup.Members && Array.isArray(nestedGroup.Members)) {
                for (const member of nestedGroup.Members) {
                  const memberName = typeof member === 'string' ? member : member.name;
                  const memberType = typeof member === 'string' ? 'user' : member.type;

                  if (memberType === 'group') {
                    // Recursively resolve this nested group
                    await resolveGroupMembers(memberName, newPath);
                  } else {
                    // Add user with the full path
                    resolvedUsers.push({
                      name: memberName,
                      path: newPath,
                      direct: false
                    });
                  }
                }
              }
            }
          } catch (error) {
            console.warn(`Failed to resolve group ${groupName}:`, error);
            resolveErrors.push(`Failed to resolve group ${groupName}`);
          }
        };

        // Resolve all direct groups
        for (const groupName of directGroups) {
          await resolveGroupMembers(groupName, [group.Groupname]);
        }

        // Find parent groups by searching for groups that contain this group as a member
        const parentGroups: string[] = [];
        try {
          // Search for groups that contain this group as a member
          const searchResponse = await apiClient.search.searchGroups(`members:${group.Groupname}`, 1, 100, repoId);
          if (searchResponse.groups) {
            searchResponse.groups.forEach(parentGroup => {
              if (parentGroup.Groupname !== group.Groupname &&
                  parentGroup.Members &&
                  Array.isArray(parentGroup.Members)) {
                // Check if this group is actually a member
                const isMember = parentGroup.Members.some(member => {
                  const memberName = typeof member === 'string' ? member : member.name;
                  return memberName === group.Groupname;
                });
                if (isMember) {
                  parentGroups.push(parentGroup.Groupname);
                }
              }
            });
          }
        } catch (error) {
          console.warn('Failed to find parent groups:', error);
        }

        // Create the enhanced group object
        const enhanced: Group = {
          ...group,
          DirectMembers: {
            users: directUsers,
            groups: directGroups,
            total: directUsers.length + directGroups.length
          },
          ResolvedMembers: {
            users: resolvedUsers,
            total: resolvedUsers.length
          },
          ParentGroups: parentGroups,
          // Store resolve errors for display
          resolveErrors: resolveErrors.length > 0 ? resolveErrors : undefined
        };

        setEnhancedGroup(enhanced);
      } catch (err) {
        console.error('Failed to create enhanced group data:', err);
        setError('Failed to process membership data');
      } finally {
        setLoading(false);
      }
    };

    createEnhancedData();
  }, [group, repoId]);

  // Fetch unique users for the "All Users" tab
  useEffect(() => {
    const fetchUniqueUsers = async () => {
      if (activeTab !== 'resolved') return;

      setUniqueUsersLoading(true);
      setUniqueUsersError(null);

      try {
        const response = await apiClient.data.getUniqueUsersForGroup(repoId, group.Groupname);
        setUniqueUsers(response.users || []);
      } catch (err) {
        console.error('Failed to fetch unique users:', err);
        setUniqueUsersError('Failed to load unique users');
      } finally {
        setUniqueUsersLoading(false);
      }
    };

    fetchUniqueUsers();
  }, [activeTab, repoId, group.Groupname]);

  // Handle group click navigation
  const handleGroupClick = (groupName: string) => {
    const params = new URLSearchParams();
    const searchValue = `groupname:"${groupName}"`;
    params.set('search', searchValue);
    params.set('page', '1');
    navigate(`/groups?${params.toString()}`);
  };

  // Toggle user expansion for showing more groups
  const toggleUserExpansion = (userName: string) => {
    const newExpanded = new Set(expandedUsers);
    if (newExpanded.has(userName)) {
      newExpanded.delete(userName);
    } else {
      newExpanded.add(userName);
    }
    setExpandedUsers(newExpanded);
  };

  const togglePath = (path: string) => {
    const newExpanded = new Set(expandedPaths);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedPaths(newExpanded);
  };

  const renderDirectMembers = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading membership data...</span>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-sm">{error}</div>;
    }

    if (!enhancedGroup?.DirectMembers) {
      return <div className="text-gray-500 text-sm">No enhanced membership data available</div>;
    }

    const { users, groups, total } = enhancedGroup.DirectMembers;

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Users className="w-4 h-4" />
          <span>Total: {total} members ({users.length} users, {groups.length} groups)</span>
        </div>

        {users.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-2 flex items-center gap-1">
              <UserCheck className="w-4 h-4" />
              Direct Users ({users.length})
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
              {users.map((user) => (
                <div key={user} className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-sm">
                  {user}
                </div>
              ))}
            </div>
          </div>
        )}

        {groups.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-2 flex items-center gap-1">
              <Building2 className="w-4 h-4" />
              Direct Groups ({groups.length})
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
              {groups.map((groupName) => (
                <div
                  key={groupName}
                  className="px-2 py-1 bg-green-50 text-green-700 rounded text-sm cursor-pointer hover:bg-green-100"
                  onClick={() => onGroupClick?.(groupName)}
                >
                  {groupName}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderResolvedMembers = () => {
    if (uniqueUsersLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading unique users...</span>
        </div>
      );
    }

    if (uniqueUsersError) {
      return <div className="text-red-500 text-sm">{uniqueUsersError}</div>;
    }

    if (uniqueUsers.length === 0) {
      return (
        <div className="text-gray-500 text-sm">
          No users found for this group.
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <UserIcon className="w-4 h-4" />
          <span>Total: {uniqueUsers.length} unique users</span>
        </div>

        <div className="space-y-3">
          {uniqueUsers.map((user, index) => {
            const userKey = `user-${index}-${user.name}`;
            const isExpanded = expandedUsers.has(user.name);
            const visibleGroups = isExpanded ? user.groups : user.groups.slice(0, 5);
            const hasMoreGroups = user.groups.length > 5;

            return (
              <div key={userKey} className="border rounded-lg p-3 bg-white">
                <div className="flex items-center gap-2 mb-2">
                  <UserIcon className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-sm">{user.name}</span>
                  <span className="text-xs text-gray-500">({user.groups.length} groups)</span>
                </div>

                <div className="flex flex-wrap gap-1 items-center">
                  {visibleGroups.map((groupName: string, groupIndex: number) => (
                    <div
                      key={`${userKey}-${groupName}-${groupIndex}`}
                      className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 cursor-pointer"
                      onClick={() => handleGroupClick(groupName)}
                    >
                      <Users className="h-3 w-3 inline-block mr-1" />
                      {groupName}
                    </div>
                  ))}

                  {hasMoreGroups && (
                    <div
                      className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded text-xs cursor-pointer inline-block h-5 leading-4 ml-1"
                      onClick={() => toggleUserExpansion(user.name)}
                    >
                      {isExpanded ? (
                        <span>Show less</span>
                      ) : (
                        <span>+{user.groups.length - 5} more</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderParentGroups = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading membership data...</span>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-sm">{error}</div>;
    }

    if (!enhancedGroup?.ParentGroups || enhancedGroup.ParentGroups.length === 0) {
      return <div className="text-gray-500 text-sm">This group is not a member of any other groups</div>;
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Building2 className="w-4 h-4" />
          <span>Member of {enhancedGroup.ParentGroups.length} groups</span>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
          {enhancedGroup.ParentGroups.map((parentGroup) => (
            <div
              key={parentGroup}
              className="px-2 py-1 bg-purple-50 text-purple-700 rounded text-sm cursor-pointer hover:bg-purple-100"
              onClick={() => onGroupClick?.(parentGroup)}
            >
              {parentGroup}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="mt-4 border-t pt-4">
      <div className="mb-4">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'direct'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('direct')}
          >
            Direct Members
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'resolved'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('resolved')}
          >
            All Users
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'parents'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('parents')}
          >
            Parent Groups
          </button>
        </div>
      </div>

      <div className="min-h-[200px]">
        {activeTab === 'direct' && renderDirectMembers()}
        {activeTab === 'resolved' && renderResolvedMembers()}
        {activeTab === 'parents' && renderParentGroups()}
      </div>
    </div>
  );
};

export default GroupMembershipInfo;
