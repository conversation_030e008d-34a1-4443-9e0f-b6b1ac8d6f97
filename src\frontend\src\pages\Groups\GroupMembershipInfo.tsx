import React, { useState, useEffect } from 'react';
import { Users, User<PERSON>he<PERSON>, Building2, Loader2, User as UserIcon } from 'lucide-react';
import { Group } from './GroupTypes';
import { apiClient } from '@/api/client';
import { useNavigate } from 'react-router-dom';

interface GroupMembershipInfoProps {
  group: Group;
  repoId: string;
  onGroupClick?: (groupName: string) => void;
}

const GroupMembershipInfo: React.FC<GroupMembershipInfoProps> = ({
  group,
  repoId,
  onGroupClick
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'direct' | 'resolved' | 'parents'>('direct');
  const [enhancedGroup, setEnhancedGroup] = useState<Group | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uniqueUsers, setUniqueUsers] = useState<any[]>([]);
  const [uniqueUsersLoading, setUniqueUsersLoading] = useState(false);
  const [uniqueUsersError, setUniqueUsersError] = useState<string | null>(null);
  const [expandedUsers, setExpandedUsers] = useState<Set<string>>(new Set());
  const [directUsers, setDirectUsers] = useState<any[]>([]);
  const [parentUsers, setParentUsers] = useState<any[]>([]);

  // Create enhanced data from current group data
  useEffect(() => {
    const createEnhancedData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Create enhanced data from the existing Members array
        const directUsers: string[] = [];
        const directGroups: string[] = [];

        // Process members to categorize them
        if (group.Members && Array.isArray(group.Members)) {
          group.Members.forEach(member => {
            const memberName = typeof member === 'string' ? member : member.name;
            const memberType = typeof member === 'string' ? 'user' : member.type;

            if (memberType === 'group') {
              directGroups.push(memberName);
            } else {
              directUsers.push(memberName);
            }
          });
        }

        // Recursively resolve users from nested groups
        const resolvedUsers: ResolvedUser[] = [];
        const processedGroups = new Set<string>();
        let resolveErrors: string[] = [];

        // Add direct users
        directUsers.forEach(user => {
          resolvedUsers.push({
            name: user,
            path: [group.Groupname],
            direct: true
          });
        });

        // Recursively resolve users from nested groups
        const resolveGroupMembers = async (groupName: string, currentPath: string[]): Promise<void> => {
          if (processedGroups.has(groupName)) {
            return; // Avoid infinite loops
          }
          processedGroups.add(groupName);

          try {
            // Fetch the nested group data
            const response = await apiClient.search.searchGroups(`groupname:${groupName}`, 1, 1, repoId);
            if (response.groups && response.groups.length > 0) {
              const nestedGroup = response.groups[0];
              const newPath = [...currentPath, groupName];

              if (nestedGroup.Members && Array.isArray(nestedGroup.Members)) {
                for (const member of nestedGroup.Members) {
                  const memberName = typeof member === 'string' ? member : member.name;
                  const memberType = typeof member === 'string' ? 'user' : member.type;

                  if (memberType === 'group') {
                    // Recursively resolve this nested group
                    await resolveGroupMembers(memberName, newPath);
                  } else {
                    // Add user with the full path
                    resolvedUsers.push({
                      name: memberName,
                      path: newPath,
                      direct: false
                    });
                  }
                }
              }
            }
          } catch (error) {
            console.warn(`Failed to resolve group ${groupName}:`, error);
            resolveErrors.push(`Failed to resolve group ${groupName}`);
          }
        };

        // Resolve all direct groups
        for (const groupName of directGroups) {
          await resolveGroupMembers(groupName, [group.Groupname]);
        }

        // Find parent groups by searching for groups that contain this group as a member
        const parentGroups: string[] = [];
        try {
          // Search for groups that contain this group as a member
          const searchResponse = await apiClient.search.searchGroups(`members:${group.Groupname}`, 1, 100, repoId);
          if (searchResponse.groups) {
            searchResponse.groups.forEach(parentGroup => {
              if (parentGroup.Groupname !== group.Groupname &&
                  parentGroup.Members &&
                  Array.isArray(parentGroup.Members)) {
                // Check if this group is actually a member
                const isMember = parentGroup.Members.some(member => {
                  const memberName = typeof member === 'string' ? member : member.name;
                  return memberName === group.Groupname;
                });
                if (isMember) {
                  parentGroups.push(parentGroup.Groupname);
                }
              }
            });
          }
        } catch (error) {
          console.warn('Failed to find parent groups:', error);
        }

        // Create the enhanced group object
        const enhanced: Group = {
          ...group,
          DirectMembers: {
            users: directUsers,
            groups: directGroups,
            total: directUsers.length + directGroups.length
          },
          ResolvedMembers: {
            users: resolvedUsers,
            total: resolvedUsers.length
          },
          ParentGroups: parentGroups,
          // Store resolve errors for display
          resolveErrors: resolveErrors.length > 0 ? resolveErrors : undefined
        };

        setEnhancedGroup(enhanced);
      } catch (err) {
        console.error('Failed to create enhanced group data:', err);
        setError('Failed to process membership data');
      } finally {
        setLoading(false);
      }
    };

    createEnhancedData();
  }, [group, repoId]);

  // Fetch unique users for the "All Users" tab
  useEffect(() => {
    const fetchUniqueUsers = async () => {
      if (activeTab !== 'resolved') return;

      setUniqueUsersLoading(true);
      setUniqueUsersError(null);

      try {
        // Get all groups from the repository to find group memberships for each user
        const allGroupsResponse = await apiClient.data.getGroups(repoId, 1, 1000); // Get a large number to get all groups
        const allGroups = allGroupsResponse.items || [];

        if (enhancedGroup?.ResolvedMembers?.users) {
          const userMap = new Map();

          // Process resolved users to create unique users
          enhancedGroup.ResolvedMembers.users.forEach(resolvedUser => {
            if (!userMap.has(resolvedUser.name)) {
              // Find all groups this user belongs to, categorizing them
              const directGroups = [];
              const otherGroups = [];

              allGroups.forEach(g => {
                if (g.Members && Array.isArray(g.Members)) {
                  const isMember = g.Members.some(member =>
                    (typeof member === 'string' && member === resolvedUser.name) ||
                    (typeof member === 'object' && member.name === resolvedUser.name && member.type === 'user')
                  );
                  if (isMember) {
                    // Check if this group is directly related to the current group we're viewing
                    const isDirectGroup = g.Groupname === group.Groupname ||
                                         (enhancedGroup?.DirectMembers?.groups?.includes(g.Groupname));

                    if (isDirectGroup) {
                      directGroups.push(g.Groupname);
                    } else {
                      otherGroups.push(g.Groupname);
                    }
                  }
                }
              });

              // Combine groups with direct groups first
              const allUserGroups = [...directGroups, ...otherGroups];

              userMap.set(resolvedUser.name, {
                name: resolvedUser.name,
                groups: allUserGroups.length > 0 ? allUserGroups : [group.Groupname],
                directGroups: directGroups,
                otherGroups: otherGroups
              });
            }
          });

          const uniqueUsersArray = Array.from(userMap.values());
          setUniqueUsers(uniqueUsersArray);

          // Process direct members
          const directUserMap = new Map();
          if (enhancedGroup?.DirectMembers?.users) {
            enhancedGroup.DirectMembers.users.forEach(directUser => {
              if (!directUserMap.has(directUser.name)) {
                // Find all groups this user belongs to
                const userGroups = [];
                allGroups.forEach(g => {
                  if (g.Members && Array.isArray(g.Members)) {
                    const isMember = g.Members.some(member =>
                      (typeof member === 'string' && member === directUser.name) ||
                      (typeof member === 'object' && member.name === directUser.name && member.type === 'user')
                    );
                    if (isMember) {
                      userGroups.push(g.Groupname);
                    }
                  }
                });

                directUserMap.set(directUser.name, {
                  name: directUser.name,
                  groups: userGroups.length > 0 ? userGroups : [group.Groupname],
                  directGroups: [group.Groupname], // Current group is always direct for direct members
                  otherGroups: userGroups.filter(g => g !== group.Groupname)
                });
              }
            });
          }
          setDirectUsers(Array.from(directUserMap.values()));

          // Process parent groups (groups that contain this group)
          const parentUserMap = new Map();
          if (enhancedGroup?.ParentMembers?.groups) {
            enhancedGroup.ParentMembers.groups.forEach(parentGroup => {
              // Find users in parent groups
              const parentGroupData = allGroups.find(g => g.Groupname === parentGroup.name);
              if (parentGroupData?.Members) {
                parentGroupData.Members.forEach(member => {
                  const memberName = typeof member === 'string' ? member : member.name;
                  const memberType = typeof member === 'string' ? 'user' : member.type;

                  if (memberType === 'user' && !parentUserMap.has(memberName)) {
                    // Find all groups this user belongs to
                    const userGroups = [];
                    allGroups.forEach(g => {
                      if (g.Members && Array.isArray(g.Members)) {
                        const isMember = g.Members.some(m =>
                          (typeof m === 'string' && m === memberName) ||
                          (typeof m === 'object' && m.name === memberName && m.type === 'user')
                        );
                        if (isMember) {
                          userGroups.push(g.Groupname);
                        }
                      }
                    });

                    parentUserMap.set(memberName, {
                      name: memberName,
                      groups: userGroups,
                      directGroups: [parentGroup.name], // Parent group is direct for these users
                      otherGroups: userGroups.filter(g => g !== parentGroup.name)
                    });
                  }
                });
              }
            });
          }
          setParentUsers(Array.from(parentUserMap.values()));
        } else {
          setUniqueUsers([]);
          setDirectUsers([]);
          setParentUsers([]);
        }
      } catch (err) {
        console.error('Failed to process unique users:', err);
        setUniqueUsersError('Failed to load unique users');
      } finally {
        setUniqueUsersLoading(false);
      }
    };

    fetchUniqueUsers();
  }, [activeTab, repoId, group.Groupname, enhancedGroup]);

  // Handle group click navigation
  const handleGroupClick = (groupName: string) => {
    const params = new URLSearchParams();
    const searchValue = `groupname:"${groupName}"`;
    params.set('search', searchValue);
    params.set('page', '1');
    navigate(`/groups?${params.toString()}`);
  };

  // Toggle user expansion for showing more groups
  const toggleUserExpansion = (userName: string) => {
    const newExpanded = new Set(expandedUsers);
    if (newExpanded.has(userName)) {
      newExpanded.delete(userName);
    } else {
      newExpanded.add(userName);
    }
    setExpandedUsers(newExpanded);
  };



  // Reusable function to render users with their group memberships
  const renderUsersWithGroups = (users: any[], title: string, icon: React.ReactNode) => {
    if (users.length === 0) {
      return (
        <div className="text-gray-500 text-sm">
          No users found.
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          {icon}
          <span>{title}: {users.length} unique users</span>
        </div>

        <div className="space-y-3">
          {users.map((user, index) => {
            const userKey = `user-${index}-${user.name}`;
            const isExpanded = expandedUsers.has(user.name);
            const visibleGroups = isExpanded ? user.groups : user.groups.slice(0, 5);
            const hasMoreGroups = user.groups.length > 5;

            return (
              <div key={userKey} className="border rounded-lg p-3 bg-white">
                <div className="flex items-center gap-2 mb-2">
                  <UserIcon className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-sm">{user.name}</span>
                  <span className="text-xs text-gray-500">({user.groups.length} groups)</span>
                </div>

                <div className="flex flex-wrap gap-1 items-center">
                  {visibleGroups.map((groupName: string, groupIndex: number) => {
                    // Check if this is a direct group or other group
                    const isDirect = user.directGroups?.includes(groupName);
                    const pillClass = isDirect
                      ? "bg-purple-100 text-purple-800"
                      : "bg-gray-100 text-gray-600";

                    return (
                      <div
                        key={`${userKey}-${groupName}-${groupIndex}`}
                        className={`${pillClass} px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 cursor-pointer`}
                        onClick={() => handleGroupClick(groupName)}
                      >
                        <Users className="h-3 w-3 inline-block mr-1" />
                        {groupName}
                      </div>
                    );
                  })}

                  {hasMoreGroups && (
                    <div
                      className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded text-xs cursor-pointer inline-block h-5 leading-4 ml-1"
                      onClick={() => toggleUserExpansion(user.name)}
                    >
                      {isExpanded ? (
                        <span>Show less</span>
                      ) : (
                        <span>+{user.groups.length - 5} more</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderDirectMembers = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading membership data...</span>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-sm">{error}</div>;
    }

    return renderUsersWithGroups(directUsers, "Direct Members", <UserCheck className="w-4 h-4" />);
  };

  const renderResolvedMembers = () => {
    if (uniqueUsersLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading unique users...</span>
        </div>
      );
    }

    if (uniqueUsersError) {
      return <div className="text-red-500 text-sm">{uniqueUsersError}</div>;
    }

    return renderUsersWithGroups(uniqueUsers, "Total", <UserIcon className="w-4 h-4" />);
  };

  const renderParentGroups = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading membership data...</span>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-sm">{error}</div>;
    }

    return renderUsersWithGroups(parentUsers, "Parent Group Members", <Building2 className="w-4 h-4" />);
  };

  return (
    <div className="mt-4 border-t pt-4">
      <div className="mb-4">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'direct'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('direct')}
          >
            Direct Members
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'resolved'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('resolved')}
          >
            All Users
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'parents'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('parents')}
          >
            Parent Groups
          </button>
        </div>
      </div>

      <div className="min-h-[200px]">
        {activeTab === 'direct' && renderDirectMembers()}
        {activeTab === 'resolved' && renderResolvedMembers()}
        {activeTab === 'parents' && renderParentGroups()}
      </div>
    </div>
  );
};

export default GroupMembershipInfo;
