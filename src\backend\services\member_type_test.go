package services

import (
	"fmt"
	"testing"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
)

// TestMemberTypeResolution tests that group members are correctly classified as groups or users
func TestMemberTypeResolution(t *testing.T) {
	dp := &DataProcessor{}

	// Create test groups with nested group structure
	groups := []models.Group{
		{
			Groupname:   "parent_group",
			Type:        "security",
			Description: "Parent group containing other groups",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "child_group", Type: models.UserMemberType}, // Initially marked as user (simulating JSON parsing)
				{Name: "john", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "child_group",
			Type:        "technical",
			Description: "Child group with users",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "alice", Type: models.UserMemberType},
				{Name: "bob", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "another_group",
			Type:        "business",
			Description: "Another group",
			Lob:         "finance",
			Members: models.GroupMembers{
				{Name: "charlie", Type: models.UserMemberType},
			},
		},
	}

	// Resolve members
	dp.ResolveMembers(groups)

	// Verify parent_group has correct member types
	parentGroup := groups[0]
	assert.Equal(t, "parent_group", parentGroup.Groupname)
	assert.Len(t, parentGroup.Members, 2, "Parent group should have exactly 2 direct members")

	// Find the child_group member
	var childGroupMember *models.Member
	var johnMember *models.Member
	for i, member := range parentGroup.Members {
		if member.Name == "child_group" {
			childGroupMember = &parentGroup.Members[i]
		} else if member.Name == "john" {
			johnMember = &parentGroup.Members[i]
		}
	}

	// Verify child_group is correctly identified as a group
	assert.NotNil(t, childGroupMember, "child_group should be found in parent_group members")
	assert.Equal(t, models.GroupMemberType, childGroupMember.Type, "child_group should be marked as type 'group'")

	// Verify john is correctly identified as a user
	assert.NotNil(t, johnMember, "john should be found in parent_group members")
	assert.Equal(t, models.UserMemberType, johnMember.Type, "john should be marked as type 'user'")

	// Verify that users from child_group are NOT added to parent_group
	for _, member := range parentGroup.Members {
		assert.NotEqual(t, "alice", member.Name, "alice should not be a direct member of parent_group")
		assert.NotEqual(t, "bob", member.Name, "bob should not be a direct member of parent_group")
	}

	// Verify child_group still has its own members
	childGroup := groups[1]
	assert.Equal(t, "child_group", childGroup.Groupname)
	assert.Len(t, childGroup.Members, 2, "Child group should have exactly 2 members")

	// Verify child_group members are all users
	for _, member := range childGroup.Members {
		assert.Equal(t, models.UserMemberType, member.Type, "All members of child_group should be users")
	}
}

// TestMemberTypeWithGroupPrefix tests that groups with "group:" prefix are handled correctly
func TestMemberTypeWithGroupPrefix(t *testing.T) {
	dp := &DataProcessor{}

	// Create test groups where one group references another with "group:" prefix
	groups := []models.Group{
		{
			Groupname:   "main_group",
			Type:        "security",
			Description: "Main group",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "sub_group", Type: models.GroupMemberType}, // Already marked as group (from "group:" prefix)
				{Name: "direct_user", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "sub_group",
			Type:        "technical",
			Description: "Sub group",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "nested_user", Type: models.UserMemberType},
			},
		},
	}

	// Resolve members
	dp.ResolveMembers(groups)

	// Verify main_group has correct member types
	mainGroup := groups[0]
	assert.Len(t, mainGroup.Members, 2, "Main group should have exactly 2 direct members")

	// Verify sub_group is still marked as group
	var subGroupMember *models.Member
	for i, member := range mainGroup.Members {
		if member.Name == "sub_group" {
			subGroupMember = &mainGroup.Members[i]
			break
		}
	}
	assert.NotNil(t, subGroupMember, "sub_group should be found in main_group members")
	assert.Equal(t, models.GroupMemberType, subGroupMember.Type, "sub_group should remain marked as type 'group'")

	// Verify nested_user is NOT added to main_group
	for _, member := range mainGroup.Members {
		assert.NotEqual(t, "nested_user", member.Name, "nested_user should not be a direct member of main_group")
	}
}

// TestRealWorldScenario tests a scenario similar to what you're seeing in the API
func TestRealWorldScenario(t *testing.T) {
	dp := &DataProcessor{}

	// Create test groups that simulate the real scenario from your API response
	groups := []models.Group{
		{
			Groupname:   "FM-Argo-Production-Admin",
			Type:        "access_group",
			Description: "This group allows editing from ArgoCD UI",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "FM-DevOps", Type: models.UserMemberType},       // Initially marked as user (simulating JSON parsing without "group:" prefix)
				{Name: "FM-SRE", Type: models.UserMemberType},          // Initially marked as user
				{Name: "FM-DevOps-India", Type: models.UserMemberType}, // Initially marked as user
			},
		},
		{
			Groupname:   "FM-DevOps",
			Type:        "team_group",
			Description: "DevOps team",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "john.doe", Type: models.UserMemberType},
				{Name: "jane.smith", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "FM-SRE",
			Type:        "team_group",
			Description: "SRE team",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "bob.wilson", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "FM-DevOps-India",
			Type:        "team_group",
			Description: "DevOps India team",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "raj.patel", Type: models.UserMemberType},
			},
		},
	}

	// Resolve members
	dp.ResolveMembers(groups)

	// Verify FM-Argo-Production-Admin has correct member types
	mainGroup := groups[0]
	assert.Equal(t, "FM-Argo-Production-Admin", mainGroup.Groupname)
	assert.Len(t, mainGroup.Members, 3, "Main group should have exactly 3 direct members")

	// Check that all members are correctly identified as groups
	expectedGroupMembers := []string{"FM-DevOps", "FM-SRE", "FM-DevOps-India"}
	actualGroupMembers := make(map[string]models.MemberType)

	for _, member := range mainGroup.Members {
		actualGroupMembers[member.Name] = member.Type
	}

	for _, expectedGroupName := range expectedGroupMembers {
		memberType, exists := actualGroupMembers[expectedGroupName]
		assert.True(t, exists, "Expected group member %s should exist", expectedGroupName)
		assert.Equal(t, models.GroupMemberType, memberType, "Member %s should be marked as type 'group'", expectedGroupName)
	}

	// Verify that individual users from nested groups are NOT added to the main group
	individualUsers := []string{"john.doe", "jane.smith", "bob.wilson", "raj.patel"}
	for _, user := range individualUsers {
		for _, member := range mainGroup.Members {
			assert.NotEqual(t, user, member.Name, "Individual user %s should not be a direct member of main group", user)
		}
	}

	// Verify that nested groups still have their own members
	for i := 1; i < len(groups); i++ {
		group := groups[i]
		assert.Greater(t, len(group.Members), 0, "Nested group %s should still have its own members", group.Groupname)
		for _, member := range group.Members {
			assert.Equal(t, models.UserMemberType, member.Type, "All members of nested group %s should be users", group.Groupname)
		}
	}
}

// TestWithActualJSONData tests with actual JSON data from the repository
func TestWithActualJSONData(t *testing.T) {
	dp := &DataProcessor{}

	// Simulate parsing the actual FM-Argo-Production-View.json file
	// This is the exact structure from the real JSON file
	groups := []models.Group{
		{
			Groupname:   "FM-Argo-Production-View",
			Type:        "access_group",
			Description: "This group allows reading from ArgoCD UI",
			Lob:         "fm",
			Members: models.GroupMembers{
				// These are initially parsed as users because they don't have "group:" prefix
				{Name: "FM-Dev-Leads", Type: models.UserMemberType},
				{Name: "FM-Customer-Ops", Type: models.UserMemberType},
				{Name: "FM-SaaS-Delivery", Type: models.UserMemberType},
				{Name: "FM-Dev-Engineers", Type: models.UserMemberType},
				{Name: "FM-QA-Engineers", Type: models.UserMemberType},
			},
		},
		// Add the referenced groups that should exist
		{
			Groupname:   "FM-Dev-Leads",
			Type:        "org_group",
			Description: "Group for FM Dev Leaders",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "abes", Type: models.UserMemberType},
				{Name: "annie.eldhose", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "FM-Customer-Ops",
			Type:        "org_group",
			Description: "Customer Operations team",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "user1", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "FM-SaaS-Delivery",
			Type:        "org_group",
			Description: "SaaS Delivery team",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "user2", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "FM-Dev-Engineers",
			Type:        "org_group",
			Description: "Development Engineers",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "user3", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "FM-QA-Engineers",
			Type:        "org_group",
			Description: "QA Engineers",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "user4", Type: models.UserMemberType},
			},
		},
	}

	// Print initial state
	fmt.Printf("=== BEFORE ResolveMembers ===\n")
	mainGroup := groups[0]
	fmt.Printf("Group: %s\n", mainGroup.Groupname)
	for _, member := range mainGroup.Members {
		fmt.Printf("  Member: %s, Type: %s\n", member.Name, member.Type)
	}

	// Resolve members
	dp.ResolveMembers(groups)

	// Print final state
	fmt.Printf("\n=== AFTER ResolveMembers ===\n")
	mainGroup = groups[0]
	fmt.Printf("Group: %s\n", mainGroup.Groupname)
	for _, member := range mainGroup.Members {
		fmt.Printf("  Member: %s, Type: %s\n", member.Name, member.Type)
	}

	// Verify that all members are correctly identified as groups
	assert.Equal(t, "FM-Argo-Production-View", mainGroup.Groupname)
	assert.Len(t, mainGroup.Members, 5, "Main group should have exactly 5 direct members")

	expectedGroupMembers := []string{"FM-Dev-Leads", "FM-Customer-Ops", "FM-SaaS-Delivery", "FM-Dev-Engineers", "FM-QA-Engineers"}
	actualGroupMembers := make(map[string]models.MemberType)

	for _, member := range mainGroup.Members {
		actualGroupMembers[member.Name] = member.Type
	}

	for _, expectedGroupName := range expectedGroupMembers {
		memberType, exists := actualGroupMembers[expectedGroupName]
		assert.True(t, exists, "Expected group member %s should exist", expectedGroupName)
		assert.Equal(t, models.GroupMemberType, memberType, "Member %s should be marked as type 'group', but got '%s'", expectedGroupName, memberType)
	}
}

// TestEnhancedMembershipInfo tests the new comprehensive membership information
func TestEnhancedMembershipInfo(t *testing.T) {
	dp := &DataProcessor{}

	// Create a realistic group hierarchy
	groups := []models.Group{
		{
			Groupname:   "FM-Argo-Production-View",
			Type:        "access_group",
			Description: "This group allows reading from ArgoCD UI",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "FM-Dev-Leads", Type: models.UserMemberType},    // Group member
				{Name: "FM-Customer-Ops", Type: models.UserMemberType}, // Group member
				{Name: "direct.user", Type: models.UserMemberType},     // Direct user
			},
		},
		{
			Groupname:   "FM-Dev-Leads",
			Type:        "org_group",
			Description: "Group for FM Dev Leaders",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "alice.dev", Type: models.UserMemberType},
				{Name: "bob.lead", Type: models.UserMemberType},
				{Name: "FM-Senior-Devs", Type: models.UserMemberType}, // Nested group
			},
		},
		{
			Groupname:   "FM-Customer-Ops",
			Type:        "org_group",
			Description: "Customer Operations team",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "charlie.ops", Type: models.UserMemberType},
				{Name: "diana.support", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "FM-Senior-Devs",
			Type:        "org_group",
			Description: "Senior Developers",
			Lob:         "fm",
			Members: models.GroupMembers{
				{Name: "eve.senior", Type: models.UserMemberType},
				{Name: "frank.architect", Type: models.UserMemberType},
			},
		},
	}

	// Process the groups
	dp.EnrichMembershipInfo(groups)

	// Test the main group (FM-Argo-Production-View)
	mainGroup := groups[0]

	// Test DirectMembers
	assert.NotNil(t, mainGroup.DirectMembers, "DirectMembers should not be nil")
	assert.Equal(t, 2, len(mainGroup.DirectMembers.Groups), "Should have 2 direct group members")
	assert.Equal(t, 1, len(mainGroup.DirectMembers.Users), "Should have 1 direct user member")
	assert.Equal(t, 3, mainGroup.DirectMembers.Total, "Total direct members should be 3")

	assert.Contains(t, mainGroup.DirectMembers.Groups, "FM-Dev-Leads")
	assert.Contains(t, mainGroup.DirectMembers.Groups, "FM-Customer-Ops")
	assert.Contains(t, mainGroup.DirectMembers.Users, "direct.user")

	// Test ResolvedMembers
	assert.NotNil(t, mainGroup.ResolvedMembers, "ResolvedMembers should not be nil")
	assert.Equal(t, 7, mainGroup.ResolvedMembers.Total, "Should resolve to 7 total users")

	// Check that all expected users are resolved
	userNames := make([]string, len(mainGroup.ResolvedMembers.Users))
	for i, user := range mainGroup.ResolvedMembers.Users {
		userNames[i] = user.Name
	}

	expectedUsers := []string{"direct.user", "alice.dev", "bob.lead", "eve.senior", "frank.architect", "charlie.ops", "diana.support"}
	for _, expectedUser := range expectedUsers {
		assert.Contains(t, userNames, expectedUser, "Should contain user %s", expectedUser)
	}

	// Test that direct user is marked as direct
	var directUser *models.ResolvedUser
	for _, user := range mainGroup.ResolvedMembers.Users {
		if user.Name == "direct.user" {
			directUser = &user
			break
		}
	}
	assert.NotNil(t, directUser, "Should find direct.user")
	assert.True(t, directUser.Direct, "direct.user should be marked as direct")
	assert.Equal(t, []string{"FM-Argo-Production-View"}, directUser.Path, "Direct user path should only contain main group")

	// Test that nested user has correct path
	var nestedUser *models.ResolvedUser
	for _, user := range mainGroup.ResolvedMembers.Users {
		if user.Name == "eve.senior" {
			nestedUser = &user
			break
		}
	}
	assert.NotNil(t, nestedUser, "Should find eve.senior")
	assert.False(t, nestedUser.Direct, "eve.senior should not be marked as direct")
	assert.Equal(t, []string{"FM-Argo-Production-View", "FM-Dev-Leads", "FM-Senior-Devs"}, nestedUser.Path, "Nested user should have correct path")

	// Test ParentGroups
	devLeadsGroup := groups[1] // FM-Dev-Leads
	assert.Contains(t, devLeadsGroup.ParentGroups, "FM-Argo-Production-View", "FM-Dev-Leads should have FM-Argo-Production-View as parent")

	seniorDevsGroup := groups[3] // FM-Senior-Devs
	assert.Contains(t, seniorDevsGroup.ParentGroups, "FM-Dev-Leads", "FM-Senior-Devs should have FM-Dev-Leads as parent")

	// Print results for manual verification
	fmt.Printf("\n=== Enhanced Membership Info Test Results ===\n")
	fmt.Printf("Main Group: %s\n", mainGroup.Groupname)
	fmt.Printf("Direct Members: %d users, %d groups (total: %d)\n",
		len(mainGroup.DirectMembers.Users),
		len(mainGroup.DirectMembers.Groups),
		mainGroup.DirectMembers.Total)
	fmt.Printf("Resolved Members: %d users\n", mainGroup.ResolvedMembers.Total)

	fmt.Printf("\nDirect Users: %v\n", mainGroup.DirectMembers.Users)
	fmt.Printf("Direct Groups: %v\n", mainGroup.DirectMembers.Groups)

	fmt.Printf("\nResolved Users:\n")
	for _, user := range mainGroup.ResolvedMembers.Users {
		fmt.Printf("  %s (direct: %t, path: %v)\n", user.Name, user.Direct, user.Path)
	}
}

// TestSimpleEnrichment tests basic enrichment without complex nesting
func TestSimpleEnrichment(t *testing.T) {
	dp := &DataProcessor{}

	// Simple test with just one level of nesting
	groups := []models.Group{
		{
			Groupname: "parent-group",
			Members: models.GroupMembers{
				{Name: "child-group", Type: models.UserMemberType},
				{Name: "direct-user", Type: models.UserMemberType},
			},
		},
		{
			Groupname: "child-group",
			Members: models.GroupMembers{
				{Name: "nested-user", Type: models.UserMemberType},
			},
		},
	}

	fmt.Printf("Before enrichment:\n")
	for _, group := range groups {
		fmt.Printf("  Group: %s, Members: %v\n", group.Groupname, group.Members)
	}

	dp.EnrichMembershipInfo(groups)

	fmt.Printf("After enrichment:\n")
	for _, group := range groups {
		fmt.Printf("  Group: %s\n", group.Groupname)
		if group.DirectMembers != nil {
			fmt.Printf("    Direct Users: %v\n", group.DirectMembers.Users)
			fmt.Printf("    Direct Groups: %v\n", group.DirectMembers.Groups)
		}
		if group.ResolvedMembers != nil {
			fmt.Printf("    Resolved Users: %d\n", group.ResolvedMembers.Total)
		}
		fmt.Printf("    Parent Groups: %v\n", group.ParentGroups)
	}

	// Basic assertions
	parentGroup := groups[0]
	assert.NotNil(t, parentGroup.DirectMembers)
	assert.Equal(t, 1, len(parentGroup.DirectMembers.Users))
	assert.Equal(t, 1, len(parentGroup.DirectMembers.Groups))

	// Check that member types are corrected in the original Members field
	assert.Equal(t, 2, len(parentGroup.Members))
	for _, member := range parentGroup.Members {
		if member.Name == "child-group" {
			assert.Equal(t, models.GroupMemberType, member.Type, "child-group should be marked as group type")
		} else if member.Name == "direct-user" {
			assert.Equal(t, models.UserMemberType, member.Type, "direct-user should be marked as user type")
		}
	}
}
