import { useState } from "react";
import { Check, ChevronsUpDown, GitBranch, Server } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useRepository } from "@/context/RepositoryContext";
import { RepositoryType } from "@/api/client";

export function RepositorySelector() {
  const [open, setOpen] = useState(false);
  const {
    selectedRepoId,
    setSelectedRepoId,
    isLoading,
    repositories,
    refreshRepositories,
  } = useRepository();

  // Get the selected repository for display
  const selectedRepository = repositories.find(
    (repo) => repo.id === selectedRepoId
  );
  const selectedRepositoryName =
    selectedRepository?.name || "Select Repository";

  // Direct selection handler to bypass any potential issues
  const selectRepository = (repoId: string) => {
        setSelectedRepoId(repoId);
    setOpen(false);
  };

  // Function to render the repository type icon/indicator
  const renderRepositoryTypeIcon = (type: RepositoryType) => {
    if (type === "bitbucket") {
      return <Server className="mr-2 h-4 w-4 text-blue-500" />;
    }
    return <GitBranch className="mr-2 h-4 w-4 text-orange-500" />;
  };

  // Function to render repository type badge
  const renderRepositoryTypeBadge = (type: RepositoryType) => {
    if (type === "bitbucket") {
      return (
        <Badge variant="secondary" className="ml-2 px-1 py-0 text-xs">
          BB
        </Badge>
      );
    }
    return (
      <Badge variant="default" className="ml-2 px-1 py-0 text-xs">
        GL
      </Badge>
    );
  };

  // Refresh repositories when selector is opened
  const handleOpenChange = (openState: boolean) => {
    setOpen(openState);
    if (openState) {
      // Force refresh repositories when dropdown is opened to ensure list is up-to-date
      refreshRepositories(true);
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[220px] justify-between"
          disabled={isLoading}
        >
          {isLoading ? (
            "Loading..."
          ) : (
            <div className="flex items-center">
              {selectedRepository &&
                renderRepositoryTypeIcon(selectedRepository.type)}
              <span className="truncate">{selectedRepositoryName}</span>
              {selectedRepository &&
                renderRepositoryTypeBadge(selectedRepository.type)}
            </div>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[220px] p-0">
        <div className="max-h-[300px] overflow-auto">
          <div className="p-2">
            <div className="text-sm font-medium text-muted-foreground mb-2">
              Repositories
            </div>
            {repositories.length === 0 ? (
              <div className="py-2 px-1 text-sm text-center text-muted-foreground">
                No repositories found
              </div>
            ) : (
              repositories.map((repo) => (
                <Button
                  key={repo.id}
                  variant="ghost"
                  className="w-full justify-start text-left mb-1 p-2 h-auto"
                  onClick={() => selectRepository(repo.id)}
                >
                  <div className="flex items-center w-full">
                    {renderRepositoryTypeIcon(repo.type)}
                    <span className="ml-2 truncate">{repo.name}</span>
                    {selectedRepoId === repo.id && (
                      <Check className="ml-auto h-4 w-4" />
                    )}
                  </div>
                </Button>
              ))
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

export default RepositorySelector;
