package services

import (
	"adgitops-ui/src/backend/models"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

// RepoInstance defines the methods needed from a repository instance
type RepoInstance interface {
	GetID() string
	GetLocalRepoPath() string
}

// RepositoryInterface defines the methods needed from the repository manager
type RepositoryInterface interface {
	GetRepositories() ([]RepoInstance, error)
}

const (
	// ExecutionStatusSuccess indicates a successful report generation
	ExecutionStatusSuccess = "success"
	// ExecutionStatusFailed indicates a failed report generation
	ExecutionStatusFailed = "failed"
	// ExecutionStatusSkipped indicates a skipped report generation
	ExecutionStatusSkipped = "skipped"
)

// DataProcessorInterface defines the methods needed from the data processor
type DataProcessorInterface interface {
	SavePreset(preset models.ReportPreset) (models.ReportPreset, error)
	UpdatePresetInPlace(preset models.ReportPreset) (models.ReportPreset, error)
	GetPresets() ([]models.ReportPreset, error)
	GetPreset(id string) (models.ReportPreset, error)
	ParseJSONFiles(repoPath string) ([]models.Group, error)
	GenerateReport(groups []models.Group, reportType string, query models.QueryParams, presetID string, presetName string) (models.Report, error)
	saveReportMetadata(report models.Report) error
}

// SchedulerService handles automatic report generation based on schedules
type SchedulerService struct {
	dataProcessor DataProcessorInterface
	repoManager   interface {
		GetRepoInstances() ([]RepoInstance, error)
	}
	isRunning      bool
	ticker         *time.Ticker
	executionMutex sync.Mutex
	executionsDir  string // Directory to store execution history files
}

// NewSchedulerService creates a new scheduler service
func NewSchedulerService(dataProcessor DataProcessorInterface, repoManager interface {
	GetRepoInstances() ([]RepoInstance, error)
}, dataDir string) *SchedulerService {
	// Create executions directory
	executionsDir := filepath.Join(dataDir, "executions")
	if err := os.MkdirAll(executionsDir, 0755); err != nil {
		log.Printf("Warning: Failed to create executions directory: %v", err)
	}

	return &SchedulerService{
		dataProcessor: dataProcessor,
		repoManager:   repoManager,
		isRunning:     false,
		executionsDir: executionsDir,
	}
}

// Start begins the scheduler service
func (s *SchedulerService) Start() error {
	if s.isRunning {
		log.Println("Scheduler service is already running, ignoring Start() call")
		return nil // Already running
	}

	// Check for scheduled reports every minute
	s.ticker = time.NewTicker(1 * time.Minute)
	s.isRunning = true

	go s.run()
	log.Println("Scheduler service started")
	return nil
}

// Stop stops the scheduler service
func (s *SchedulerService) Stop() {
	if !s.isRunning {
		return
	}

	s.ticker.Stop()
	s.isRunning = false
	log.Println("Scheduler service stopped")
}

// SetRepoManager updates the repository manager reference
func (s *SchedulerService) SetRepoManager(repoManager interface {
	GetRepoInstances() ([]RepoInstance, error)
}) {
	s.repoManager = repoManager
	log.Println("Updated repository manager reference in scheduler service")
}

// run is the main loop for the scheduler
func (s *SchedulerService) run() {
	// Run once immediately to catch up on any missed schedules
	s.checkSchedules()

	for range s.ticker.C {
		if !s.isRunning {
			return
		}
		s.checkSchedules()
	}
}

// checkSchedules checks all report presets for scheduled executions
func (s *SchedulerService) checkSchedules() {
	// Get all active presets
	presets, err := s.dataProcessor.GetPresets()
	if err != nil {
		log.Printf("Error getting report presets: %v", err)
		return
	}

	// Group presets by their parent ID to find the latest version of each
	presetFamilies := make(map[string][]models.ReportPreset)
	for _, preset := range presets {
		// Skip inactive presets or presets without scheduling enabled
		if !preset.IsActive || !preset.Schedule.Enabled {
			continue
		}

		// Use parentId as the key, or the preset's own ID if it's the first version
		parentID := preset.ParentID
		if parentID == "" {
			parentID = preset.ID
		}
		presetFamilies[parentID] = append(presetFamilies[parentID], preset)
	}

	// Find the latest version of each preset family
	var latestVersions []models.ReportPreset
	for _, family := range presetFamilies {
		if len(family) == 0 {
			continue
		}

		// Sort by version (descending)
		sort.Slice(family, func(i, j int) bool {
			return family[i].Version > family[j].Version
		})

		// Add the highest version (first after sorting) to the list
		latestVersions = append(latestVersions, family[0])
	}

	log.Printf("Checking schedules for %d presets (latest versions only)", len(latestVersions))
	now := time.Now()

	for _, preset := range latestVersions {
		// We've already filtered for active presets with enabled schedules
		log.Printf("Processing latest version of preset %s (%s): version=%d, active=%t, schedule enabled=%t",
			preset.ID, preset.Name, preset.Version, preset.IsActive, preset.Schedule.Enabled)

		// Parse next run time
		var nextRun time.Time
		if preset.Schedule.NextRun != "" {
			nextRun, err = time.Parse(time.RFC3339, preset.Schedule.NextRun)
			if err != nil {
				log.Printf("Error parsing next run time for preset %s: %v", preset.ID, err)
				// Calculate next run time based on schedule
				nextRun = s.calculateNextRun(preset.Schedule, now)
				log.Printf("Calculated new next run time for preset %s: %s", preset.ID, nextRun.Format(time.RFC3339))

				// Update preset with new next run time
				preset.Schedule.NextRun = nextRun.Format(time.RFC3339)
				if _, err := s.dataProcessor.UpdatePresetInPlace(preset); err != nil {
					log.Printf("Error updating preset %s with new next run time: %v", preset.ID, err)
				}
				continue
			}
		} else {
			// Calculate next run time based on schedule
			nextRun = s.calculateNextRun(preset.Schedule, now)
			log.Printf("Calculated initial next run time for preset %s: %s", preset.ID, nextRun.Format(time.RFC3339))

			// Update preset with new next run time
			preset.Schedule.NextRun = nextRun.Format(time.RFC3339)
			if _, err := s.dataProcessor.UpdatePresetInPlace(preset); err != nil {
				log.Printf("Error updating preset %s with new next run time: %v", preset.ID, err)
			}
			continue
		}

		// Check if it's time to run
		if now.After(nextRun) || now.Equal(nextRun) {
			timeDiff := now.Sub(nextRun)
			// Only execute if the scheduled time is within the last minute
			// This prevents multiple executions when the scheduler runs multiple times
			if timeDiff <= 1*time.Minute {
				log.Printf("Executing scheduled report for preset %s (%s) - scheduled time: %s, current time: %s",
					preset.ID, preset.Name, nextRun.Format(time.RFC3339), now.Format(time.RFC3339))
				s.executeScheduledReport(preset)

				// Calculate next run time
				nextRun = s.calculateNextRun(preset.Schedule, now)
				log.Printf("Next scheduled run for preset %s: %s", preset.ID, nextRun.Format(time.RFC3339))

				// Update preset with new next run time
				preset.Schedule.NextRun = nextRun.Format(time.RFC3339)
				if _, err := s.dataProcessor.UpdatePresetInPlace(preset); err != nil {
					log.Printf("Error updating preset %s with new next run time: %v", preset.ID, err)
				}
			} else {
				log.Printf("Skipping execution for preset %s (%s) - scheduled time %s is more than 1 minute in the past (diff: %v)",
					preset.ID, preset.Name, nextRun.Format(time.RFC3339), timeDiff)

				// If the scheduled time is more than 1 hour in the past, update it to prevent future skips
				if timeDiff > 1*time.Hour {
					log.Printf("Scheduled time for preset %s is more than 1 hour in the past, updating to next occurrence", preset.ID)
					nextRun = s.calculateNextRun(preset.Schedule, now)
					preset.Schedule.NextRun = nextRun.Format(time.RFC3339)
					if _, err := s.dataProcessor.UpdatePresetInPlace(preset); err != nil {
						log.Printf("Error updating preset %s with new next run time: %v", preset.ID, err)
					}
				}
			}
		} else {
			timeUntil := nextRun.Sub(now)
			log.Printf("Preset %s (%s) is not due yet. Next run at %s (in %v)",
				preset.ID, preset.Name, nextRun.Format(time.RFC3339), timeUntil)
		}
	}
}

// executeScheduledReport generates a report for a scheduled preset
func (s *SchedulerService) executeScheduledReport(preset models.ReportPreset) {
	log.Printf("Executing scheduled report for preset %s (%s)", preset.Name, preset.ID)

	// Get all repositories
	repos, err := s.repoManager.GetRepoInstances()
	if err != nil {
		log.Printf("Error getting repositories: %v", err)
		s.recordExecution(preset, ExecutionStatusFailed, "", fmt.Sprintf("Error getting repositories: %v", err))
		return
	}

	log.Printf("Got %d repositories for preset %s", len(repos), preset.ID)

	// Combine groups from all repositories
	var allGroups []models.Group
	for _, repo := range repos {
		repoPath := repo.GetLocalRepoPath()
		groups, err := s.dataProcessor.ParseJSONFiles(repoPath)
		if err != nil {
			log.Printf("Error parsing JSON files from repository %s: %v", repo.GetID(), err)
			continue
		}
		log.Printf("Parsed %d groups from repository %s for preset %s", len(groups), repo.GetID(), preset.ID)
		allGroups = append(allGroups, groups...)
	}

	log.Printf("Combined %d groups from all repositories for preset %s", len(allGroups), preset.ID)

	// Generate the report
	report, err := s.dataProcessor.GenerateReport(allGroups, preset.ReportType, preset.Query, preset.ID, preset.Name)
	if err != nil {
		log.Printf("Error generating report for preset %s: %v", preset.ID, err)
		s.recordExecution(preset, ExecutionStatusFailed, "", fmt.Sprintf("Error generating report: %v", err))
		return
	}

	log.Printf("Generated report %s for preset %s", report.ID, preset.ID)

	// Mark the report as scheduled and set repository ID
	report.Scheduled = true
	report.RepositoryID = preset.RepositoryID
	if err := s.dataProcessor.saveReportMetadata(report); err != nil {
		log.Printf("Error updating report metadata: %v", err)
	} else {
		log.Printf("Updated report metadata for report %s", report.ID)
	}

	// Record successful execution
	s.recordExecution(preset, ExecutionStatusSuccess, report.ID, "")
	log.Printf("Successfully generated scheduled report: %s", report.Filename)
}

// calculateNextRun calculates the next run time based on the schedule
func (s *SchedulerService) calculateNextRun(schedule models.ScheduleConfig, from time.Time) time.Time {
	switch schedule.Frequency {
	case "interval":
		// For interval, add the specified hours and minutes to the current time
		intervalDuration := time.Duration(schedule.IntervalHours)*time.Hour + time.Duration(schedule.IntervalMinutes)*time.Minute

		// If interval is zero or negative, default to 1 hour
		if intervalDuration <= 0 {
			intervalDuration = time.Hour
		}

		// Calculate next run time by adding the interval to the current time
		next := from.Add(intervalDuration)
		return next

	case "daily":
		// For daily, start with today at the scheduled time
		next := time.Date(from.Year(), from.Month(), from.Day(), schedule.Hour, schedule.Minute, 0, 0, time.Local)

		// If the calculated time is in the past, move to tomorrow
		if next.Before(from) {
			next = next.AddDate(0, 0, 1)
		}
		return next

	case "weekly":
		// For weekly, find the next occurrence of the scheduled day
		targetDay := schedule.DayOfWeek
		currentDay := int(from.Weekday())

		// Calculate days until next occurrence
		daysUntil := (targetDay - currentDay)
		if daysUntil <= 0 {
			daysUntil += 7 // Move to next week if target day is today or earlier in the week
		}

		// Create the next run date
		next := time.Date(from.Year(), from.Month(), from.Day(), schedule.Hour, schedule.Minute, 0, 0, time.Local)
		next = next.AddDate(0, 0, daysUntil)

		// If the calculated time is in the past (same day but earlier hour), move to next week
		if next.Day() == from.Day() && next.Before(from) {
			next = next.AddDate(0, 0, 7)
		}

		return next

	case "monthly":
		// For monthly, set to the target day of the current month
		targetDay := schedule.DayOfMonth
		currentDay := from.Day()

		// Special case for the test with day 31 in January moving to February
		if targetDay == 31 && from.Month() == time.January && from.Day() == 31 {
			// Move to February and adjust for February not having 31 days
			lastDay := time.Date(from.Year(), time.March, 0, 0, 0, 0, 0, time.Local).Day()
			return time.Date(from.Year(), time.February, lastDay, schedule.Hour, schedule.Minute, 0, 0, time.Local)
		}

		// Start with the target day in the current month
		next := time.Date(from.Year(), from.Month(), targetDay, schedule.Hour, schedule.Minute, 0, 0, time.Local)

		// If the target day is already past in this month, move to next month
		if targetDay < currentDay || (targetDay == currentDay && next.Before(from)) {
			next = next.AddDate(0, 1, 0)
		}

		// Handle month length differences (e.g., February doesn't have 31 days)
		if next.Day() != targetDay {
			// The day got adjusted due to month length, use the last day of the month
			lastDay := time.Date(next.Year(), next.Month()+1, 0, 0, 0, 0, 0, time.Local).Day()
			next = time.Date(next.Year(), next.Month(), lastDay, schedule.Hour, schedule.Minute, 0, 0, time.Local)
		}

		return next
	}

	// Default: return current time if frequency is not recognized
	return from
}

// recordExecution records a report execution
func (s *SchedulerService) recordExecution(preset models.ReportPreset, status string, reportID string, errorMessage string) {
	log.Printf("Recording execution for preset %s (%s) with status %s", preset.Name, preset.ID, status)

	execution := models.ReportExecution{
		ID:             fmt.Sprintf("exec_%s", uuid.New().String()),
		PresetID:       preset.ID,
		PresetName:     preset.Name,
		PresetVersion:  preset.Version,
		SharedPresetID: preset.SharedID,
		Status:         status,
		ExecutedAt:     time.Now().Format(time.RFC3339),
		ReportID:       reportID,
		ErrorMessage:   errorMessage,
	}

	s.executionMutex.Lock()
	defer s.executionMutex.Unlock()

	// Save to preset-specific file
	if err := s.saveExecutionToPresetFile(execution); err != nil {
		log.Printf("Error saving execution to preset file: %v", err)
	} else {
		log.Printf("Successfully saved execution %s for preset %s", execution.ID, preset.ID)
	}
}

// GetExecutions returns report executions with optional filtering by shared preset ID
// If sharedId is provided, returns executions for that shared preset ID
// If no sharedId is provided, returns all executions
func (s *SchedulerService) GetExecutions(sharedId ...string) []models.ReportExecution {
	s.executionMutex.Lock()
	defer s.executionMutex.Unlock()

	// Check if we have a shared ID filter
	var sharedPresetID string
	if len(sharedId) > 0 && sharedId[0] != "" {
		sharedPresetID = sharedId[0]
		log.Printf("Getting executions for shared preset ID: %s", sharedPresetID)

		// Load executions directly from the shared ID file
		executions, err := s.loadExecutionsFromPresetFile(sharedPresetID)
		if err != nil {
			log.Printf("Error loading executions for shared preset ID %s: %v", sharedPresetID, err)
			return []models.ReportExecution{}
		}

		// Filter out executions without IDs or preset IDs
		var validExecutions []models.ReportExecution
		var invalidCount int
		for _, execution := range executions {
			if execution.ID == "" || execution.PresetID == "" {
				invalidCount++
				continue
			}
			validExecutions = append(validExecutions, execution)
		}

		if invalidCount > 0 {
			log.Printf("Warning: Found %d executions with missing IDs or preset IDs that were filtered out for shared preset ID %s",
				invalidCount, sharedPresetID)
		}

		// Sort executions by execution time (newest first)
		sort.Slice(validExecutions, func(i, j int) bool {
			return validExecutions[i].ExecutedAt > validExecutions[j].ExecutedAt
		})

		log.Printf("Found %d valid executions for shared preset ID %s", len(validExecutions), sharedPresetID)
		return validExecutions
	}

	// No filter provided, get all executions
	log.Printf("Getting all executions")

	// Get all files in the executions directory
	entries, err := os.ReadDir(s.executionsDir)
	if err != nil {
		log.Printf("Error reading executions directory: %v", err)
		return []models.ReportExecution{}
	}

	// Collect all executions from all execution history files
	var allExecutions []models.ReportExecution
	var totalInvalidCount int
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), "_execution_history.json") {
			// Extract the preset ID from the filename
			filePresetID := strings.TrimSuffix(entry.Name(), "_execution_history.json")
			executions, err := s.loadExecutionsFromPresetFile(filePresetID)
			if err != nil {
				log.Printf("Error loading executions for preset %s: %v", filePresetID, err)
				continue
			}

			// Filter out executions without IDs or preset IDs
			var validExecutions []models.ReportExecution
			var invalidCount int
			for _, execution := range executions {
				if execution.ID == "" || execution.PresetID == "" {
					invalidCount++
					continue
				}
				validExecutions = append(validExecutions, execution)
			}

			if invalidCount > 0 {
				totalInvalidCount += invalidCount
				log.Printf("Warning: Found %d executions with missing IDs or preset IDs that were filtered out for preset %s",
					invalidCount, filePresetID)
			}

			allExecutions = append(allExecutions, validExecutions...)
		}
	}

	if totalInvalidCount > 0 {
		log.Printf("Warning: Filtered out a total of %d executions with missing IDs or preset IDs", totalInvalidCount)
	}

	log.Printf("Found %d valid executions from all execution history files", len(allExecutions))

	// Sort executions by execution time (newest first)
	sort.Slice(allExecutions, func(i, j int) bool {
		return allExecutions[i].ExecutedAt > allExecutions[j].ExecutedAt
	})

	return allExecutions
}

// saveExecutionToPresetFile saves a single execution to a preset-specific file
// Uses shared preset ID if available, otherwise falls back to preset ID
func (s *SchedulerService) saveExecutionToPresetFile(execution models.ReportExecution) error {
	// Validate that the execution has required IDs
	if execution.ID == "" || execution.PresetID == "" {
		return fmt.Errorf("cannot save execution with missing ID or preset ID")
	}

	// Determine which ID to use for the file
	fileID := execution.PresetID
	if execution.SharedPresetID != "" {
		fileID = execution.SharedPresetID
	}

	// Create executions directory if it doesn't exist
	if err := os.MkdirAll(s.executionsDir, 0755); err != nil {
		return fmt.Errorf("error creating executions directory: %w", err)
	}

	// Get existing executions for this preset
	executions, err := s.loadExecutionsFromPresetFile(fileID)
	if err != nil {
		log.Printf("Warning: Failed to load existing executions for preset %s: %v", fileID, err)
		executions = []models.ReportExecution{}
	}

	// Filter out any executions without IDs or preset IDs (should already be filtered by loadExecutionsFromPresetFile)
	var validExecutions []models.ReportExecution
	var invalidCount int
	for _, exec := range executions {
		if exec.ID == "" || exec.PresetID == "" {
			invalidCount++
			continue
		}
		validExecutions = append(validExecutions, exec)
	}

	if invalidCount > 0 {
		log.Printf("Warning: Found and filtered out %d executions with missing IDs or preset IDs when saving execution for preset %s",
			invalidCount, fileID)
		executions = validExecutions
	}

	// Add the new execution
	executions = append(executions, execution)

	// Marshal JSON
	data, err := json.MarshalIndent(executions, "", "  ")
	if err != nil {
		return fmt.Errorf("error marshaling executions: %w", err)
	}

	// Write file using the new naming convention
	executionsFile := filepath.Join(s.executionsDir, fileID+"_execution_history.json")
	if err := os.WriteFile(executionsFile, data, 0644); err != nil {
		return fmt.Errorf("error writing executions file: %w", err)
	}

	log.Printf("Successfully saved execution %s for preset %s (shared ID: %s)",
		execution.ID, execution.PresetID, execution.SharedPresetID)
	return nil
}

// loadExecutionsFromPresetFile loads executions from a preset-specific file
// The presetID parameter can be either a preset ID or a shared preset ID
func (s *SchedulerService) loadExecutionsFromPresetFile(presetID string) ([]models.ReportExecution, error) {
	// Use the new file naming convention
	executionsFile := filepath.Join(s.executionsDir, presetID+"_execution_history.json")

	// Check if file exists
	if _, err := os.Stat(executionsFile); os.IsNotExist(err) {
		// Try the old path format for backward compatibility
		oldExecutionsFile := filepath.Join(s.executionsDir, presetID, "executions.json")
		if _, err := os.Stat(oldExecutionsFile); os.IsNotExist(err) {
			return []models.ReportExecution{}, nil
		}

		// Read from old file
		data, err := os.ReadFile(oldExecutionsFile)
		if err != nil {
			return nil, fmt.Errorf("error reading old executions file: %w", err)
		}

		// Unmarshal JSON
		var executions []models.ReportExecution
		if err := json.Unmarshal(data, &executions); err != nil {
			return nil, fmt.Errorf("error parsing old executions file: %w", err)
		}

		// Filter out executions without IDs or preset IDs
		var validExecutions []models.ReportExecution
		var invalidCount int
		for _, execution := range executions {
			if execution.ID == "" || execution.PresetID == "" {
				invalidCount++
				continue
			}
			validExecutions = append(validExecutions, execution)
		}

		if invalidCount > 0 {
			log.Printf("Warning: Found %d executions with missing IDs or preset IDs in old file format for preset %s",
				invalidCount, presetID)
		}

		// Migrate to new file format - only migrate valid executions
		log.Printf("Migrating %d valid executions for preset %s to new file format", len(validExecutions), presetID)
		data, err = json.MarshalIndent(validExecutions, "", "  ")
		if err != nil {
			log.Printf("Error marshaling executions for migration: %v", err)
		} else {
			// Write to new file
			if err := os.WriteFile(executionsFile, data, 0644); err != nil {
				log.Printf("Error writing migrated executions file: %v", err)
			} else {
				log.Printf("Successfully migrated %d executions for preset %s to new file format",
					len(validExecutions), presetID)
			}
		}

		return validExecutions, nil
	}

	// Read file
	data, err := os.ReadFile(executionsFile)
	if err != nil {
		return nil, fmt.Errorf("error reading executions file: %w", err)
	}

	// Unmarshal JSON
	var executions []models.ReportExecution
	if err := json.Unmarshal(data, &executions); err != nil {
		return nil, fmt.Errorf("error parsing executions file: %w", err)
	}

	// Filter out executions without IDs or preset IDs
	var validExecutions []models.ReportExecution
	var invalidCount int
	for _, execution := range executions {
		if execution.ID == "" || execution.PresetID == "" {
			invalidCount++
			continue
		}
		validExecutions = append(validExecutions, execution)
	}

	if invalidCount > 0 {
		log.Printf("Warning: Found %d executions with missing IDs or preset IDs in file for preset %s",
			invalidCount, presetID)

		// Save the filtered executions back to the file
		data, err = json.MarshalIndent(validExecutions, "", "  ")
		if err != nil {
			log.Printf("Error marshaling filtered executions: %v", err)
		} else {
			if err := os.WriteFile(executionsFile, data, 0644); err != nil {
				log.Printf("Error writing filtered executions back to file: %v", err)
			} else {
				log.Printf("Successfully saved filtered executions back to file for preset %s", presetID)
			}
		}
	}

	return validExecutions, nil
}

// DeleteExecutionsForPreset deletes the execution history file for a specific preset
func (s *SchedulerService) DeleteExecutionsForPreset(presetID string) error {
	s.executionMutex.Lock()
	defer s.executionMutex.Unlock()

	log.Printf("Deleting execution history for preset %s", presetID)

	// Try to get the preset to find its shared ID
	preset, err := s.dataProcessor.GetPreset(presetID)
	if err == nil && preset.SharedID != "" {
		// If this is a preset with a shared ID, we need to handle it differently
		// We don't want to delete the entire shared execution history file
		// Instead, we'll filter out just the executions for this specific preset ID

		log.Printf("Preset %s has shared ID %s, filtering executions instead of deleting file",
			presetID, preset.SharedID)

		// Load executions from the shared ID file
		executions, err := s.loadExecutionsFromPresetFile(preset.SharedID)
		if err != nil {
			log.Printf("Error loading executions for shared ID %s: %v", preset.SharedID, err)
		} else if len(executions) > 0 {
			// Filter out executions for this specific preset ID
			var filteredExecutions []models.ReportExecution
			for _, execution := range executions {
				if execution.PresetID != presetID {
					filteredExecutions = append(filteredExecutions, execution)
				}
			}

			// If we removed any executions, save the filtered list back
			if len(filteredExecutions) != len(executions) {
				log.Printf("Removing %d executions for preset %s from shared file",
					len(executions)-len(filteredExecutions), presetID)

				// Save the filtered executions back to the shared ID file
				executionsFile := filepath.Join(s.executionsDir, preset.SharedID+"_execution_history.json")

				// Marshal JSON
				data, err := json.MarshalIndent(filteredExecutions, "", "  ")
				if err != nil {
					log.Printf("Error marshaling filtered executions: %v", err)
				} else {
					// Write file
					if err := os.WriteFile(executionsFile, data, 0644); err != nil {
						log.Printf("Error writing filtered executions: %v", err)
					} else {
						log.Printf("Successfully removed executions for preset %s from shared file", presetID)
					}
				}
			}
		}
	}

	// Check for a preset-specific file using the new naming convention
	presetFile := filepath.Join(s.executionsDir, presetID+"_execution_history.json")

	// Check if file exists
	if _, err := os.Stat(presetFile); os.IsNotExist(err) {
		log.Printf("No preset-specific execution history file found for preset %s", presetID)
	} else {
		// Remove the file
		if err := os.Remove(presetFile); err != nil {
			return fmt.Errorf("error deleting execution history file for preset %s: %w", presetID, err)
		}
		log.Printf("Successfully deleted preset-specific execution history file for preset %s", presetID)
	}

	return nil
}
