import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

import { Power, PowerOff, Trash } from 'lucide-react'
import { ReportPreset } from './types'

interface PresetCardActionsProps {
  preset: ReportPreset
  selectedRepoId: string
  onToggleActivation: (presetId: string) => void
  onDeleteClick: (presetId: string) => void
}

export function PresetCardActions({ preset, onToggleActivation, onDeleteClick }: PresetCardActionsProps) {
  // These variables are used for tracking if the preset has reports
  // const navigate = useNavigate()
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setHasReports] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(true)

  // We'll use a prop to determine if this preset has reports instead of making API calls
  // This will prevent the infinite loop of API calls
  useEffect(() => {
    // Set loading to false immediately since we're not making an API call
    setLoading(false)

    // Check if the preset has reports based on its properties
    // This is a temporary solution until we implement a proper way to check for reports
    setHasReports(preset.hasReports || false)
  }, [preset])

  return (
    <div className="flex gap-1">
      {/* Activate/Deactivate button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={(e) => {
          e.stopPropagation()
          onToggleActivation(preset.id!)
        }}
        title={preset.isActive === false ? "Activate preset" : "Deactivate preset"}
        className={`transition-all h-8 text-xs px-2 ${preset.isActive === false
          ? "hover:bg-green-50 hover:text-green-700"
          : "hover:bg-gray-100"}`}
      >
        {preset.isActive === false ? (
          <><Power className="h-3.5 w-3.5 mr-1" /> Activate</>
        ) : (
          <><PowerOff className="h-3.5 w-3.5 mr-1" /> Deactivate</>
        )}
      </Button>

      {/* Delete button - only show for presets that don't have reports */}
      {!loading && !preset.hasReports && (
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            onDeleteClick(preset.id!)
          }}
          title="Delete preset permanently"
          className="transition-all h-8 text-xs px-2 hover:bg-red-50 hover:text-red-700"
        >
          <Trash className="h-3.5 w-3.5 mr-1" /> Delete
        </Button>
      )}
    </div>
  )
}
