import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Loader2,
  Plus,
  Pencil,
  Trash,
  RefreshCw
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { apiClient, GitLabConfig, RepositoryStatus } from '@/api/client';
import { useRepository } from '@/context/RepositoryContext';

const GitLabSettings = () => {
  const { toast } = useToast();
  const { setSelectedRepoId } = useRepository();
  const [isLoading, setIsLoading] = useState(false);
  const [repositories, setRepositories] = useState<GitLabConfig[]>([]);
  const [repoStatuses, setRepoStatuses] = useState<Record<string, RepositoryStatus>>({});
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Form state for adding/editing repository
  const [formState, setFormState] = useState<GitLabConfig & { type: 'gitlab' }>({
    id: '',
    name: '',
    url: '',
    token: '',
    projectId: '',
    pollFrequency: 300,
    isActive: true,
    type: 'gitlab',
  });
  const [isEditing, setIsEditing] = useState(false);

  // Load repositories on component mount
  useEffect(() => {
    loadRepositories();
  }, []);

  const loadRepositories = async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.repositories.getConfigurations();
      setRepositories(response.configs);

      // Load statuses for each repository
      if (response.configs.length > 0) {
        const statuses = await apiClient.repositories.getAllStatuses();
        setRepoStatuses(statuses.statuses);
      }
    } catch (error) {
      toast({
        title: "Error Loading Repositories",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes in the form
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : name === 'pollFrequency' ? Number(value) : value
    }));
  };

  // Function to open the dialog for adding a new repository
  const handleAddRepository = () => {
    setFormState({
      id: '',
      name: '',
      url: '',
      token: '',
      projectId: '',
      pollFrequency: 300,
      isActive: true,
      type: 'gitlab',
    });
    setIsEditing(false);
    setIsDialogOpen(true);
  };

  // Function to open the dialog for editing an existing repository
  const handleEditRepository = (repo: GitLabConfig) => {
    setFormState({
      ...repo,
      type: 'gitlab',
    });
    setIsEditing(true);
    setIsDialogOpen(true);
  };

  // Function to handle form submission (add or edit)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate the inputs
    if (!formState.name || !formState.url || !formState.token || !formState.projectId) {
      toast({
        title: "Validation Error",
        description: "All fields are required",
        variant: "destructive",
      });
      return;
    }

    if (formState.pollFrequency < 30) {
      toast({
        title: "Validation Error",
        description: "Poll frequency must be at least 30 seconds",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      if (isEditing) {
        // Update existing repository
        await apiClient.repositories.updateConfiguration(formState.id!, formState);
        toast({
          title: "Repository Updated",
          description: `The repository "${formState.name}" has been updated successfully`,
        });
      } else {
        // Add new repository
        const response = await apiClient.repositories.addConfiguration(formState);
        toast({
          title: "Repository Added",
          description: `The repository "${formState.name}" has been added successfully`,
        });

        // Set this as the selected repository if it's the first one
        if (repositories.length === 0) {
          setSelectedRepoId(response.config.id!);
        }
      }

      // Close dialog and reload repositories
      setIsDialogOpen(false);
      await loadRepositories();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to delete a repository
  const handleDeleteRepository = async (id: string) => {
    if (!window.confirm("Are you sure you want to delete this repository configuration?")) {
      return;
    }

    setIsLoading(true);
    try {
      await apiClient.repositories.deleteConfiguration(id);
      toast({
        title: "Repository Deleted",
        description: "The repository configuration has been deleted successfully",
      });
      await loadRepositories();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to sync a repository manually
  const handleSyncRepository = async (id: string) => {
    setIsLoading(true);
    try {
      const response = await apiClient.repositories.syncRepository(id);
      toast({
        title: "Repository Synced",
        description: response.message || "The repository has been synced successfully",
      });

      // Refresh statuses
      const statuses = await apiClient.repositories.getAllStatuses();
      setRepoStatuses(statuses.statuses);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // These functions are currently not used in the UI but kept for future use
  // They are commented out to avoid TypeScript warnings
  /*
  // Function to start polling for a repository
  const handleStartPolling = async (id: string) => {
    setIsLoading(true);
    try {
      const response = await apiClient.repositories.startPolling(id);
      toast({
        title: "Polling Started",
        description: response.message || "The repository will now be monitored for changes",
      });

      // Refresh statuses
      const statuses = await apiClient.repositories.getAllStatuses();
      setRepoStatuses(statuses.statuses);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to stop polling for a repository
  const handleStopPolling = async (id: string) => {
    setIsLoading(true);
    try {
      const response = await apiClient.repositories.stopPolling(id);
      toast({
        title: "Polling Stopped",
        description: response.message || "Repository monitoring has been stopped",
      });

      // Refresh statuses
      const statuses = await apiClient.repositories.getAllStatuses();
      setRepoStatuses(statuses.statuses);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  */

  // Helper function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>GitLab Repositories</CardTitle>
              <CardDescription>
                Manage multiple GitLab repositories for ADGitOps data extraction.
              </CardDescription>
            </div>
            <Button onClick={handleAddRepository}>
              <Plus className="h-4 w-4 mr-2" />
              Add Repository
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading && (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          )}

          {!isLoading && repositories.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No GitLab repositories configured.</p>
              <p className="mt-2">Click "Add Repository" to configure your first repository.</p>
            </div>
          )}

          {!isLoading && repositories.length > 0 && (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>URL</TableHead>
                    <TableHead>Project ID</TableHead>
                    <TableHead>Poll Frequency</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Sync</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {repositories.map((repo) => (
                    <TableRow key={repo.id}>
                      <TableCell className="font-medium">
                        {repo.name}
                        {repo.isActive && <Badge className="ml-2" variant="outline">Active</Badge>}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate">{repo.url}</TableCell>
                      <TableCell>{repo.projectId}</TableCell>
                      <TableCell>{repo.pollFrequency} sec</TableCell>
                      <TableCell>
                        {repoStatuses[repo.id!] ? (
                          repoStatuses[repo.id!].syncInProgress ? (
                            <Badge variant="secondary">Syncing</Badge>
                          ) : (
                            <Badge variant="outline" className="bg-green-50 text-green-700">
                              Synced
                            </Badge>
                          )
                        ) : (
                          <Badge variant="outline" className="bg-gray-50">
                            Unknown
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {repoStatuses[repo.id!] ? formatDate(repoStatuses[repo.id!].lastSync) : 'Never'}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSyncRepository(repo.id!)}
                            disabled={isLoading}
                          >
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditRepository(repo)}
                            disabled={isLoading}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteRepository(repo.id!)}
                            disabled={isLoading}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Repository Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]" onClose={() => setIsDialogOpen(false)}>
          <DialogHeader>
            <DialogTitle>{isEditing ? 'Edit Repository' : 'Add Repository'}</DialogTitle>
            <DialogDescription>
              {isEditing
                ? 'Update the GitLab repository configuration details.'
                : 'Configure a new GitLab repository for ADGitOps data extraction.'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Display Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formState.name}
                  onChange={handleChange}
                  placeholder="e.g., Production GitOps"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="url">GitLab URL</Label>
                <Input
                  id="url"
                  name="url"
                  value={formState.url}
                  onChange={handleChange}
                  placeholder="e.g., https://gitlab.company.com"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="token">GitLab API Token</Label>
                <Input
                  id="token"
                  name="token"
                  type="password"
                  value={formState.token}
                  onChange={handleChange}
                  placeholder="Your GitLab Personal Access Token"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="projectId">Project ID</Label>
                <Input
                  id="projectId"
                  name="projectId"
                  value={formState.projectId}
                  onChange={handleChange}
                  placeholder="GitLab Project ID or path (e.g., 'group/project')"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="pollFrequency">Poll Frequency (seconds)</Label>
                <Input
                  id="pollFrequency"
                  name="pollFrequency"
                  type="number"
                  min="30"
                  value={formState.pollFrequency}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  name="isActive"
                  checked={formState.isActive}
                  onCheckedChange={(checked) =>
                    setFormState(prev => ({ ...prev, isActive: checked }))
                  }
                />
                <Label htmlFor="isActive">Active (Start polling automatically)</Label>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? 'Update Repository' : 'Add Repository'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GitLabSettings;
