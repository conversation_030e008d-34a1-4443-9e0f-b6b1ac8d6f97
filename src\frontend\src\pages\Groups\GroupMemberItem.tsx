import { User, Users } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { PaginationInfo } from './GroupTypes';

interface GroupMemberItemProps {
  memberName: string;
  memberType: string;
  groupKey: string;
  memberIndex: number;
  expanded?: boolean;
  selectedRepoId: string | null;
  selectedLob: string;
  pagination: PaginationInfo;
  setInputValue: (value: string) => void;
  fetchGroupsData: (repoId: string, page: number, pageSize: number, lob?: string, search?: string) => void;
}

const GroupMemberItem = ({
  memberName,
  memberType,
  groupKey,
  memberIndex,
  expanded = false,
  selectedRepoId,
  selectedLob,
  pagination,
  setInputValue,
  fetchGroupsData
}: GroupMemberItemProps) => {
  const navigate = useNavigate();
  const isGroup = memberType === 'group';
  const suffix = expanded ? '-expanded' : '';

  const handleClick = () => {
    // Navigate to search for this member
    const params = new URLSearchParams();

    if (isGroup) {
      // For groups, use field-specific search with the exact group name
      // Format: groupname:"exactGroupName"
      const searchValue = `groupname:"${memberName}"`;

      // Update the input value directly
      setInputValue(searchValue);

      // Set search parameter and page to 1
      params.set('search', searchValue);
      params.set('page', '1');

      // Navigate to groups page with search for this group
      const url = `/groups?${params.toString()}`;

      // Log for debugging

      // Fetch data directly with the search value
      if (selectedRepoId) {
        fetchGroupsData(selectedRepoId, 1, pagination.pageSize, selectedLob, searchValue);
      }

      // Update URL after fetching data
      navigate(url);
    } else {
      // For users, use field-specific search with the exact user name
      const searchValue = `name:"${memberName}"`;

      // Set search parameter
      params.set('search', searchValue);
      params.set('query', searchValue); // Set both for compatibility
      params.set('page', '1');

      // Navigate to users page with search for this user
      navigate(`/users?${params.toString()}`);

      // Log for debugging
                }
  };

  return (
    <div
      key={`${groupKey}-${memberName}-${memberIndex}${suffix}`}
      className={`${isGroup ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'} px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 cursor-pointer`}
      onClick={handleClick}
    >
      {isGroup ?
        <Users className="h-3 w-3 inline-block mr-1" /> :
        <User className="h-3 w-3 inline-block mr-1" />
      }
      {memberName}
    </div>
  );
};

export default GroupMemberItem;
