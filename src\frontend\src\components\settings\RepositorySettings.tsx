import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Loader2,
  Plus,
  Pencil,
  Trash,
  RefreshCw,
  PlayCircle,
  StopCircle,
  Copy
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableHead,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { apiClient, RepositoryConfig, RepositoryStatus, RepositoryType } from '@/api/client';
import { useRepository } from '@/context/RepositoryContext';

const RepositorySettings = () => {
  // State for repositories, statuses, and UI state
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [repositories, setRepositories] = useState<RepositoryConfig[]>([]);
  const [repoStatuses, setRepoStatuses] = useState<Record<string, RepositoryStatus>>({});
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [formState, setFormState] = useState<Partial<RepositoryConfig>>({
    type: 'gitlab',
    isActive: true,
    pollFrequency: 300,
  });

  // Use the repository context to access the refreshRepositories method
  const { setSelectedRepoId, refreshRepositories } = useRepository();

  // Form state for adding/editing repository
  // const [formState, setFormState] = useState<RepositoryConfig>({
  //   id: '',
  //   name: '',
  //   type: 'gitlab', // Default to GitLab
  //   URL: '',
  //   Token: '',
  //   ProjectID: '',
  //   PollFrequency: 300,
  //   IsActive: true,
  // });

  // Load repositories on component mount
  useEffect(() => {
    loadRepositories();
  }, []);

  const loadRepositories = async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.repositories.getConfigurations();
      setRepositories(response.configs);

      // Load statuses for each repository
      if (response.configs.length > 0) {
        const statuses = await apiClient.repositories.getAllStatuses();
        setRepoStatuses(statuses.statuses);
      }
    } catch (error) {
      toast({
        title: "Error Loading Repositories",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes in the form
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : name === 'pollFrequency' ? Number(value) : value
    }));
  };

  // Handle select changes in the form
  const handleSelectChange = (name: string, value: string) => {
    setFormState(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Function to open the dialog for adding a new repository
  const handleAddRepository = () => {
    setFormState({
      type: 'gitlab',
      isActive: true,
      pollFrequency: 300,
    });
    setIsEditing(false);
    setIsDialogOpen(true);
  };

  // Function to open the dialog for editing an existing repository
  const handleEditRepository = (repo: RepositoryConfig) => {
    // Handle case sensitivity issues between backend and frontend properties

    // Get the raw repository data without type constraints
    const rawRepo = repo as any;

    const formData: RepositoryConfig = {
      ...repo,
      // Ensure proper case for all properties by checking the raw object
      url: rawRepo.url || '',
      token: rawRepo.token || '',
      projectId: rawRepo.projectId || '',
      pollFrequency: rawRepo.pollFrequency || 300,
      isActive: typeof rawRepo.isActive !== 'undefined' ? rawRepo.isActive : true,
      bitbucketWorkspace: rawRepo.bitbucketWorkspace || '',
      bitbucketRepoSlug: rawRepo.bitbucketRepoSlug || '',
      bitbucketUsername: rawRepo.bitbucketUsername || '',
      bitbucketPassword: rawRepo.bitbucketPassword || '',
      gitlabNamespace: rawRepo.gitlabNamespace || '',
    };

        setFormState(formData);
    setIsEditing(true);
    setIsDialogOpen(true);
  };

  // Function to handle form submission (add or edit)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate the common inputs
    if (!formState.name || !formState.url) {
      toast({
        title: "Validation Error",
        description: "Repository name and URL are required",
        variant: "destructive",
      });
      return;
    }

    // Validate GitLab specific inputs
    if (formState.type === 'gitlab') {
      if (!formState.token || !formState.projectId) {
        toast({
          title: "Validation Error",
          description: "GitLab token and project ID are required",
          variant: "destructive",
        });
        return;
      }
    }

    // Validate Bitbucket specific inputs
    if (formState.type === 'bitbucket') {
      if (!formState.bitbucketWorkspace || !formState.bitbucketRepoSlug) {
        toast({
          title: "Validation Error",
          description: "Bitbucket workspace and repository slug are required",
          variant: "destructive",
        });
        return;
      }

      // Either token or username/password is required for Bitbucket
      if (!formState.token && (!formState.bitbucketUsername || !formState.bitbucketPassword)) {
        toast({
          title: "Validation Error",
          description: "Either token or username/password is required for Bitbucket authentication",
          variant: "destructive",
        });
        return;
      }
    }

    if (formState.pollFrequency !== undefined && formState.pollFrequency < 30) {
      toast({
        title: "Validation Error",
        description: "Poll frequency must be at least 30 seconds",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Ensure the form state has all required fields before submitting
      const completeFormState: RepositoryConfig = {
        id: formState.id || '',
        name: formState.name || '',
        type: formState.type || 'gitlab',
        url: formState.url || '',
        token: formState.token || '',
        projectId: formState.projectId || '',
        pollFrequency: formState.pollFrequency || 300,
        isActive: formState.isActive !== undefined ? formState.isActive : true,
        gitlabNamespace: formState.gitlabNamespace || '',
        bitbucketWorkspace: formState.bitbucketWorkspace || '',
        bitbucketRepoSlug: formState.bitbucketRepoSlug || '',
        bitbucketUsername: formState.bitbucketUsername || '',
        bitbucketPassword: formState.bitbucketPassword || '',
      };

      if (isEditing && completeFormState.id) {
        // Update existing repository
        await apiClient.repositories.updateConfiguration(
          completeFormState.id,
          completeFormState
        );
        toast({
          title: "Repository Updated",
          description: `The repository "${completeFormState.name}" has been updated successfully`,
        });
      } else {
        // Add new repository
        const response = await apiClient.repositories.addConfiguration(completeFormState);
        toast({
          title: "Repository Added",
          description: `The repository "${completeFormState.name}" has been added successfully`,
        });

        // Set this as the selected repository if it's the first one
        if (repositories.length === 0) {
          setSelectedRepoId(response.config.id!);
        }
      }

      // Close dialog and refresh repositories in context
      setIsDialogOpen(false);

      // Reload local repositories state and force refresh global repositories context
      await loadRepositories();
      await refreshRepositories(true); // Force refresh to ensure UI is updated
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to delete a repository
  const handleDeleteRepository = async (id: string) => {
    // Confirm before deletion
    if (!window.confirm("Are you sure you want to delete this repository configuration?")) {
      return;
    }

    try {
      setIsLoading(true);
      await apiClient.repositories.deleteConfiguration(id);
      toast({
        title: "Repository Deleted",
        description: "The repository configuration has been deleted successfully",
      });

      // Update both local and global repository state
      await loadRepositories();
      await refreshRepositories(true); // Force refresh to ensure UI is updated
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to sync a repository
  const handleSyncRepository = async (id: string) => {
    try {
      await apiClient.repositories.syncRepository(id);
      toast({
        title: "Sync Initiated",
        description: "Repository synchronization has been initiated",
      });

      // Refresh the repository status after a short delay
      setTimeout(async () => {
        try {
          const status = await apiClient.repositories.getRepositoryStatus(id);
          setRepoStatuses(prev => {
            const newStatuses = { ...prev };
            // Check if status has a nested status property
            if (status && 'status' in status) {
              newStatuses[id] = status.status;
            } else {
              newStatuses[id] = status as RepositoryStatus;
            }
            return newStatuses;
          });
        } catch (error) {
          console.error("Failed to refresh repository status:", error);
        }
      }, 2000);

      // Update both local and global repository state
      await loadRepositories();
      await refreshRepositories();
    } catch (error) {
      toast({
        title: "Sync Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    }
  };

  // Function to start polling for a repository
  const handleStartPolling = async (id: string) => {
    try {
      setIsLoading(true);
      await apiClient.repositories.startPolling(id);
      toast({
        title: "Polling Started",
        description: "Repository polling has been started",
      });

      // Update both local and global repository state
      await loadRepositories();
      await refreshRepositories(true); // Force refresh to ensure UI is updated
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to stop polling for a repository
  const handleStopPolling = async (id: string) => {
    try {
      setIsLoading(true);
      await apiClient.repositories.stopPolling(id);
      toast({
        title: "Polling Stopped",
        description: "Repository polling has been stopped",
      });

      // Update both local and global repository state
      await loadRepositories();
      await refreshRepositories(true); // Force refresh to ensure UI is updated
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to duplicate a repository
  const handleDuplicateRepository = async (id: string) => {
    try {
      setIsLoading(true);

      // Find the repository to duplicate in our existing data
      const repToDuplicate = repositories.find(repo => repo.id === id);
      if (!repToDuplicate) {
        throw new Error("Repository not found");
      }

      // Log for debugging

      // Get the raw repository data without type constraints
      const rawRepo = repToDuplicate as any;

      // Create a duplicate config
      const duplicateConfig: RepositoryConfig = {
        // Common fields
        id: '', // Will be generated by the server
        name: `${repToDuplicate.name} (Copy)`,
        type: repToDuplicate.type,
        url: rawRepo.url || '',
        token: rawRepo.token || '',
        projectId: rawRepo.projectId || '',
        pollFrequency: rawRepo.pollFrequency || 300,
        isActive: typeof rawRepo.isActive !== 'undefined' ? rawRepo.isActive : true,

        // Type-specific fields
        gitlabNamespace: rawRepo.gitlabNamespace || '',
        bitbucketWorkspace: rawRepo.bitbucketWorkspace || '',
        bitbucketRepoSlug: rawRepo.bitbucketRepoSlug || '',
        bitbucketUsername: rawRepo.bitbucketUsername || '',
        bitbucketPassword: rawRepo.bitbucketPassword || '',
      };

      // Log duplicate config for debugging

      await apiClient.repositories.addConfiguration(duplicateConfig);
      toast({
        title: "Repository Duplicated",
        description: `The repository "${duplicateConfig.name}" has been duplicated successfully`,
      });

      // Update both local and global repository state
      await loadRepositories();
      await refreshRepositories(true); // Force refresh to ensure UI is updated
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
      console.error("Duplication error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to render repository type badge
  const renderRepositoryTypeBadge = (type: RepositoryType) => {
    return (
      <Badge variant={type === 'gitlab' ? 'default' : 'secondary'}>
        {type === 'gitlab' ? 'GitLab' : 'Bitbucket'}
      </Badge>
    );
  };

  // Function to render repository status badge
  const renderStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? 'default' : 'outline'}>
        {isActive ? 'Active' : 'Inactive'}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Repository Settings</span>
          <Button onClick={handleAddRepository} disabled={isLoading}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Plus className="h-4 w-4 mr-2" />
            )}
            Add Repository
          </Button>
        </CardTitle>
        <CardDescription>
          Configure your GitLab or Bitbucket repositories for ADGitOps data extraction.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {repositories.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No repositories configured yet.</p>
            <Button onClick={handleAddRepository} className="mt-4" variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Repository
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Sync</TableHead>
                  <TableHead>Poll Frequency</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {repositories.map((repo) => (
                  <TableRow key={repo.id}>
                    <TableCell className="font-medium">{repo.name}</TableCell>
                    <TableCell>{renderRepositoryTypeBadge(repo.type as RepositoryType)}</TableCell>
                    <TableCell>
                      {(() => {
                        // Access raw repository data to handle case differences
                        const rawRepo = repo as any;
                        return rawRepo.URL || rawRepo.url || '';
                      })()}
                    </TableCell>
                    <TableCell>
                      {(() => {
                        // Access raw repository data to handle case differences
                        const rawRepo = repo as any;
                        const isActive =
                          typeof rawRepo.IsActive !== 'undefined' ? rawRepo.IsActive :
                          typeof rawRepo.isActive !== 'undefined' ? rawRepo.isActive :
                          false;
                        return renderStatusBadge(isActive);
                      })()}
                    </TableCell>
                    <TableCell>
                      {repoStatuses[repo.id!]?.lastSync
                        ? new Date(repoStatuses[repo.id!].lastSync).toLocaleString()
                        : 'Never'}
                    </TableCell>
                    <TableCell>
                      {(() => {
                        // Access raw repository data to handle case differences
                        const rawRepo = repo as any;
                        const pollFreq = rawRepo.PollFrequency || rawRepo.pollFrequency;
                        return pollFreq ? `${pollFreq} seconds` : 'N/A';
                      })()}
                    </TableCell>
                    <TableCell className="text-right space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleSyncRepository(repo.id!)}
                        title="Sync Repository"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                      {repo.isActive ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStopPolling(repo.id!)}
                          title="Stop Polling"
                        >
                          <StopCircle className="h-4 w-4" />
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStartPolling(repo.id!)}
                          title="Start Polling"
                        >
                          <PlayCircle className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditRepository(repo)}
                        title="Edit Repository"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDuplicateRepository(repo.id!)}
                        title="Duplicate Repository"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteRepository(repo.id!)}
                        title="Delete Repository"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {/* Add/Edit Repository Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]" onClose={() => setIsDialogOpen(false)}>
          <DialogHeader>
            <DialogTitle>{isEditing ? 'Edit Repository' : 'Add Repository'}</DialogTitle>
            <DialogDescription>
              {isEditing
                ? 'Update the repository configuration details.'
                : 'Configure a new repository for ADGitOps data extraction.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              {/* Repository Type */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right">
                  Type
                </Label>
                <div className="col-span-3">
                  <Select
                    value={formState.type}
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select repository type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gitlab">GitLab</SelectItem>
                      <SelectItem value="bitbucket">Bitbucket</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Common Fields */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formState.name || ''}
                  onChange={handleChange}
                  className="col-span-3"
                  placeholder="e.g., Production GitLab"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="URL" className="text-right">
                  URL
                </Label>
                <Input
                  id="url"
                  name="url"
                  value={formState.url || ''}
                  onChange={handleChange}
                  className="col-span-3"
                  placeholder={formState.type === 'gitlab'
                    ? "e.g., https://gitlab.company.com"
                    : "e.g., https://bitbucket.company.com"
                  }
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="Token" className="text-right">
                  Token
                </Label>
                <Input
                  id="token"
                  name="token"
                  type="password"
                  value={formState.token || ''}
                  onChange={handleChange}
                  className="col-span-3"
                  placeholder="Access token with read permissions"
                />
              </div>

              {/* GitLab-specific fields */}
              {formState.type === 'gitlab' && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="ProjectID" className="text-right">
                    Project ID
                  </Label>
                  <Input
                    id="projectId"
                    name="projectId"
                    value={formState.projectId || ''}
                    onChange={handleChange}
                    className="col-span-3"
                    placeholder="e.g., namespace/project-name"
                  />
                </div>
              )}

              {/* Bitbucket-specific fields */}
              {formState.type === 'bitbucket' && (
                <>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="BitbucketWorkspace" className="text-right">
                      Workspace
                    </Label>
                    <Input
                      id="bitbucketWorkspace"
                      name="bitbucketWorkspace"
                      value={formState.bitbucketWorkspace || ''}
                      onChange={handleChange}
                      className="col-span-3"
                      placeholder="e.g., team-name"
                    />
                  </div>

                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="BitbucketRepoSlug" className="text-right">
                      Repo Slug
                    </Label>
                    <Input
                      id="bitbucketRepoSlug"
                      name="bitbucketRepoSlug"
                      value={formState.bitbucketRepoSlug || ''}
                      onChange={handleChange}
                      className="col-span-3"
                      placeholder="e.g., project-name"
                    />
                  </div>

                  <div className="border-t pt-4 pb-2 mt-2">
                    <p className="text-sm text-muted-foreground mb-2 ml-4">
                      If not using a token, provide username and password:
                    </p>
                  </div>

                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="BitbucketUsername" className="text-right">
                      Username
                    </Label>
                    <Input
                      id="bitbucketUsername"
                      name="bitbucketUsername"
                      value={formState.bitbucketUsername || ''}
                      onChange={handleChange}
                      className="col-span-3"
                      placeholder="Bitbucket username"
                    />
                  </div>

                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="BitbucketPassword" className="text-right">
                      Password
                    </Label>
                    <Input
                      id="bitbucketPassword"
                      name="bitbucketPassword"
                      type="password"
                      value={formState.bitbucketPassword || ''}
                      onChange={handleChange}
                      className="col-span-3"
                      placeholder="Bitbucket password or app password"
                    />
                  </div>
                </>
              )}

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="PollFrequency" className="text-right">
                  Poll Frequency
                </Label>
                <div className="col-span-3 grid grid-cols-4 gap-2 items-center">
                  <Input
                    id="pollFrequency"
                    name="pollFrequency"
                    type="number"
                    min={30}
                    value={formState.pollFrequency || 300}
                    onChange={handleChange}
                    className="col-span-3"
                  />
                  <span className="text-sm text-muted-foreground">seconds</span>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="isActive" className="text-right">
                  Active
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch
                    id="isActive"
                    name="isActive"
                    checked={formState.isActive || false}
                    onCheckedChange={(checked) =>
                      setFormState(prev => ({ ...prev, isActive: checked }))
                    }
                  />
                  <Label htmlFor="isActive" className="cursor-pointer">
                    {formState.isActive ? 'Yes' : 'No'}
                  </Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? 'Save Changes' : 'Add Repository'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default RepositorySettings;
