import { useState, useEffect, useRef, useCallback } from 'react'
import { ReportExecution } from '@/api/client'
import { useToast } from '@/components/ui/use-toast'
import { useRepository } from '@/context/RepositoryContext'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Download, AlertCircle, CheckCircle, Clock } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'
import { useNavigate } from 'react-router-dom'

interface ExecutionHistoryProps {
  sharedPresetId: string
}

export default function ExecutionHistory({ sharedPresetId }: ExecutionHistoryProps) {
  const [executions, setExecutions] = useState<ReportExecution[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const { toast } = useToast()
  const navigate = useNavigate()
  const { selectedRepoId } = useRepository()

  // Use a ref to track if we've already fetched data for this preset
  const fetchedPresetIdRef = useRef<string | null>(null);

  // Define fetchExecutions as a memoized callback to prevent recreation on each render
  const fetchExecutions = useCallback(async () => {
    // Only use shared preset ID, not individual preset ID
    if (!sharedPresetId) {
            setLoading(false);
      setExecutions([]);
      return;
    }

    // Skip if we've already fetched this preset's data
    if (fetchedPresetIdRef.current === sharedPresetId) {
            return;
    }

    if (!selectedRepoId) {
      console.error('Missing repository ID');
      setLoading(false);
      setExecutions([]);
      return;
    }

    // Create a controller for aborting fetch requests
    const controller = new AbortController();
    const signal = controller.signal;

    try {
      setLoading(true);

      // Try to fetch from the API using only the shared preset ID
      try {
        const response = await fetch(
          `/api/data/repositories/${selectedRepoId}/reports/presets/${sharedPresetId}/executions`,
          { signal }
        );

        if (response.ok) {
          const data = await response.json();
          if (data && Array.isArray(data)) {
                        setExecutions(data);
            // Mark this preset as fetched
            fetchedPresetIdRef.current = sharedPresetId;
            return;
          }
        }

        // If we get here, the API call didn't return valid data
                setExecutions([]);

      } catch (error) {
        console.error('API fetch failed:', error);
        setExecutions([]);
        toast({
          title: 'Error',
          description: 'Failed to load execution history',
          variant: 'destructive',
        });
      }

      // Mark this preset as fetched regardless of outcome
      fetchedPresetIdRef.current = sharedPresetId;

    } catch (error) {
      console.error('Error in fetchExecutions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load execution history',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }

    return () => {
      controller.abort();
    };
  }, [sharedPresetId, selectedRepoId, toast]);

  // Use effect to trigger the fetch once
  useEffect(() => {
    // Only use shared preset ID
    if (sharedPresetId && selectedRepoId) {
      // Reset the fetched flag if the shared ID changes
      if (fetchedPresetIdRef.current !== sharedPresetId) {
        fetchedPresetIdRef.current = null;
      }

      fetchExecutions();
    } else if (!sharedPresetId) {
      // If no shared ID is available, set empty executions and stop loading
      setExecutions([]);
      setLoading(false);
    }

    // Cleanup function
    return () => {
      // No cleanup needed here since we moved the controller to the fetchExecutions function
    };
  }, [sharedPresetId, selectedRepoId, fetchExecutions]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  // Get status icon based on execution status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'skipped':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return null
    }
  }

  // Get status badge based on execution status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Success</Badge>
      case 'failed':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Failed</Badge>
      case 'skipped':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Skipped</Badge>
      default:
        return null
    }
  }

  // Navigate to report details
  const viewReport = (reportId: string) => {
    // Use the ID filter which is supported by the Reports page
    if (selectedRepoId) {
      // Format the query parameter with the id: prefix
      const formattedQuery = `id:${reportId}`;

      // Navigate to the reports page with the properly formatted query parameter
      navigate(`/reports?query=${encodeURIComponent(formattedQuery)}`);

      // Then use a timeout to ensure the Reports page has time to load and process the URL parameters
      setTimeout(() => {
        // Dispatch a custom event to notify the Reports page to filter by this ID
        // This ensures the search input field is properly updated and focused
        window.dispatchEvent(new CustomEvent('filter-report-by-id', {
          detail: { reportId }
        }));
      }, 100);
    }
  }

  if (loading) {
    return (
      <div className="space-y-2" data-testid="loading-skeleton">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    )
  }

  if (executions.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No execution history available for this preset.
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Status</TableHead>
            <TableHead>Version</TableHead>
            <TableHead>Executed At</TableHead>
            <TableHead>Report</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {executions.map((execution) => (
            <TableRow key={execution.id}>
              <TableCell className="flex items-center space-x-2">
                {getStatusIcon(execution.status)}
                <span>{getStatusBadge(execution.status)}</span>
              </TableCell>
              <TableCell>
                {execution.presetVersion || 'N/A'}
              </TableCell>
              <TableCell>{formatDate(execution.executedAt)}</TableCell>
              <TableCell>
                {execution.reportId ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => viewReport(execution.reportId!)}
                    className="flex items-center space-x-1"
                  >
                    <Download className="h-4 w-4" />
                    <span>View Report</span>
                  </Button>
                ) : (
                  <span className="text-muted-foreground text-sm">
                    {execution.errorMessage || 'No report generated'}
                  </span>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
