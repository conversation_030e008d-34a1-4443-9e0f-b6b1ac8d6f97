import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { PaginationInfo } from './UserTypes';

interface UserPaginationProps {
  pagination: PaginationInfo;
  loading: boolean;
  pageSizeOptions: number[];
  handlePageChange: (newPage: number) => void;
  handlePageSizeChange: (newPageSize: number) => void;
}

const UserPagination = ({
  pagination,
  loading,
  pageSizeOptions,
  handlePageChange,
  handlePageSizeChange
}: UserPaginationProps) => {
  // State for page jump input
  const [pageInputValue, setPageInputValue] = useState<number | undefined>();

  return (
    <div className="flex-none border-t border-gray-200 bg-white p-4 shadow-sm">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <span className="text-sm text-gray-500 mr-2">Rows per page:</span>
            <select
              className="border border-gray-300 rounded-md p-1 text-sm bg-white"
              value={pagination.pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            >
              {pageSizeOptions.map(size => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
          </div>

          <div className="text-sm text-gray-500">
            Showing {pagination.totalItems === 0 ? 0 : ((pagination.page - 1) * pagination.pageSize) + 1}
            &nbsp;to {Math.min(pagination.page * pagination.pageSize, pagination.totalItems)}
            &nbsp;of {pagination.totalItems} users
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex items-center mr-3">
            <span className="text-sm text-gray-500 mr-2">Go to page:</span>
            <input
              type="number"
              min={1}
              max={pagination.totalPages}
              value={pageInputValue !== undefined ? pageInputValue : pagination.page}
              onChange={(e) => setPageInputValue(Number(e.target.value))}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  if (pageInputValue && pageInputValue >= 1 && pageInputValue <= pagination.totalPages) {
                    handlePageChange(pageInputValue);
                    setPageInputValue(undefined);
                  }
                }
              }}
              onBlur={() => setPageInputValue(undefined)}
              className="border border-gray-300 rounded-md p-1 text-sm w-16"
            />
            <span className="text-sm text-gray-500 ml-1">of {pagination.totalPages}</span>
          </div>

          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(1)}
              disabled={pagination.page === 1 || loading}
              className="px-2"
            >
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1 || loading}
              className="px-2"
            >
              Previous
            </Button>

            <div className="flex items-center px-2">
              <span className="text-sm">{pagination.page}</span>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages || loading}
              className="px-2"
            >
              Next
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.totalPages)}
              disabled={pagination.page === pagination.totalPages || loading}
              className="px-2"
            >
              Last
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserPagination;
