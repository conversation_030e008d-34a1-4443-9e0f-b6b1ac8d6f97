import { Users as UsersIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { User as UserType } from './UserTypes';

interface UserListItemProps {
  user: UserType;
  userKey: string;
  expandedRows: Record<string, boolean>;
  toggleRowExpansion: (userName: string) => void;
  setSearchQuery: (query: string) => void;
}

const UserListItem = ({
  user,
  userKey,
  expandedRows,
  toggleRowExpansion,
  setSearchQuery
}: UserListItemProps) => {
  const navigate = useNavigate();

  const handleGroupClick = (group: string) => {
    // Navigate to search for this group
    const params = new URLSearchParams();

    // For groups, use field-specific search with the exact group name
    // Format: groupname:"exactGroupName"
    const searchValue = `groupname:"${group}"`;

    // Update the input value directly
    setSearchQuery(searchValue);

    // Set search parameter and page to 1
    params.set('search', searchValue);
    params.set('page', '1');

    // Navigate to groups page with search for this group
    const url = `/groups?${params.toString()}`;

    // Log for debugging

    // Navigate to the groups page
    navigate(url);
  };

  return (
    <div className="grid grid-cols-2 gap-4 py-4 px-4 border-b border-gray-200 dark:border-gray-700">
      <div className="text-sm font-medium">{user.name || "Unknown User"}</div>
      <div className="text-sm">
        {user.Lobs && user.Lobs.length > 0 ? (
          <div className="flex flex-wrap gap-1">
            {user.Lobs.map((lob, index) => (
              <div
                key={`${userKey}-lob-${index}`}
                className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 mr-1 mb-1"
              >
                {lob}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-gray-400 text-xs">No LOB</div>
        )}
      </div>

      {/* Groups row - spans all columns */}
      <div className="col-span-2">
        {Array.isArray(user.groups) && user.groups.length > 0 ? (
          <div className="flex flex-wrap gap-1 items-center">
            {/* Show first 5 groups */}
            {user.groups.slice(0, 5).map((group, groupIndex) => (
              <div
                key={`${userKey}-${group}-${groupIndex}`}
                className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 cursor-pointer"
                onClick={() => handleGroupClick(group)}
              >
                <UsersIcon className="h-3 w-3 inline-block mr-1" />
                {group}
              </div>
            ))}

            {/* Show +X more or Show less button inline with the list */}
            {user.groups.length > 5 && (
              <div
                className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded text-xs cursor-pointer inline-block h-5 leading-4 ml-1"
                onClick={() => toggleRowExpansion(user.name)}
              >
                {expandedRows[user.name] ?
                  <span>Show less</span> :
                  <span>+{user.groups.length - 5} more</span>
                }
              </div>
            )}

            {/* Show expanded groups */}
            {expandedRows[user.name] && user.groups.length > 5 && (
              <>
                {user.groups.slice(5).map((group, groupIndex) => (
                  <div
                    key={`${userKey}-${group}-${groupIndex}-expanded`}
                    className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 cursor-pointer"
                    onClick={() => handleGroupClick(group)}
                  >
                    <UsersIcon className="h-3 w-3 inline-block mr-1" />
                    {group}
                  </div>
                ))}
              </>
            )}
          </div>
        ) : (
          <div className="text-gray-400 text-xs">No groups</div>
        )}
      </div>
    </div>
  );
};

export default UserListItem;
