package search

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/blevesearch/bleve/v2"
	"github.com/blevesearch/bleve/v2/mapping"
)

// BleveSearchService is a search service that uses Bleve
type BleveSearchService struct {
	indexPath   string
	index       bleve.Index
	initialized bool
	mutex       sync.RWMutex
}

// NewBleveSearchService creates a new BleveSearchService
func NewBleveSearchService(indexPath string) (*BleveSearchService, error) {
	// Create a new search service
	service := &BleveSearchService{
		indexPath:   indexPath,
		initialized: false,
	}

	// Create the index directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(indexPath), 0755); err != nil {
		return nil, fmt.Errorf("failed to create index directory: %w", err)
	}

	// Check if the index already exists
	if _, err := os.Stat(indexPath); os.IsNotExist(err) {
		// Create a new index
		indexMapping := service.createIndexMapping()
		index, err := bleve.New(indexPath, indexMapping)
		if err != nil {
			return nil, fmt.Errorf("failed to create index: %w", err)
		}
		service.index = index
	} else {
		// Open the existing index
		index, err := bleve.Open(indexPath)
		if err != nil {
			return nil, fmt.Errorf("failed to open index: %w", err)
		}
		service.index = index
	}

	service.initialized = true
	return service, nil
}

// createIndexMapping creates a new index mapping for groups and users
func (s *BleveSearchService) createIndexMapping() mapping.IndexMapping {
	// Create a new index mapping
	indexMapping := bleve.NewIndexMapping()

	// Use standard analyzers instead of custom ones for better compatibility
	// We'll use the keyword analyzer for exact matches and the standard analyzer for text

	// Create a mapping for groups
	groupMapping := bleve.NewDocumentMapping()

	// Add field mappings for groups
	groupnameFieldMapping := bleve.NewTextFieldMapping()
	groupnameFieldMapping.Analyzer = "standard" // Use standard analyzer for better wildcard support
	groupMapping.AddFieldMappingsAt("groupname", groupnameFieldMapping)

	typeFieldMapping := bleve.NewTextFieldMapping()
	typeFieldMapping.Analyzer = "standard" // Use standard analyzer for better wildcard support
	groupMapping.AddFieldMappingsAt("type", typeFieldMapping)

	descriptionFieldMapping := bleve.NewTextFieldMapping()
	descriptionFieldMapping.Analyzer = "standard"
	groupMapping.AddFieldMappingsAt("description", descriptionFieldMapping)

	lobFieldMapping := bleve.NewTextFieldMapping()
	lobFieldMapping.Analyzer = "standard" // Use standard analyzer for better wildcard support
	groupMapping.AddFieldMappingsAt("lob", lobFieldMapping)

	// Add mapping for members
	// Use a text field mapping directly for the members array
	membersFieldMapping := bleve.NewTextFieldMapping()
	membersFieldMapping.Analyzer = "standard" // Use standard analyzer for better wildcard support
	groupMapping.AddFieldMappingsAt("members", membersFieldMapping)

	// Add mapping for repository ID
	repoIdFieldMapping := bleve.NewTextFieldMapping()
	repoIdFieldMapping.Analyzer = "keyword" // Use keyword analyzer for exact matching
	groupMapping.AddFieldMappingsAt("repoId", repoIdFieldMapping)

	// Create a mapping for users
	userMapping := bleve.NewDocumentMapping()

	// Add field mappings for users
	userNameFieldMapping := bleve.NewTextFieldMapping()
	userNameFieldMapping.Analyzer = "standard" // Use standard analyzer for better wildcard support
	userMapping.AddFieldMappingsAt("name", userNameFieldMapping)

	userGroupsFieldMapping := bleve.NewTextFieldMapping()
	userGroupsFieldMapping.Analyzer = "standard" // Use standard analyzer for better wildcard support
	userMapping.AddFieldMappingsAt("groups", userGroupsFieldMapping)

	userLobsFieldMapping := bleve.NewTextFieldMapping()
	userLobsFieldMapping.Analyzer = "standard" // Use standard analyzer for better wildcard support
	userMapping.AddFieldMappingsAt("lobs", userLobsFieldMapping)

	// Add mapping for repository ID
	userRepoIdFieldMapping := bleve.NewTextFieldMapping()
	userRepoIdFieldMapping.Analyzer = "keyword" // Use keyword analyzer for exact matching
	userMapping.AddFieldMappingsAt("repoId", userRepoIdFieldMapping)

	// Add the mappings to the index
	indexMapping.AddDocumentMapping("group", groupMapping)
	indexMapping.AddDocumentMapping("user", userMapping)

	return indexMapping
}

// Close closes the index
func (s *BleveSearchService) Close() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.initialized {
		return nil
	}

	if err := s.index.Close(); err != nil {
		return fmt.Errorf("failed to close index: %w", err)
	}

	s.initialized = false
	return nil
}
