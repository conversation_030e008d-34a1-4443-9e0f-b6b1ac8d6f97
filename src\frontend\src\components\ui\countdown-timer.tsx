import { useEffect, useRef, useState } from 'react';
import { getTimeRemaining, formatCountdown } from '@/utils/countdownUtils';
import { Clock } from 'lucide-react';

interface CountdownTimerProps {
  targetDate: string | Date;
  className?: string;
  showIcon?: boolean;
  refreshInterval?: number; // in milliseconds
}

export function CountdownTimer({
  targetDate,
  className = '',
  showIcon = true,
  refreshInterval = 1000 // default to 1 second
}: CountdownTimerProps) {
  // Use a ref to store the target date as a Date object to avoid string parsing on every render
  const targetDateRef = useRef<Date>(typeof targetDate === 'string' ? new Date(targetDate) : targetDate);

  // Force component to update by using a counter
  // We need a counter state to force re-renders
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setCounter] = useState(0);

  // Calculate time remaining based on current time
  const timeRemaining = getTimeRemaining(targetDateRef.current);
  const formattedTime = formatCountdown(timeRemaining);

  useEffect(() => {
    // Update the target date ref when the prop changes
    targetDateRef.current = typeof targetDate === 'string' ? new Date(targetDate) : targetDate;
  }, [targetDate]);

  useEffect(() => {
    // Set up interval for countdown
    const interval = setInterval(() => {
      // Increment counter to force re-render
      setCounter(prev => prev + 1);
    }, refreshInterval);

    // Clean up interval on unmount
    return () => clearInterval(interval);
  }, [refreshInterval]);

  return (
    <span className={`inline-flex items-center ${className}`}>
      {showIcon && <Clock className="h-3 w-3 mr-1" />}
      <span>{formattedTime}</span>
    </span>
  );
}
