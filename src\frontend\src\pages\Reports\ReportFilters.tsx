import React from 'react';
import { FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import ReportSearchHelp from './ReportSearchHelp';
import ReportSearchSuggestions from './ReportSearchSuggestions';
import { Report } from '@/api/client';

interface ReportFiltersProps {
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  searchInputRef: React.RefObject<HTMLInputElement>;
  reports?: Report[];
  handleSearch: (value: string) => void;
}

const ReportFilters = ({
  searchQuery,
  setSearchQuery,
  searchInputRef,
  reports = [],
  handleSearch,
}: ReportFiltersProps) => {
  const navigate = useNavigate();

  // <PERSON>le creating a report preset from the current query
  const handleCreateReportPreset = () => {
    // Navigate to the report presets page with query parameters
    navigate(`/report-presets?createFromQuery=${encodeURIComponent(searchQuery)}&reportType=both`);
  };

  return (
    <div className="flex items-center w-full">
      <div className="flex-1">
        <ReportSearchSuggestions
          inputValue={searchQuery}
          setInputValue={setSearchQuery}
          onSearch={handleSearch}
          placeholder="Search reports..."
          searchInputRef={searchInputRef}
          reports={reports}
        />
      </div>

      <div className="flex items-center space-x-3 ml-3 flex-shrink-0">
        <ReportSearchHelp />

        <Button
          onClick={handleCreateReportPreset}
          disabled={!searchQuery.trim()}
          variant="outline"
          size="sm"
          title="Create a report preset from this query"
        >
          <FileText className="h-4 w-4 mr-2" /> Create Preset
        </Button>
      </div>
    </div>
  );
};

export default ReportFilters;
