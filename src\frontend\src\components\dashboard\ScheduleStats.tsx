import { useState, useEffect } from 'react';
import { RefreshCw, Calendar, Clock, FileCheck } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { apiClient, ReportPreset, ReportExecution } from '@/api/client';
import { useRepository } from '@/context/RepositoryContext';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';

interface ScheduleStatsCardProps {
  repoId?: string; // Optional: If provided, will override the context repository
}

interface ScheduleStats {
  totalSchedules: number;
  scheduledForToday: number;
  reportsGeneratedToday: number;
  lastUpdated: string;
}

const ScheduleStatsCard = ({ repoId }: ScheduleStatsCardProps = {}) => {
  const [stats, setStats] = useState<ScheduleStats | null>(null);
  const [loading, setLoading] = useState(true);
  const { selectedRepoId, isLoading: contextLoading } = useRepository();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Use provided repoId or fall back to selected repo from context
  const currentRepoId = repoId || selectedRepoId;

  // Function to check if a preset is scheduled for today
  const isScheduledForToday = (preset: ReportPreset): boolean => {
    if (!preset.schedule || !preset.schedule.enabled) {
      return false;
    }

    const now = new Date();
    const today = now.getDay(); // 0-6, Sunday-Saturday
    const currentDate = now.getDate(); // 1-31
    
    switch (preset.schedule.frequency) {
      case 'daily':
        return true;
      case 'weekly':
        return preset.schedule.dayOfWeek === today;
      case 'monthly':
        return preset.schedule.dayOfMonth === currentDate;
      case 'interval':
        // For interval schedules, check if there's a next run time for today
        if (preset.schedule.nextRun) {
          const nextRun = new Date(preset.schedule.nextRun);
          return nextRun.toDateString() === now.toDateString();
        }
        return false;
      default:
        return false;
    }
  };

  // Function to check if a report execution happened today
  const isExecutedToday = (execution: ReportExecution): boolean => {
    if (!execution.executedAt) {
      return false;
    }

    const executionDate = new Date(execution.executedAt);
    const today = new Date();
    
    return (
      executionDate.getDate() === today.getDate() &&
      executionDate.getMonth() === today.getMonth() &&
      executionDate.getFullYear() === today.getFullYear()
    );
  };

  // Function to fetch schedule statistics
  const fetchStats = async () => {
    if (!currentRepoId) {
      setStats(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      // Fetch all report presets
      const presets = await apiClient.data.getReportPresets(currentRepoId);
      
      // Count total schedules (presets with scheduling enabled)
      const scheduledPresets = presets.filter(preset => 
        preset.isActive && preset.schedule && preset.schedule.enabled
      );
      const totalSchedules = scheduledPresets.length;
      
      // Count presets scheduled for today
      const scheduledForToday = scheduledPresets.filter(preset => 
        isScheduledForToday(preset)
      ).length;
      
      // Get all executions for all presets to count reports generated today
      let allExecutions: ReportExecution[] = [];
      
      // For each preset with a shared ID, fetch its executions
      const uniqueSharedIds = new Set(presets.map(preset => preset.sharedId).filter(Boolean));
      
      for (const sharedId of uniqueSharedIds) {
        if (!sharedId) continue;
        
        try {
          // Use the first preset with this shared ID to fetch executions
          const executions = await apiClient.getReportExecutionsByPreset(sharedId, currentRepoId);
          allExecutions = [...allExecutions, ...executions];
        } catch (error) {
          console.error(`Error fetching executions for preset ${sharedId}:`, error);
        }
      }
      
      // Count executions that happened today and were successful
      const reportsGeneratedToday = allExecutions.filter(execution => 
        execution.status === 'success' && isExecutedToday(execution)
      ).length;
      
      // Set the stats
      setStats({
        totalSchedules,
        scheduledForToday,
        reportsGeneratedToday,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      toast({
        title: "Failed to load schedule statistics",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
      console.error("Error fetching schedule statistics:", error);

      // Set default stats for development/testing
      if (process.env.NODE_ENV === 'development') {
        setStats({
          totalSchedules: 0,
          scheduledForToday: 0,
          reportsGeneratedToday: 0,
          lastUpdated: new Date().toISOString()
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch stats when the repository changes or on manual refresh
  useEffect(() => {
    if (!contextLoading && currentRepoId) {
      fetchStats();
    }
  }, [currentRepoId, contextLoading]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (e) {
      return 'Unknown';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Calendar className="h-6 w-6 text-purple-500" />
            <span>Schedule Statistics</span>
          </div>
          <Button
            size="icon"
            variant="ghost"
            onClick={fetchStats}
            disabled={loading}
            title="Refresh Statistics"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
        <div className="text-sm text-muted-foreground mt-1.5">
          Overview of scheduled reports
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ) : stats ? (
          <div className="grid grid-cols-1 gap-4">
            <div 
              className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
              onClick={() => navigate('/report-presets')}
            >
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-purple-500 mr-2" />
                <span>Total Schedules</span>
              </div>
              <div className="flex items-center">
                <Badge variant="outline" className="text-lg font-semibold mr-1">
                  {stats.totalSchedules}
                </Badge>
              </div>
            </div>
            
            <div 
              className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
              onClick={() => navigate('/report-presets')}
            >
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-blue-500 mr-2" />
                <span>Scheduled For Today</span>
              </div>
              <div className="flex items-center">
                <Badge variant="outline" className="text-lg font-semibold mr-1">
                  {stats.scheduledForToday}
                </Badge>
              </div>
            </div>
            
            <div 
              className="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer transition-colors group"
              onClick={() => navigate('/reports')}
            >
              <div className="flex items-center">
                <FileCheck className="h-5 w-5 text-green-500 mr-2" />
                <span>Generated Today</span>
              </div>
              <div className="flex items-center">
                <Badge variant="outline" className="text-lg font-semibold mr-1">
                  {stats.reportsGeneratedToday}
                </Badge>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            <p>No schedule statistics available</p>
          </div>
        )}
      </CardContent>
      {stats && (
        <CardFooter className="text-xs text-muted-foreground">
          Last updated: {formatDate(stats.lastUpdated)}
        </CardFooter>
      )}
    </Card>
  );
};

export default ScheduleStatsCard;
