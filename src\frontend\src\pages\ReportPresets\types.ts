// Import types from API client
import '@/api/client'

// Extended ReportPreset type with additional properties
export interface ReportPreset {
  id?: string
  name?: string
  description?: string
  version?: number
  parentId?: string
  sharedId?: string // Shared ID for all versions of the same preset
  isActive?: boolean
  createdAt?: string
  updatedAt?: string
  repositoryId?: string
  reportType?: 'users' | 'groups' | 'both'
  searchQuery?: string
  schedule?: {
    enabled: boolean
    frequency: 'interval' | 'daily' | 'weekly' | 'monthly'
    hour: number
    minute: number
    dayOfWeek?: number
    dayOfMonth?: number
    intervalHours?: number
    intervalMinutes?: number
  }
  flattenMembership?: boolean
  groupColumns?: string[]
  userColumns?: string[]
  lob?: string
  types?: string
  groupIds?: string
  userIds?: string
  hasReports?: boolean
}

export interface PresetFormState {
  name: string
  description: string
  reportType: 'users' | 'groups' | 'both'
  searchQuery: string
  flattenMembership: boolean
  schedule: {
    enabled: boolean
    frequency: 'interval' | 'daily' | 'weekly' | 'monthly'
    hour: number
    minute: number
    dayOfWeek?: number
    dayOfMonth?: number
    intervalHours?: number
    intervalMinutes?: number
  }
  groupColumns: string[]
  userColumns: string[]
  // Legacy form fields
  lob: string
  types: string
  groupIds: string
  userIds: string
}

export interface PresetCardProps {
  preset: ReportPreset
  onEdit: (preset: ReportPreset) => void
  onView: (preset: ReportPreset) => void
  onGenerate: (preset: ReportPreset) => void
  onOpenHistory: (preset: ReportPreset) => void
}

export interface CreatePresetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (formData: PresetFormState) => void
  initialValues?: Partial<PresetFormState>
  groupsData: any[]
  availableLobs: string[]
  availableGroupTypes: string[]
  availableGroups: string[]
  availableUsers: string[]
}

export interface EditPresetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (formData: PresetFormState) => void
  preset: ReportPreset | null
  groupsData: any[]
  availableLobs: string[]
  availableGroupTypes: string[]
  availableGroups: string[]
  availableUsers: string[]
}

export interface ViewPresetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  preset: ReportPreset | null
  availableVersions: number[]
  onNavigateToVersion: (version: number) => void
  versionInputValue: string
  onVersionInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onVersionInputSubmit: () => void
  versionNavigating: boolean
}

export interface DeletePresetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
}

export interface PresetsListProps {
  presets: ReportPreset[] | null
  loading: boolean
  error: string | null
  onEdit: (preset: ReportPreset) => void
  onView: (preset: ReportPreset) => void
  onGenerate: (preset: ReportPreset) => void
  onOpenHistory: (preset: ReportPreset) => void
}

export interface ExecutionHistoryDialogProps {
  presetId: string
  presetName: string
  open: boolean
  onOpenChange: (open: boolean) => void
}
