import React from 'react';
import { Info } from 'lucide-react';
import { Group, PaginationInfo } from './GroupTypes';
import GroupMembershipInfo from './GroupMembershipInfo';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface GroupListItemProps {
  group: Group;
  groupKey: string;
  expandedRows: Record<string, boolean>;
  toggleRowExpansion: (groupName: string) => void;
  selectedRepoId: string | null;
  selectedLob: string;
  pagination: PaginationInfo;
  setInputValue: (value: string) => void;
  fetchGroupsData: (repoId: string, page: number, pageSize: number, lob?: string, search?: string) => void;
}

const GroupListItem = ({
  group,
  groupKey,
  expandedRows,
  toggleRowExpansion,
  selectedRepoId,
  selectedLob,
  pagination,
  setInputValue,
  fetchGroupsData
}: GroupListItemProps) => {

  // Handle navigation to another group
  const handleGroupClick = (groupName: string) => {
    if (selectedRepoId) {
      setInputValue(`groupname:${groupName}`);
      fetchGroupsData(selectedRepoId, 1, pagination.pageSize, selectedLob, `groupname:${groupName}`);
    }
  };
  // Normalize members to array of objects with name and type
  const normalizeMembers = () => {
    if (!group.Members) return [];

    if (Array.isArray(group.Members)) {
      return group.Members.map(member => {
        if (typeof member === 'string') {
          return { name: member, type: 'user' };
        }
        return member;
      });
    } else if (typeof group.Members === 'object') {
      return Object.entries(group.Members).map(([name, type]) => ({
        name,
        type: typeof type === 'string' ? type : 'user'
      }));
    }
    return [];
  };

  const members = normalizeMembers();

  // Get LOB value from any of the possible fields
  const getLobValue = () => {
    if (group.LOB) return group.LOB;
    if (group.Lob) return group.Lob;
    if (group.lob) return group.lob;
    if (group.Lobs && Array.isArray(group.Lobs) && group.Lobs.length > 0) {
      return group.Lobs.join(', ');
    }
    return '';
  };

  const lobValue = getLobValue();

  return (
    <div className="grid grid-cols-4 gap-4 py-4 px-4 border-b border-gray-200 dark:border-gray-700">
      <div className="text-sm font-medium">{group.Groupname}</div>
      <div className="text-sm">
        {group.Type || <span className="text-gray-400 text-xs">No type</span>}
      </div>
      <div className="text-sm">
        {lobValue ? (
          <div className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs inline-block">
            {lobValue}
          </div>
        ) : (
          <span className="text-gray-400 text-xs">No LOB</span>
        )}
      </div>
      <div className="text-sm">
        {group.Description || group.SourceFile ? (
          <div className="flex flex-col gap-1">
            {group.Description && (
              <div className="flex items-center">
                <Popover>
                  <PopoverTrigger>
                    <Info className="h-4 w-4 text-gray-400 cursor-pointer" />
                  </PopoverTrigger>
                  <PopoverContent side="top">
                    <div className="p-2 max-w-md">
                      <p className="text-sm">
                          <span className="font-medium">Description</span>
                        <div className="mt-2 pt-2 border-t border-gray-200">
                          {group.Description}
                        </div>
                      </p>
                    </div>
                  </PopoverContent>
                </Popover>
                <span className="ml-2 text-xs text-gray-500 truncate max-w-[200px]">
                  {group.Description}
                </span>
              </div>
            )}
            {group.SourceFile && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Source File:</span>
                <span className="ml-1 bg-blue-50 text-blue-700 px-1.5 py-0.5 rounded text-xs">
                  {group.SourceFile}
                </span>
              </div>
            )}
          </div>
        ) : (
          <span className="text-gray-400 text-xs">No description</span>
        )}
      </div>
      <div className="col-span-4">
        {/* Enhanced Membership View - Always Displayed */}
        {selectedRepoId && (
          <GroupMembershipInfo
            group={group}
            repoId={selectedRepoId}
            onGroupClick={handleGroupClick}
          />
        )}


      </div>
    </div>
  );
};

export default GroupListItem;
