import React, { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { HelpCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import SearchInputWithSuggestions from '@/components/SearchInputWithSuggestions'
import ScheduleConfigComponent from './ScheduleConfig'
import { usePresetForm } from './hooks/usePresetForm'
import { CreatePresetDialogProps } from './types'

export function CreatePresetDialog({
  open,
  onOpenChange,
  onSubmit,
  initialValues,
  // groupsData is not used directly but is passed to the component for future use
  // groupsData, // Unused but may be needed in the future
  availableLobs,
  availableGroupTypes,
  availableGroups,
  availableUsers,
}: CreatePresetDialogProps) {
  const location = useLocation()
  const {
    formName,
    setFormName,
    formDescription,
    setFormDescription,
    formReportType,
    setFormReportType,
    formSearchQuery,
    setFormSearchQuery,
    formFlattenMembership,
    setFormFlattenMembership,
    formSchedule,
    setFormSchedule,
    formGroupColumns,
    setFormGroupColumns,
    formUserColumns,
    setFormUserColumns,
    // Legacy form state variables - not used but kept for reference
    // formLob,
    setFormLob,
    // formTypes,
    setFormTypes,
    // formGroupIds,
    setFormGroupIds,
    // formUserIds,
    setFormUserIds,
    resetForm,
    getFormData,
  } = usePresetForm()

  // Initialize form with initial values if provided
  useEffect(() => {
    if (initialValues) {
      if (initialValues.name) setFormName(initialValues.name)
      if (initialValues.description) setFormDescription(initialValues.description)
      if (initialValues.reportType) setFormReportType(initialValues.reportType)
      if (initialValues.searchQuery) setFormSearchQuery(initialValues.searchQuery)
      if (initialValues.flattenMembership !== undefined) setFormFlattenMembership(initialValues.flattenMembership)
      if (initialValues.schedule) setFormSchedule(initialValues.schedule)
      if (initialValues.groupColumns) setFormGroupColumns(initialValues.groupColumns)
      if (initialValues.userColumns) setFormUserColumns(initialValues.userColumns)
      if (initialValues.lob) setFormLob(initialValues.lob)
      if (initialValues.types) setFormTypes(initialValues.types)
      if (initialValues.groupIds) setFormGroupIds(initialValues.groupIds)
      if (initialValues.userIds) setFormUserIds(initialValues.userIds)
    }
  }, [initialValues, setFormName, setFormDescription, setFormReportType, setFormSearchQuery, setFormFlattenMembership, setFormSchedule, setFormGroupColumns, setFormUserColumns, setFormLob, setFormTypes, setFormGroupIds, setFormUserIds])

  // Reset form when dialog is closed
  useEffect(() => {
    if (!open) {
      resetForm()
    }
  }, [open, resetForm])

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(getFormData())
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Report Preset</DialogTitle>
          <DialogDescription>
            Configure a new report preset. This preset can be used to quickly generate reports.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-3">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">Name <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  placeholder="Monthly User Report"
                  value={formName}
                  onChange={(e) => setFormName(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reportType">Report Type <span className="text-red-500">*</span></Label>
                <Select value={formReportType} onValueChange={(value: 'users' | 'groups' | 'both') => setFormReportType(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select report type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="users">Users Only</SelectItem>
                    <SelectItem value="groups">Groups Only</SelectItem>
                    <SelectItem value="both">Users & Groups</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                placeholder="This report contains all users in the Marketing LOB"
                value={formDescription}
                onChange={(e) => setFormDescription(e.target.value)}
                rows={2}
              />
            </div>

            <Separator className="my-2" />

            <Tabs defaultValue={formSearchQuery ? "search" : "search"} className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="search">Search Query</TabsTrigger>
                <TabsTrigger value="columns">Columns</TabsTrigger>
                <TabsTrigger value="schedule">Schedule</TabsTrigger>
              </TabsList>

              <TabsContent value="search" className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Label htmlFor="searchQuery">Search Query</Label>
                      {location.search.includes('createFromQuery') && (
                        <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                          Pre-filled from search
                        </span>
                      )}
                    </div>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <HelpCircle className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80">
                        <div className="space-y-2">
                          <h4 className="font-medium">Search Syntax Help</h4>
                          <p className="text-sm">Use the same syntax as in the Groups and Users pages:</p>
                          <div className="text-sm space-y-3">
                            <div>
                              <h5 className="font-medium">Basic Filters</h5>
                              <ul className="space-y-1">
                                <li><code>lob:finance</code> - Filter by LOB</li>
                                <li><code>type:security</code> - Filter by group type</li>
                                <li><code>group:engineering</code> - Filter by group name</li>
                                <li><code>user:jsmith</code> - Filter by username</li>
                              </ul>
                            </div>

                            <div>
                              <h5 className="font-medium">Advanced Syntax</h5>
                              <ul className="space-y-1">
                                <li><code>"exact match"</code> - Use quotes for exact matches</li>
                                <li><code>lob:finance AND type:security</code> - Require both conditions</li>
                                <li><code>group:dev OR group:test</code> - Match either condition</li>
                                <li><code>NOT user:admin</code> - Exclude matches</li>
                                <li><code>(type:team AND lob:finance) OR type:security</code> - Group conditions</li>
                              </ul>
                            </div>

                            <div>
                              <h5 className="font-medium">Tips</h5>
                              <ul className="space-y-1">
                                <li>Type to see suggestions for filters and operators</li>
                                <li>Operators (AND, OR, NOT) should be uppercase</li>
                                <li>Use the autocomplete to build complex queries easily</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <SearchInputWithSuggestions
                    value={formSearchQuery}
                    onChange={setFormSearchQuery}
                    onSearch={() => {}} // Add empty onSearch function to satisfy the type
                    initialValue={formSearchQuery} // Add initialValue prop to ensure the search query is displayed
                    placeholder="Example: lob:finance type:security"
                    className={`w-full ${location.search.includes('createFromQuery') ? 'border-green-500 ring-1 ring-green-500' : ''}`}
                    autoFocus={false}
                    suggestions={[
                      // Filter type suggestions
                      { type: 'lob', value: '', display: 'lob:' },
                      { type: 'type', value: '', display: 'type:' },
                      { type: 'group', value: '', display: 'group:' },
                      { type: 'user', value: '', display: 'user:' },

                      // LOB suggestions
                      ...availableLobs.map(lob => ({
                        type: 'lob',
                        value: lob,
                        display: lob
                      })),
                      // Group type suggestions
                      ...availableGroupTypes.map(type => ({
                        type: 'type',
                        value: type,
                        display: type
                      })),
                      // Group suggestions
                      ...availableGroups.map(group => ({
                        type: 'group',
                        value: group,
                        display: group
                      })),
                      // User suggestions
                      ...availableUsers.map(user => ({
                        type: 'user',
                        value: user,
                        display: user
                      }))
                    ]}
                  />
                  <p className="text-xs text-muted-foreground">
                    This query uses the same syntax as the search bar on the Groups and Users pages.
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="schedule" className="space-y-4">
                <ScheduleConfigComponent
                  schedule={formSchedule}
                  onChange={setFormSchedule}
                />
              </TabsContent>

              <TabsContent value="columns" className="space-y-4">
                <div className="space-y-4">
                  {formReportType !== 'users' && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>Group Columns</Label>
                        <div className="grid grid-cols-2 gap-2">
                          {['groupname', 'type', 'lob', 'description', 'members'].map(column => (
                            <div key={column} className="flex items-center justify-between space-x-2 border p-2 rounded">
                              <Label htmlFor={`group-column-${column}`} className="capitalize">{column}</Label>
                              <Switch
                                id={`group-column-${column}`}
                                checked={formGroupColumns.includes(column)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setFormGroupColumns([...formGroupColumns, column])
                                  } else {
                                    setFormGroupColumns(formGroupColumns.filter(c => c !== column))
                                  }
                                }}
                              />
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between space-x-2 border p-2 rounded">
                          <div>
                            <Label htmlFor="flatten-membership" className="font-medium">Flatten Group Membership</Label>
                            <p className="text-xs text-muted-foreground">Recursively extract all users from nested groups</p>
                          </div>
                          <Switch
                            id="flatten-membership"
                            checked={formFlattenMembership}
                            onCheckedChange={setFormFlattenMembership}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {formReportType !== 'groups' && (
                    <div className="space-y-2">
                      <Label>User Columns</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {['username', 'groups', 'lobs'].map(column => (
                          <div key={column} className="flex items-center justify-between space-x-2 border p-2 rounded">
                            <Label htmlFor={`user-column-${column}`} className="capitalize">{column}</Label>
                            <Switch
                              id={`user-column-${column}`}
                              checked={formUserColumns.includes(column)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setFormUserColumns([...formUserColumns, column])
                                } else {
                                  setFormUserColumns(formUserColumns.filter(c => c !== column))
                                }
                              }}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>


            </Tabs>
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">
              Create Preset
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
